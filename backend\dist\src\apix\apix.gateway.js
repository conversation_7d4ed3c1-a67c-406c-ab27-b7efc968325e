"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var APIMXGateway_1;
var _a, _b;
Object.defineProperty(exports, "__esModule", { value: true });
exports.APIMXGateway = void 0;
const websockets_1 = require("@nestjs/websockets");
const socket_io_1 = require("socket.io");
const common_1 = require("@nestjs/common");
const jwt_1 = require("@nestjs/jwt");
let APIMXGateway = APIMXGateway_1 = class APIMXGateway {
    constructor(jwtService) {
        this.jwtService = jwtService;
        this.logger = new common_1.Logger(APIMXGateway_1.name);
        this.connectedClients = new Map();
        this.userSockets = new Map();
        this.organizationSockets = new Map();
        this.sessionSockets = new Map();
    }
    async handleConnection(client) {
        try {
            const token = client.handshake.auth.token || client.handshake.headers.authorization?.replace('Bearer ', '');
            if (!token) {
                client.disconnect();
                return;
            }
            const payload = this.jwtService.verify(token);
            client.userId = payload.sub;
            client.organizationId = payload.organizationId;
            client.userRoles = payload.roles;
            this.connectedClients.set(client.id, client);
            if (!this.userSockets.has(client.userId)) {
                this.userSockets.set(client.userId, new Set());
            }
            this.userSockets.get(client.userId).add(client.id);
            if (!this.organizationSockets.has(client.organizationId)) {
                this.organizationSockets.set(client.organizationId, new Set());
            }
            this.organizationSockets.get(client.organizationId).add(client.id);
            await client.join(`org:${client.organizationId}`);
            await client.join(`user:${client.userId}`);
            this.logger.log(`Client connected: ${client.id} (User: ${client.userId}, Org: ${client.organizationId})`);
            client.emit('connected', {
                clientId: client.id,
                userId: client.userId,
                organizationId: client.organizationId,
                timestamp: new Date(),
            });
        }
        catch (error) {
            this.logger.error(`Authentication failed for client ${client.id}:`, error);
            client.disconnect();
        }
    }
    async handleDisconnect(client) {
        this.connectedClients.delete(client.id);
        if (client.userId && this.userSockets.has(client.userId)) {
            this.userSockets.get(client.userId).delete(client.id);
            if (this.userSockets.get(client.userId).size === 0) {
                this.userSockets.delete(client.userId);
            }
        }
        if (client.organizationId && this.organizationSockets.has(client.organizationId)) {
            this.organizationSockets.get(client.organizationId).delete(client.id);
            if (this.organizationSockets.get(client.organizationId).size === 0) {
                this.organizationSockets.delete(client.organizationId);
            }
        }
        this.logger.log(`Client disconnected: ${client.id}`);
    }
    async handleJoinSession(client, data) {
        const { sessionId } = data;
        if (!this.sessionSockets.has(sessionId)) {
            this.sessionSockets.set(sessionId, new Set());
        }
        this.sessionSockets.get(sessionId).add(client.id);
        await client.join(`session:${sessionId}`);
        client.emit('session_joined', { sessionId, timestamp: new Date() });
        this.logger.log(`Client ${client.id} joined session: ${sessionId}`);
    }
    async handleLeaveSession(client, data) {
        const { sessionId } = data;
        if (this.sessionSockets.has(sessionId)) {
            this.sessionSockets.get(sessionId).delete(client.id);
            if (this.sessionSockets.get(sessionId).size === 0) {
                this.sessionSockets.delete(sessionId);
            }
        }
        await client.leave(`session:${sessionId}`);
        client.emit('session_left', { sessionId, timestamp: new Date() });
        this.logger.log(`Client ${client.id} left session: ${sessionId}`);
    }
    handlePing(client) {
        client.emit('pong', { timestamp: new Date() });
    }
    broadcastToOrganization(organizationId, event) {
        this.server.to(`org:${organizationId}`).emit('apix_event', event);
        this.logger.debug(`Broadcasted ${event.type} to organization: ${organizationId}`);
    }
    broadcastToUser(userId, event) {
        this.server.to(`user:${userId}`).emit('apix_event', event);
        this.logger.debug(`Broadcasted ${event.type} to user: ${userId}`);
    }
    broadcastToSession(sessionId, event) {
        this.server.to(`session:${sessionId}`).emit('apix_event', event);
        this.logger.debug(`Broadcasted ${event.type} to session: ${sessionId}`);
    }
    broadcastToAll(event) {
        this.server.emit('apix_event', event);
        this.logger.debug(`Broadcasted ${event.type} to all clients`);
    }
    getConnectedClientsCount() {
        return this.connectedClients.size;
    }
    getOrganizationClientsCount(organizationId) {
        return this.organizationSockets.get(organizationId)?.size || 0;
    }
    getUserClientsCount(userId) {
        return this.userSockets.get(userId)?.size || 0;
    }
    isUserOnline(userId) {
        return this.userSockets.has(userId) && this.userSockets.get(userId).size > 0;
    }
};
exports.APIMXGateway = APIMXGateway;
__decorate([
    (0, websockets_1.WebSocketServer)(),
    __metadata("design:type", typeof (_b = typeof socket_io_1.Server !== "undefined" && socket_io_1.Server) === "function" ? _b : Object)
], APIMXGateway.prototype, "server", void 0);
__decorate([
    (0, websockets_1.SubscribeMessage)('join_session'),
    __param(0, (0, websockets_1.ConnectedSocket)()),
    __param(1, (0, websockets_1.MessageBody)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], APIMXGateway.prototype, "handleJoinSession", null);
__decorate([
    (0, websockets_1.SubscribeMessage)('leave_session'),
    __param(0, (0, websockets_1.ConnectedSocket)()),
    __param(1, (0, websockets_1.MessageBody)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], APIMXGateway.prototype, "handleLeaveSession", null);
__decorate([
    (0, websockets_1.SubscribeMessage)('ping'),
    __param(0, (0, websockets_1.ConnectedSocket)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", void 0)
], APIMXGateway.prototype, "handlePing", null);
exports.APIMXGateway = APIMXGateway = APIMXGateway_1 = __decorate([
    (0, common_1.Injectable)(),
    (0, websockets_1.WebSocketGateway)({
        cors: {
            origin: process.env.FRONTEND_URL || 'http://localhost:3000',
            credentials: true,
        },
        namespace: '/apix',
    }),
    __metadata("design:paramtypes", [typeof (_a = typeof jwt_1.JwtService !== "undefined" && jwt_1.JwtService) === "function" ? _a : Object])
], APIMXGateway);
//# sourceMappingURL=apix.gateway.js.map