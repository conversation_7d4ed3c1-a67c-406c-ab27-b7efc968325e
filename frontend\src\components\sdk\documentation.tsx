'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { 
  Book, 
  Code, 
  Copy, 
  ExternalLink, 
  CheckCircle,
  MessageSquare,
  Cog,
  Zap,
  Users,
  FileText,
  Video,
  Download,
  Search,
  Star,
  Clock
} from 'lucide-react';

export function SDKDocumentation() {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('getting-started');
  const [copiedCode, setCopiedCode] = useState<string | null>(null);

  const categories = [
    {
      id: 'getting-started',
      name: 'Getting Started',
      icon: <Book className="w-4 h-4" />,
      description: 'Quick start guides and basic concepts'
    },
    {
      id: 'agents',
      name: 'AI Agents',
      icon: <MessageSquare className="w-4 h-4" />,
      description: 'Create and manage AI agents'
    },
    {
      id: 'tools',
      name: 'Tools & APIs',
      icon: <Cog className="w-4 h-4" />,
      description: 'Integrate external tools and APIs'
    },
    {
      id: 'workflows',
      name: 'Workflows',
      icon: <Zap className="w-4 h-4" />,
      description: 'Build complex automation workflows'
    },
    {
      id: 'examples',
      name: 'Examples',
      icon: <Code className="w-4 h-4" />,
      description: 'Real-world implementation examples'
    }
  ];

  const gettingStartedGuides = [
    {
      title: 'Installation & Setup',
      description: 'Get the SDK running in 5 minutes',
      type: 'Step-by-step',
      duration: '5 min',
      difficulty: 'Beginner',
      content: `
# Install SynapseAI SDK

\`\`\`bash
npm install @synapseai/sdk
\`\`\`

# Initialize the client

\`\`\`javascript
import { SynapseAIClient } from '@synapseai/sdk';

const client = new SynapseAIClient({
  apiKey: 'your-api-key',
  organizationId: 'your-org-id'
});
\`\`\`

# Test the connection

\`\`\`javascript
const isConnected = await client.testConnection();
console.log('Connected:', isConnected);
\`\`\`
      `
    },
    {
      title: 'Your First Agent',
      description: 'Create and execute your first AI agent',
      type: 'Tutorial',
      duration: '10 min',
      difficulty: 'Beginner',
      content: `
# Create an Agent

\`\`\`javascript
const agent = await client.agents.create({
  name: 'Customer Support Agent',
  description: 'Helps customers with common questions',
  instructions: 'You are a helpful customer support agent...',
  config: {
    temperature: 0.7,
    maxTokens: 1000
  }
});
\`\`\`

# Execute the Agent

\`\`\`javascript
const response = await client.agents.execute(agent.id, {
  input: 'Hello, I need help with my order',
  stream: false
});

console.log(response.output);
\`\`\`
      `
    },
    {
      title: 'Understanding Sessions',
      description: 'Learn how conversation memory works',
      type: 'Concept',
      duration: '8 min',
      difficulty: 'Beginner',
      content: `
# Sessions provide memory for conversations

\`\`\`javascript
// Create a session
const session = await client.sessions.create({
  type: 'agent',
  targetId: agent.id
});

// Use the session for multiple interactions
const response1 = await client.agents.execute(agent.id, {
  input: 'My name is John',
  sessionId: session.id
});

const response2 = await client.agents.execute(agent.id, {
  input: 'What is my name?',
  sessionId: session.id
});
// Response: "Your name is John"
\`\`\`
      `
    }
  ];

  const codeExamples = {
    'create-agent': `// Create a customer service agent
const agent = await client.agents.create({
  name: 'Customer Service Bot',
  description: 'Handles customer inquiries',
  instructions: 'You are a friendly customer service agent. Help customers with their questions and escalate complex issues.',
  config: {
    temperature: 0.7,
    maxTokens: 1000,
    model: 'gpt-4'
  }
});`,
    
    'streaming-response': `// Get streaming responses from an agent
const executionId = await client.agents.executeStreaming(agent.id, {
  input: 'Tell me about your products',
  sessionId: session.id
}, {
  onChunk: (chunk) => {
    console.log('Received:', chunk.content);
  },
  onComplete: (result) => {
    console.log('Final result:', result);
  },
  onError: (error) => {
    console.error('Error:', error);
  }
});`,
    
    'workflow-execution': `// Execute a complex workflow
const execution = await client.workflows.execute(workflowId, {
  input: {
    customerEmail: '<EMAIL>',
    orderData: { id: '12345', amount: 99.99 }
  }
});

// Monitor progress in real-time
client.workflows.subscribeToExecution(execution.id, (event) => {
  console.log(\`Step \${event.stepName} completed\`);
});`,
    
    'tool-integration': `// Create and use an API tool
const tool = await client.tools.create({
  name: 'Weather API',
  type: 'api',
  config: {
    endpoint: 'https://api.weather.com/forecast',
    method: 'GET',
    authentication: {
      type: 'api_key',
      credentials: { key: 'your-api-key' }
    }
  },
  schema: {
    input: {
      location: { type: 'string', required: true }
    },
    output: {
      temperature: 'number',
      conditions: 'string'
    }
  }
});

// Execute the tool
const result = await client.tools.execute(tool.id, {
  input: { location: 'New York' }
});`
  };

  const copyToClipboard = (code: string, id: string) => {
    navigator.clipboard.writeText(code);
    setCopiedCode(id);
    setTimeout(() => setCopiedCode(null), 2000);
  };

  const tutorials = [
    {
      title: 'Build a Chatbot in 10 Minutes',
      description: 'Complete guide to creating a customer service chatbot',
      type: 'video',
      duration: '10 min',
      level: 'Beginner',
      views: '2.3k'
    },
    {
      title: 'Advanced Workflow Automation',
      description: 'Create complex multi-step workflows with conditions',
      type: 'article',
      duration: '15 min',
      level: 'Intermediate',
      views: '1.8k'
    },
    {
      title: 'Integrating External APIs',
      description: 'Connect your favorite tools and services',
      type: 'video',
      duration: '12 min',
      level: 'Beginner',
      views: '3.1k'
    }
  ];

  return (
    <div className="space-y-6">
      <Card className="bg-gradient-to-r from-indigo-600 to-blue-600 text-white border-0">
        <CardContent className="p-8 text-center">
          <FileText className="w-12 h-12 mx-auto mb-4 opacity-90" />
          <h2 className="text-2xl font-bold mb-2">SDK Documentation</h2>
          <p className="text-indigo-100 max-w-2xl mx-auto">
            Comprehensive guides, API references, and examples to help you build amazing AI applications.
          </p>
        </CardContent>
      </Card>

      {/* Search Bar */}
      <Card>
        <CardContent className="p-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
            <input
              type="text"
              placeholder="Search documentation..."
              className="w-full pl-10 pr-4 py-3 border rounded-lg text-sm"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Categories Sidebar */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Categories</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            {categories.map((category) => (
              <Button
                key={category.id}
                variant={selectedCategory === category.id ? "default" : "outline"}
                className="w-full justify-start"
                onClick={() => setSelectedCategory(category.id)}
              >
                {category.icon}
                <span className="ml-2">{category.name}</span>
              </Button>
            ))}
          </CardContent>
        </Card>

        {/* Main Content */}
        <div className="lg:col-span-3">
          <Tabs defaultValue="guides" className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="guides">Guides</TabsTrigger>
              <TabsTrigger value="reference">API Reference</TabsTrigger>
              <TabsTrigger value="examples">Examples</TabsTrigger>
            </TabsList>

            <TabsContent value="guides" className="space-y-6">
              {selectedCategory === 'getting-started' && (
                <div className="space-y-4">
                  <h3 className="text-xl font-semibold">Getting Started</h3>
                  {gettingStartedGuides.map((guide, index) => (
                    <Card key={index}>
                      <CardHeader>
                        <div className="flex items-start justify-between">
                          <div>
                            <CardTitle className="text-lg">{guide.title}</CardTitle>
                            <CardDescription>{guide.description}</CardDescription>
                          </div>
                          <div className="flex gap-2">
                            <Badge variant="outline">{guide.type}</Badge>
                            <Badge variant="outline">
                              <Clock className="w-3 h-3 mr-1" />
                              {guide.duration}
                            </Badge>
                            <Badge variant="secondary">{guide.difficulty}</Badge>
                          </div>
                        </div>
                      </CardHeader>
                      <CardContent>
                        <div className="prose prose-sm max-w-none">
                          <pre className="bg-gray-100 p-4 rounded-lg overflow-x-auto">
                            <code>{guide.content.trim()}</code>
                          </pre>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}

              {/* Popular Tutorials */}
              <div className="space-y-4">
                <h3 className="text-xl font-semibold">Popular Tutorials</h3>
                <div className="grid gap-4">
                  {tutorials.map((tutorial, index) => (
                    <Card key={index} className="hover:shadow-lg transition-all cursor-pointer">
                      <CardContent className="p-6">
                        <div className="flex items-start justify-between">
                          <div className="flex items-start gap-4">
                            <div className={`p-3 rounded-lg ${
                              tutorial.type === 'video' ? 'bg-red-100 text-red-600' : 'bg-blue-100 text-blue-600'
                            }`}>
                              {tutorial.type === 'video' ? <Video className="w-6 h-6" /> : <FileText className="w-6 h-6" />}
                            </div>
                            <div>
                              <h4 className="font-semibold text-lg mb-2">{tutorial.title}</h4>
                              <p className="text-gray-600 text-sm mb-3">{tutorial.description}</p>
                              <div className="flex gap-3 text-sm text-gray-500">
                                <span className="flex items-center gap-1">
                                  <Clock className="w-4 h-4" />
                                  {tutorial.duration}
                                </span>
                                <span className="flex items-center gap-1">
                                  <Users className="w-4 h-4" />
                                  {tutorial.views} views
                                </span>
                                <Badge variant="outline" className="text-xs">
                                  {tutorial.level}
                                </Badge>
                              </div>
                            </div>
                          </div>
                          <Button variant="outline" size="sm">
                            <ExternalLink className="w-4 h-4 mr-1" />
                            View
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>
            </TabsContent>

            <TabsContent value="reference" className="space-y-6">
              <div className="space-y-4">
                <h3 className="text-xl font-semibold">API Reference</h3>
                
                <div className="grid gap-4">
                  {Object.entries({
                    'Agents API': 'Manage and execute AI agents',
                    'Tools API': 'Create and run API tools',
                    'Workflows API': 'Build and execute workflows',
                    'Sessions API': 'Manage conversation sessions',
                    'Providers API': 'Configure AI providers'
                  }).map(([title, description]) => (
                    <Card key={title} className="hover:shadow-lg transition-all cursor-pointer">
                      <CardContent className="p-6">
                        <div className="flex items-center justify-between">
                          <div>
                            <h4 className="font-semibold text-lg mb-2">{title}</h4>
                            <p className="text-gray-600 text-sm">{description}</p>
                          </div>
                          <Button variant="outline" size="sm">
                            <Book className="w-4 h-4 mr-1" />
                            View Docs
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>
            </TabsContent>

            <TabsContent value="examples" className="space-y-6">
              <div className="space-y-4">
                <h3 className="text-xl font-semibold">Code Examples</h3>
                
                {Object.entries(codeExamples).map(([id, code]) => (
                  <Card key={id}>
                    <CardHeader>
                      <div className="flex items-center justify-between">
                        <CardTitle className="text-lg capitalize">
                          {id.replace('-', ' ')}
                        </CardTitle>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => copyToClipboard(code, id)}
                        >
                          {copiedCode === id ? (
                            <CheckCircle className="w-4 h-4 mr-1 text-green-600" />
                          ) : (
                            <Copy className="w-4 h-4 mr-1" />
                          )}
                          {copiedCode === id ? 'Copied!' : 'Copy'}
                        </Button>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <pre className="bg-gray-100 p-4 rounded-lg overflow-x-auto">
                        <code className="text-sm">{code}</code>
                      </pre>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </div>

      {/* Quick Links */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Links</CardTitle>
          <CardDescription>Jump to commonly used resources</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Button variant="outline" className="justify-start h-auto p-4">
              <div className="text-left">
                <div className="flex items-center gap-2 mb-1">
                  <Download className="w-4 h-4" />
                  <span className="font-semibold">Download SDK</span>
                </div>
                <p className="text-xs text-gray-600">Get the latest version</p>
              </div>
            </Button>
            
            <Button variant="outline" className="justify-start h-auto p-4">
              <div className="text-left">
                <div className="flex items-center gap-2 mb-1">
                  <Star className="w-4 h-4" />
                  <span className="font-semibold">GitHub Repository</span>
                </div>
                <p className="text-xs text-gray-600">View source code</p>
              </div>
            </Button>
            
            <Button variant="outline" className="justify-start h-auto p-4">
              <div className="text-left">
                <div className="flex items-center gap-2 mb-1">
                  <Users className="w-4 h-4" />
                  <span className="font-semibold">Community Forum</span>
                </div>
                <p className="text-xs text-gray-600">Get help from others</p>
              </div>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}