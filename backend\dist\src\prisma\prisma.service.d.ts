import { OnModuleInit, OnModuleD<PERSON>roy, INestApplication } from '@nestjs/common';
import { PrismaClient } from '@prisma/client';
import { ConfigService } from '@nestjs/config';
export declare class PrismaService extends PrismaClient implements OnModuleInit, OnModuleDestroy {
    private configService;
    constructor(configService: ConfigService);
    onModuleInit(): Promise<void>;
    onModuleDestroy(): Promise<void>;
    enableShutdownHooks(app: INestApplication): Promise<void>;
    withOrganization<T>(organizationId: string, callback: (prisma: PrismaClient) => Promise<T>): Promise<T>;
    getOrganizationContext(organizationId: string): Promise<any>;
}
