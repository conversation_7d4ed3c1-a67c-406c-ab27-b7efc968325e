import { Module } from '@nestjs/common';
import { ProvidersController } from './providers.controller';
import { ProvidersService } from './providers.service';
import { CircuitBreakerService } from './circuit-breaker.service';
import { ProviderExecutorService } from './provider-executor.service';
import { AuthModule } from '../auth/auth.module';
import { APIMXModule } from '../apix/apix.module';
import { SessionsModule } from '../sessions/sessions.module';

@Module({
  imports: [AuthModule, APIMXModule, SessionsModule],
  controllers: [ProvidersController],
  providers: [
    ProvidersService,
    CircuitBreakerService,
    ProviderExecutorService,
  ],
  exports: [
    ProvidersService,
    CircuitBreakerService,
    ProviderExecutorService,
  ],
})
export class ProvidersModule {}