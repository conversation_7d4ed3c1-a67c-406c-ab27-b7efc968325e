import { Injectable, NotFoundException, ForbiddenException, BadRequestException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { APIMXService } from '../apix/apix.service';
import { CreateAgentDto, UpdateAgentDto, AgentQueryDto, AgentVersionDto, CloneAgentDto } from './dto/agent.dto';

@Injectable()
export class AgentsService {
  constructor(
    private prisma: PrismaService,
    private apixService: APIMXService,
  ) {}

  async create(userId: string, organizationId: string, createAgentDto: CreateAgentDto) {
    const { templateId, config, tags = [], ...agentData } = createAgentDto;

    // Validate template if provided
    if (templateId) {
      const template = await this.prisma.template.findFirst({
        where: {
          id: templateId,
          OR: [
            { organizationId },
            { isPublic: true },
          ],
        },
      });

      if (!template) {
        throw new NotFoundException('Template not found');
      }

      // Merge template config with provided config
      const templateConfig = template.template as any;
      config.prompt = config.prompt || templateConfig.prompt;
      config.systemPrompt = config.systemPrompt || templateConfig.systemPrompt;
      config.temperature = config.temperature ?? templateConfig.config?.temperature ?? 0.7;
      config.maxTokens = config.maxTokens ?? templateConfig.config?.maxTokens ?? 4000;
      config.memory = config.memory || templateConfig.config?.memory;
    }

    const agent = await this.prisma.agent.create({
      data: {
        ...agentData,
        organizationId,
        templateId,
        config: {
          ...config,
          memory: config.memory || {
            enabled: true,
            maxMessages: 50,
            strategy: 'rolling',
          },
        },
        metadata: {
          tags,
          createdBy: userId,
          version: '1.0.0',
        },
      },
    });

    await this.apixService.publishAgentEvent(
      'created',
      agent.id,
      organizationId,
      {
        name: agent.name,
        status: agent.status,
        createdBy: userId,
      }
    );

    return agent;
  }

  async findAll(organizationId: string, query: AgentQueryDto) {
    const {
      page = 1,
      limit = 20,
      status,
      search,
      model,
      tags,
      sortBy = 'createdAt',
      sortOrder = 'desc',
    } = query;

    const skip = (page - 1) * limit;

    const where: any = {
      organizationId,
      ...(status && { status }),
    };

    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } },
      ];
    }

    if (model) {
      where.config = {
        path: ['model'],
        equals: model,
      };
    }

    if (tags && tags.length > 0) {
      where.metadata = {
        path: ['tags'],
        array_contains: tags,
      };
    }

    const [agents, total] = await Promise.all([
      this.prisma.agent.findMany({
        where,
        include: {
          template: {
            select: {
              id: true,
              name: true,
              category: true,
            },
          },
        },
        orderBy: { [sortBy]: sortOrder },
        skip,
        take: limit,
      }),
      this.prisma.agent.count({ where }),
    ]);

    return {
      agents,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    };
  }

  async findOne(id: string, organizationId: string) {
    const agent = await this.prisma.agent.findUnique({
      where: { id },
      include: {
        template: {
          select: {
            id: true,
            name: true,
            description: true,
            category: true,
          },
        },
      },
    });

    if (!agent) {
      throw new NotFoundException('Agent not found');
    }

    if (agent.organizationId !== organizationId) {
      throw new ForbiddenException('Access denied');
    }

    return agent;
  }

  async update(id: string, organizationId: string, updateAgentDto: UpdateAgentDto) {
    const agent = await this.findOne(id, organizationId);

    const updatedAgent = await this.prisma.agent.update({
      where: { id },
      data: {
        ...updateAgentDto,
        config: updateAgentDto.config ? {
          ...agent.config,
          ...updateAgentDto.config,
        } : agent.config,
        metadata: {
          ...agent.metadata,
          ...(updateAgentDto.tags && { tags: updateAgentDto.tags }),
          updatedAt: new Date(),
        },
        updatedAt: new Date(),
      },
    });

    await this.apixService.publishAgentEvent(
      'updated',
      agent.id,
      organizationId,
      {
        changes: updateAgentDto,
      }
    );

    return updatedAgent;
  }

  async remove(id: string, organizationId: string) {
    const agent = await this.findOne(id, organizationId);

    // Check if agent has active sessions
    const activeSessions = await this.prisma.session.count({
      where: {
        organizationId,
        type: 'agent',
        metadata: {
          path: ['targetId'],
          equals: id,
        },
        status: 'active',
      },
    });

    if (activeSessions > 0) {
      throw new BadRequestException('Cannot delete agent with active sessions');
    }

    await this.prisma.agent.delete({
      where: { id },
    });

    await this.apixService.publishAgentEvent(
      'deleted',
      agent.id,
      organizationId,
      {}
    );

    return { message: 'Agent deleted successfully' };
  }

  async createVersion(id: string, organizationId: string, versionDto: AgentVersionDto) {
    const agent = await this.findOne(id, organizationId);

    const currentMetadata = agent.metadata as any;
    const versions = currentMetadata.versions || [];

    // Add current config as a version
    versions.push({
      version: versionDto.version,
      description: versionDto.description,
      config: versionDto.config,
      createdAt: new Date(),
      metadata: versionDto.metadata,
    });

    const updatedAgent = await this.prisma.agent.update({
      where: { id },
      data: {
        config: versionDto.config,
        metadata: {
          ...currentMetadata,
          versions,
          currentVersion: versionDto.version,
        },
        updatedAt: new Date(),
      },
    });

    await this.apixService.publishAgentEvent(
      'version_created',
      agent.id,
      organizationId,
      {
        version: versionDto.version,
      }
    );

    return updatedAgent;
  }

  async rollbackToVersion(id: string, organizationId: string, version: string) {
    const agent = await this.findOne(id, organizationId);
    const metadata = agent.metadata as any;
    const versions = metadata.versions || [];

    const targetVersion = versions.find((v: any) => v.version === version);
    if (!targetVersion) {
      throw new NotFoundException('Version not found');
    }

    const updatedAgent = await this.prisma.agent.update({
      where: { id },
      data: {
        config: targetVersion.config,
        metadata: {
          ...metadata,
          currentVersion: version,
          rolledBackAt: new Date(),
        },
        updatedAt: new Date(),
      },
    });

    await this.apixService.publishAgentEvent(
      'version_rollback',
      agent.id,
      organizationId,
      {
        version,
        previousVersion: metadata.currentVersion,
      }
    );

    return updatedAgent;
  }

  async clone(id: string, organizationId: string, cloneDto: CloneAgentDto) {
    const originalAgent = await this.findOne(id, organizationId);

    const clonedAgent = await this.prisma.agent.create({
      data: {
        name: cloneDto.name,
        description: cloneDto.description || `Clone of ${originalAgent.name}`,
        organizationId,
        templateId: originalAgent.templateId,
        config: originalAgent.config,
        status: 'draft',
        metadata: {
          ...originalAgent.metadata,
          clonedFrom: originalAgent.id,
          clonedAt: new Date(),
          versions: cloneDto.includeVersionHistory
            ? (originalAgent.metadata as any).versions
            : undefined,
        },
      },
    });

    await this.apixService.publishAgentEvent(
      'cloned',
      clonedAgent.id,
      organizationId,
      {
        originalId: originalAgent.id,
        originalName: originalAgent.name,
      }
    );

    return clonedAgent;
  }

  async getAgentStats(organizationId: string) {
    const [total, byStatus, byModel, executions] = await Promise.all([
      this.prisma.agent.count({ where: { organizationId } }),
      this.prisma.agent.groupBy({
        by: ['status'],
        where: { organizationId },
        _count: true,
      }),
      this.prisma.agent.groupBy({
        by: ['config'],
        where: { organizationId },
        _count: true,
      }),
      this.prisma.session.count({
        where: {
          organizationId,
          type: 'agent',
        },
      }),
    ]);

    const modelStats = {};
    const modelData = await this.prisma.agent.findMany({
      where: { organizationId },
      select: { config: true },
    });

    modelData.forEach((agent) => {
      const config = agent.config as any;
      const model = config.model;
      modelStats[model] = (modelStats[model] || 0) + 1;
    });

    return {
      total,
      executions,
      byStatus: byStatus.reduce((acc, item) => {
        acc[item.status] = item._count;
        return acc;
      }, {}),
      byModel: modelStats,
    };
  }

  async getPopularModels(organizationId: string) {
    const agents = await this.prisma.agent.findMany({
      where: { organizationId },
      select: { config: true },
    });

    const modelCounts = {};
    agents.forEach((agent) => {
      const config = agent.config as any;
      const model = config.model;
      modelCounts[model] = (modelCounts[model] || 0) + 1;
    });

    return Object.entries(modelCounts)
      .map(([model, count]) => ({ model, count }))
      .sort((a, b) => (b.count as number) - (a.count as number))
      .slice(0, 10);
  }

  async searchAgentsByTags(organizationId: string, tags: string[]) {
    return this.prisma.agent.findMany({
      where: {
        organizationId,
        metadata: {
          path: ['tags'],
          array_contains: tags,
        },
      },
      select: {
        id: true,
        name: true,
        description: true,
        status: true,
        metadata: true,
      },
    });
  }
}