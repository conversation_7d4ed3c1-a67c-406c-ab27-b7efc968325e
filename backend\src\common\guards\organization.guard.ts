import { Injectable, CanActivate, ExecutionContext, ForbiddenException } from '@nestjs/common';
import { Reflector } from '@nestjs/core';

@Injectable()
export class OrganizationGuard implements CanActivate {
  constructor(private reflector: Reflector) {}

  canActivate(context: ExecutionContext): boolean {
    const request = context.switchToHttp().getRequest();
    const user = request.user;

    if (!user) {
      throw new ForbiddenException('User not authenticated');
    }

    const organizationId = request.params.organizationId || 
                          request.body.organizationId || 
                          request.query.organizationId;

    if (organizationId && organizationId !== user.organizationId) {
      throw new ForbiddenException('Access denied to this organization');
    }

    request.organizationId = user.organizationId;
    return true;
  }
}