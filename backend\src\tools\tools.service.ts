import { Injectable, NotFoundException, ForbiddenException, BadRequestException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { APIMXService } from '../apix/apix.service';
import { CreateToolDto, UpdateToolDto, ToolQueryDto, ToolVersionDto } from './dto/tool.dto';

@Injectable()
export class ToolsService {
  constructor(
    private prisma: PrismaService,
    private apixService: APIMXService,
  ) {}

  async create(userId: string, organizationId: string, createToolDto: CreateToolDto) {
    const { tags = [], metadata = {}, ...toolData } = createToolDto;

    // Validate tool configuration based on type
    this.validateToolConfig(createToolDto.type, createToolDto.config);

    const tool = await this.prisma.tool.create({
      data: {
        ...toolData,
        organizationId,
        metadata: {
          ...metadata,
          tags,
          createdBy: userId,
          version: metadata.version || '1.0.0',
          executions: 0,
          lastExecuted: null,
        },
      },
    });

    await this.apixService.publishToolEvent(
      'created',
      tool.id,
      organizationId,
      {
        name: tool.name,
        type: tool.type,
        status: tool.status,
        createdBy: userId,
      }
    );

    return tool;
  }

  async findAll(organizationId: string, query: ToolQueryDto) {
    const {
      page = 1,
      limit = 20,
      type,
      status,
      search,
      category,
      tags,
      sortBy = 'createdAt',
      sortOrder = 'desc',
    } = query;

    const skip = (page - 1) * limit;

    const where: any = {
      organizationId,
      ...(type && { type }),
      ...(status && { status }),
    };

    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } },
      ];
    }

    if (category) {
      where.metadata = {
        path: ['category'],
        equals: category,
      };
    }

    if (tags && tags.length > 0) {
      where.metadata = {
        ...where.metadata,
        path: ['tags'],
        array_contains: tags,
      };
    }

    const [tools, total] = await Promise.all([
      this.prisma.tool.findMany({
        where,
        orderBy: { [sortBy]: sortOrder },
        skip,
        take: limit,
      }),
      this.prisma.tool.count({ where }),
    ]);

    return {
      tools,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    };
  }

  async findOne(id: string, organizationId: string) {
    const tool = await this.prisma.tool.findUnique({
      where: { id },
    });

    if (!tool) {
      throw new NotFoundException('Tool not found');
    }

    if (tool.organizationId !== organizationId) {
      throw new ForbiddenException('Access denied');
    }

    return tool;
  }

  async update(id: string, organizationId: string, updateToolDto: UpdateToolDto) {
    const tool = await this.findOne(id, organizationId);

    // Validate configuration if provided
    if (updateToolDto.config) {
      this.validateToolConfig(tool.type as any, updateToolDto.config);
    }

    const updatedTool = await this.prisma.tool.update({
      where: { id },
      data: {
        ...updateToolDto,
        config: updateToolDto.config ? {
          ...tool.config,
          ...updateToolDto.config,
        } : tool.config,
        schema: updateToolDto.schema ? {
          ...tool.schema,
          ...updateToolDto.schema,
        } : tool.schema,
        metadata: updateToolDto.metadata ? {
          ...tool.metadata,
          ...updateToolDto.metadata,
          updatedAt: new Date(),
        } : tool.metadata,
        updatedAt: new Date(),
      },
    });

    await this.apixService.publishToolEvent(
      'updated',
      tool.id,
      organizationId,
      {
        changes: updateToolDto,
      }
    );

    return updatedTool;
  }

  async remove(id: string, organizationId: string) {
    const tool = await this.findOne(id, organizationId);

    // Check if tool has active sessions
    const activeSessions = await this.prisma.session.count({
      where: {
        organizationId,
        type: 'tool',
        metadata: {
          path: ['targetId'],
          equals: id,
        },
        status: 'active',
      },
    });

    if (activeSessions > 0) {
      throw new BadRequestException('Cannot delete tool with active sessions');
    }

    await this.prisma.tool.delete({
      where: { id },
    });

    await this.apixService.publishToolEvent(
      'deleted',
      tool.id,
      organizationId,
      {}
    );

    return { message: 'Tool deleted successfully' };
  }

  async createVersion(id: string, organizationId: string, versionDto: ToolVersionDto) {
    const tool = await this.findOne(id, organizationId);

    const currentMetadata = tool.metadata as any;
    const versions = currentMetadata.versions || [];

    // Add current config as a version
    versions.push({
      version: versionDto.version,
      description: versionDto.description,
      config: versionDto.config,
      schema: versionDto.schema,
      createdAt: new Date(),
      metadata: versionDto.metadata,
    });

    const updatedTool = await this.prisma.tool.update({
      where: { id },
      data: {
        config: versionDto.config,
        schema: versionDto.schema,
        metadata: {
          ...currentMetadata,
          versions,
          currentVersion: versionDto.version,
        },
        updatedAt: new Date(),
      },
    });

    await this.apixService.publishToolEvent(
      'version_created',
      tool.id,
      organizationId,
      {
        version: versionDto.version,
      }
    );

    return updatedTool;
  }

  async rollbackToVersion(id: string, organizationId: string, version: string) {
    const tool = await this.findOne(id, organizationId);
    const metadata = tool.metadata as any;
    const versions = metadata.versions || [];

    const targetVersion = versions.find((v: any) => v.version === version);
    if (!targetVersion) {
      throw new NotFoundException('Version not found');
    }

    const updatedTool = await this.prisma.tool.update({
      where: { id },
      data: {
        config: targetVersion.config,
        schema: targetVersion.schema,
        metadata: {
          ...metadata,
          currentVersion: version,
          rolledBackAt: new Date(),
        },
        updatedAt: new Date(),
      },
    });

    await this.apixService.publishToolEvent(
      'version_rollback',
      tool.id,
      organizationId,
      {
        version,
        previousVersion: metadata.currentVersion,
      }
    );

    return updatedTool;
  }

  async clone(id: string, organizationId: string, cloneData: { name: string; description?: string }) {
    const originalTool = await this.findOne(id, organizationId);

    const clonedTool = await this.prisma.tool.create({
      data: {
        name: cloneData.name,
        description: cloneData.description || `Clone of ${originalTool.name}`,
        organizationId,
        type: originalTool.type,
        config: originalTool.config,
        schema: originalTool.schema,
        status: 'draft',
        metadata: {
          ...originalTool.metadata,
          clonedFrom: originalTool.id,
          clonedAt: new Date(),
          executions: 0,
          lastExecuted: null,
        },
      },
    });

    await this.apixService.publishToolEvent(
      'cloned',
      clonedTool.id,
      organizationId,
      {
        originalId: originalTool.id,
        originalName: originalTool.name,
      }
    );

    return clonedTool;
  }

  async getToolStats(organizationId: string) {
    const [total, byType, byStatus, executions] = await Promise.all([
      this.prisma.tool.count({ where: { organizationId } }),
      this.prisma.tool.groupBy({
        by: ['type'],
        where: { organizationId },
        _count: true,
      }),
      this.prisma.tool.groupBy({
        by: ['status'],
        where: { organizationId },
        _count: true,
      }),
      this.prisma.session.count({
        where: {
          organizationId,
          type: 'tool',
        },
      }),
    ]);

    return {
      total,
      executions,
      byType: byType.reduce((acc, item) => {
        acc[item.type] = item._count;
        return acc;
      }, {}),
      byStatus: byStatus.reduce((acc, item) => {
        acc[item.status] = item._count;
        return acc;
      }, {}),
    };
  }

  async getPopularTools(organizationId: string, limit = 10) {
    const tools = await this.prisma.tool.findMany({
      where: { organizationId },
      take: limit,
    });

    // Sort by executions from metadata
    return tools
      .map(tool => ({
        ...tool,
        executions: (tool.metadata as any)?.executions || 0,
      }))
      .sort((a, b) => b.executions - a.executions);
  }

  async searchToolsByTags(organizationId: string, tags: string[]) {
    return this.prisma.tool.findMany({
      where: {
        organizationId,
        metadata: {
          path: ['tags'],
          array_contains: tags,
        },
      },
      select: {
        id: true,
        name: true,
        description: true,
        type: true,
        status: true,
        metadata: true,
      },
    });
  }

  async getToolsByCategory(organizationId: string, category: string) {
    return this.prisma.tool.findMany({
      where: {
        organizationId,
        metadata: {
          path: ['category'],
          equals: category,
        },
      },
      orderBy: { metadata: { path: ['executions'], sort: 'desc' } },
    });
  }

  private validateToolConfig(type: string, config: any) {
    switch (type) {
      case 'api':
        if (!config.endpoint) {
          throw new BadRequestException('API tools require an endpoint');
        }
        if (!config.method) {
          throw new BadRequestException('API tools require an HTTP method');
        }
        break;

      case 'function':
        if (!config.functionCode) {
          throw new BadRequestException('Function tools require function code');
        }
        break;

      case 'workflow':
        if (!config.workflowSteps || !Array.isArray(config.workflowSteps)) {
          throw new BadRequestException('Workflow tools require workflow steps');
        }
        break;

      default:
        throw new BadRequestException('Invalid tool type');
    }
  }

  async incrementExecutionCount(toolId: string) {
    const tool = await this.prisma.tool.findUnique({
      where: { id: toolId },
    });

    if (tool) {
      const metadata = tool.metadata as any;
      await this.prisma.tool.update({
        where: { id: toolId },
        data: {
          metadata: {
            ...metadata,
            executions: (metadata.executions || 0) + 1,
            lastExecuted: new Date(),
          },
        },
      });
    }
  }
}