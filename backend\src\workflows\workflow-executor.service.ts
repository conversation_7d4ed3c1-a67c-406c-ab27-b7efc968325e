import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { APIMXService } from '../apix/apix.service';
import { SessionsService } from '../sessions/sessions.service';
import { AgentExecutorService } from '../agents/agent-executor.service';
import { ToolExecutorService } from '../tools/tool-executor.service';
import { 
  ExecuteWorkflowDto, 
  WorkflowExecutionStatus, 
  WorkflowNodeType,
  WorkflowExecutionQueryDto 
} from './dto/workflow.dto';

export interface WorkflowExecutionResult {
  success: boolean;
  output: any;
  executionTime: number;
  stepResults: WorkflowStepResult[];
  error?: string;
  executionId: string;
}

export interface WorkflowStepResult {
  nodeId: string;
  nodeName: string;
  nodeType: WorkflowNodeType;
  status: 'pending' | 'running' | 'completed' | 'failed' | 'skipped';
  input?: any;
  output?: any;
  error?: string;
  startTime: number;
  endTime?: number;
  executionTime?: number;
}

export interface WorkflowContext {
  input: any;
  variables: Record<string, any>;
  stepResults: Record<string, any>;
  sessionId?: string;
  userId: string;
  organizationId: string;
}

@Injectable()
export class WorkflowExecutorService {
  constructor(
    private prisma: PrismaService,
    private apixService: APIMXService,
    private sessionsService: SessionsService,
    private agentExecutorService: AgentExecutorService,
    private toolExecutorService: ToolExecutorService,
  ) {}

  async executeWorkflow(
    workflowId: string,
    organizationId: string,
    userId: string,
    executeDto: ExecuteWorkflowDto,
  ): Promise<WorkflowExecutionResult> {
    const startTime = Date.now();

    // Get workflow definition
    const workflow = await this.prisma.workflow.findFirst({
      where: { id: workflowId, organizationId },
    });

    if (!workflow) {
      throw new NotFoundException('Workflow not found');
    }

    if (workflow.status !== 'active') {
      throw new BadRequestException('Workflow is not active');
    }

    // Create execution record
    const execution = await this.prisma.workflowExecution.create({
      data: {
        workflowId,
        organizationId,
        userId,
        sessionId: executeDto.sessionId,
        status: WorkflowExecutionStatus.RUNNING,
        input: executeDto.input,
        context: executeDto.context,
        startedAt: new Date(),
        metadata: {
          overrides: executeDto.overrides,
          version: workflow.metadata?.version || '1.0.0',
        },
      },
    });

    try {
      // Initialize execution context
      const context: WorkflowContext = {
        input: executeDto.input || {},
        variables: {},
        stepResults: {},
        sessionId: executeDto.sessionId,
        userId,
        organizationId,
      };

      // Emit execution started event
      await this.apixService.broadcastToOrganization(organizationId, {
        type: 'workflow_execution_started',
        payload: { 
          executionId: execution.id, 
          workflowId, 
          userId 
        },
        timestamp: new Date(),
        userId,
        organizationId,
      });

      // Execute workflow
      const stepResults = await this.executeWorkflowNodes(
        workflow.nodes as any[],
        workflow.connections as any[],
        context,
        execution.id,
      );

      const executionTime = Date.now() - startTime;
      const finalOutput = this.extractFinalOutput(stepResults, workflow.nodes as any[]);

      // Update execution record
      await this.prisma.workflowExecution.update({
        where: { id: execution.id },
        data: {
          status: WorkflowExecutionStatus.COMPLETED,
          output: finalOutput,
          completedAt: new Date(),
          executionTime,
          stepResults,
        },
      });

      // Emit completion event
      await this.apixService.broadcastToOrganization(organizationId, {
        type: 'workflow_execution_completed',
        payload: { 
          executionId: execution.id, 
          workflowId, 
          output: finalOutput,
          executionTime 
        },
        timestamp: new Date(),
        userId,
        organizationId,
      });

      // Track billing
      await this.trackUsage(organizationId, workflowId, execution.id, executionTime);

      return {
        success: true,
        output: finalOutput,
        executionTime,
        stepResults,
        executionId: execution.id,
      };

    } catch (error) {
      const executionTime = Date.now() - startTime;

      // Update execution record with error
      await this.prisma.workflowExecution.update({
        where: { id: execution.id },
        data: {
          status: WorkflowExecutionStatus.FAILED,
          error: error.message,
          completedAt: new Date(),
          executionTime,
        },
      });

      // Emit error event
      await this.apixService.broadcastToOrganization(organizationId, {
        type: 'workflow_execution_failed',
        payload: { 
          executionId: execution.id, 
          workflowId, 
          error: error.message 
        },
        timestamp: new Date(),
        userId,
        organizationId,
      });

      return {
        success: false,
        output: null,
        executionTime,
        stepResults: [],
        error: error.message,
        executionId: execution.id,
      };
    }
  }

  async getExecutions(
    organizationId: string,
    query: WorkflowExecutionQueryDto,
  ) {
    const { status, workflowId, page, limit } = query;
    const skip = (page - 1) * limit;

    const where: any = { organizationId };

    if (status) {
      where.status = status;
    }

    if (workflowId) {
      where.workflowId = workflowId;
    }

    const [executions, total] = await Promise.all([
      this.prisma.workflowExecution.findMany({
        where,
        skip,
        take: limit,
        orderBy: { startedAt: 'desc' },
        include: {
          workflow: {
            select: { id: true, name: true },
          },
        },
      }),
      this.prisma.workflowExecution.count({ where }),
    ]);

    return {
      executions,
      pagination: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  async getExecution(executionId: string, organizationId: string) {
    const execution = await this.prisma.workflowExecution.findFirst({
      where: { id: executionId, organizationId },
      include: {
        workflow: true,
      },
    });

    if (!execution) {
      throw new NotFoundException('Workflow execution not found');
    }

    return execution;
  }

  async cancelExecution(executionId: string, organizationId: string, userId: string) {
    const execution = await this.getExecution(executionId, organizationId);

    if (!['pending', 'running', 'waiting_approval'].includes(execution.status)) {
      throw new BadRequestException('Cannot cancel execution in current status');
    }

    await this.prisma.workflowExecution.update({
      where: { id: executionId },
      data: {
        status: WorkflowExecutionStatus.CANCELLED,
        completedAt: new Date(),
        error: 'Cancelled by user',
      },
    });

    // Emit cancellation event
    await this.apixService.broadcastToOrganization(organizationId, {
      type: 'workflow_execution_cancelled',
      payload: { executionId, workflowId: execution.workflowId },
      timestamp: new Date(),
      userId,
      organizationId,
    });

    return { success: true };
  }

  private async executeWorkflowNodes(
    nodes: any[],
    connections: any[],
    context: WorkflowContext,
    executionId: string,
  ): Promise<WorkflowStepResult[]> {
    const stepResults: WorkflowStepResult[] = [];
    const nodeStatus: Record<string, 'pending' | 'running' | 'completed' | 'failed' | 'skipped'> = {};
    const nodeOutputs: Record<string, any> = {};

    // Initialize all nodes as pending
    nodes.forEach(node => {
      nodeStatus[node.id] = 'pending';
    });

    // Find entry points (nodes with no incoming connections)
    const entryNodes = nodes.filter(node => 
      !connections.some(conn => conn.targetNodeId === node.id)
    );

    if (entryNodes.length === 0) {
      throw new BadRequestException('Workflow has no entry points');
    }

    // Execute nodes using topological sort
    const executionQueue = [...entryNodes];
    const inDegree: Record<string, number> = {};

    // Calculate in-degrees
    nodes.forEach(node => {
      inDegree[node.id] = connections.filter(conn => conn.targetNodeId === node.id).length;
    });

    while (executionQueue.length > 0) {
      const currentNode = executionQueue.shift()!;

      if (nodeStatus[currentNode.id] !== 'pending') {
        continue;
      }

      try {
        // Execute the node
        const stepResult = await this.executeNode(currentNode, context, nodeOutputs);
        stepResults.push(stepResult);
        
        nodeStatus[currentNode.id] = stepResult.status;
        if (stepResult.status === 'completed') {
          nodeOutputs[currentNode.id] = stepResult.output;
          context.stepResults[currentNode.id] = stepResult.output;
        }

        // Emit step completed event
        await this.apixService.broadcastToOrganization(context.organizationId, {
          type: 'workflow_step_completed',
          payload: { 
            executionId,
            nodeId: currentNode.id,
            nodeName: currentNode.name,
            status: stepResult.status,
            output: stepResult.output,
          },
          timestamp: new Date(),
          userId: context.userId,
          organizationId: context.organizationId,
        });

        // Handle failed node
        if (stepResult.status === 'failed') {
          throw new Error(`Node ${currentNode.name} failed: ${stepResult.error}`);
        }

        // Add successor nodes to queue
        const successorConnections = connections.filter(conn => 
          conn.sourceNodeId === currentNode.id
        );

        for (const connection of successorConnections) {
          // Check if condition is met (if specified)
          if (connection.condition) {
            const conditionMet = this.evaluateCondition(
              connection.condition, 
              stepResult.output, 
              context
            );
            if (!conditionMet) continue;
          }

          // Decrease in-degree and add to queue if ready
          inDegree[connection.targetNodeId]--;
          if (inDegree[connection.targetNodeId] === 0) {
            const targetNode = nodes.find(n => n.id === connection.targetNodeId);
            if (targetNode && nodeStatus[targetNode.id] === 'pending') {
              executionQueue.push(targetNode);
            }
          }
        }

      } catch (error) {
        const stepResult: WorkflowStepResult = {
          nodeId: currentNode.id,
          nodeName: currentNode.name,
          nodeType: currentNode.type,
          status: 'failed',
          error: error.message,
          startTime: Date.now(),
          endTime: Date.now(),
          executionTime: 0,
        };
        stepResults.push(stepResult);
        throw error;
      }
    }

    return stepResults;
  }

  private async executeNode(
    node: any,
    context: WorkflowContext,
    nodeOutputs: Record<string, any>,
  ): Promise<WorkflowStepResult> {
    const startTime = Date.now();
    
    const stepResult: WorkflowStepResult = {
      nodeId: node.id,
      nodeName: node.name,
      nodeType: node.type,
      status: 'running',
      startTime,
    };

    try {
      let output: any;

      // Prepare input for the node
      const nodeInput = this.prepareNodeInput(node, context, nodeOutputs);
      stepResult.input = nodeInput;

      switch (node.type) {
        case WorkflowNodeType.AGENT:
          output = await this.executeAgentNode(node, nodeInput, context);
          break;

        case WorkflowNodeType.TOOL:
          output = await this.executeToolNode(node, nodeInput, context);
          break;

        case WorkflowNodeType.CONDITION:
          output = await this.executeConditionNode(node, nodeInput, context);
          break;

        case WorkflowNodeType.PARALLEL:
          output = await this.executeParallelNode(node, nodeInput, context);
          break;

        case WorkflowNodeType.SEQUENCE:
          output = await this.executeSequenceNode(node, nodeInput, context);
          break;

        case WorkflowNodeType.HUMAN_APPROVAL:
          output = await this.executeHumanApprovalNode(node, nodeInput, context);
          break;

        default:
          throw new Error(`Unsupported node type: ${node.type}`);
      }

      const endTime = Date.now();
      stepResult.status = 'completed';
      stepResult.output = output;
      stepResult.endTime = endTime;
      stepResult.executionTime = endTime - startTime;

    } catch (error) {
      const endTime = Date.now();
      stepResult.status = 'failed';
      stepResult.error = error.message;
      stepResult.endTime = endTime;
      stepResult.executionTime = endTime - startTime;
    }

    return stepResult;
  }

  private async executeAgentNode(node: any, input: any, context: WorkflowContext) {
    const { agentId, sessionId: nodeSessionId, overrides } = node.config;

    // Use node's session ID or context session ID
    const sessionId = nodeSessionId || context.sessionId;

    if (!sessionId) {
      throw new Error('Session ID required for agent execution');
    }

    const result = await this.agentExecutorService.executeAgent(
      agentId,
      context.organizationId,
      context.userId,
      {
        input: typeof input === 'string' ? input : JSON.stringify(input),
        sessionId,
        context: context.variables,
        overrides,
      },
    );

    if (!result.success) {
      throw new Error(result.error || 'Agent execution failed');
    }

    return result.output;
  }

  private async executeToolNode(node: any, input: any, context: WorkflowContext) {
    const { toolId, overrides } = node.config;

    const result = await this.toolExecutorService.executeTool(
      toolId,
      context.organizationId,
      context.userId,
      {
        input,
        sessionId: context.sessionId,
        context: context.variables,
        overrides,
      },
    );

    if (!result.success) {
      throw new Error(result.error || 'Tool execution failed');
    }

    return result.output;
  }

  private async executeConditionNode(node: any, input: any, context: WorkflowContext) {
    const { condition, trueValue, falseValue } = node.config;

    const result = this.evaluateCondition(condition, input, context);
    
    return {
      condition: result,
      value: result ? trueValue : falseValue,
      input,
    };
  }

  private async executeParallelNode(node: any, input: any, context: WorkflowContext) {
    // Parallel node would execute multiple sub-workflows concurrently
    // For now, return the input
    return { type: 'parallel', input };
  }

  private async executeSequenceNode(node: any, input: any, context: WorkflowContext) {
    // Sequence node would execute multiple sub-workflows in sequence
    // For now, return the input
    return { type: 'sequence', input };
  }

  private async executeHumanApprovalNode(node: any, input: any, context: WorkflowContext) {
    const { approvers, timeout, message } = node.config;

    // Create HITL request (simplified implementation)
    const hitlRequest = await this.prisma.hITLRequest.create({
      data: {
        organizationId: context.organizationId,
        sessionId: context.sessionId || 'workflow',
        userId: context.userId,
        type: 'workflow_approval',
        title: `Approval needed: ${node.name}`,
        description: message || `Please approve the workflow step: ${node.name}`,
        payload: {
          workflowNodeId: node.id,
          input,
          approvers,
        },
        status: 'pending',
        priority: 'medium',
      },
    });

    // For now, auto-approve (in real implementation, this would wait for human input)
    await this.prisma.hITLRequest.update({
      where: { id: hitlRequest.id },
      data: {
        status: 'approved',
        resolvedAt: new Date(),
      },
    });

    return {
      approved: true,
      approver: 'auto-system',
      input,
      timestamp: new Date(),
    };
  }

  private prepareNodeInput(
    node: any,
    context: WorkflowContext,
    nodeOutputs: Record<string, any>,
  ): any {
    let input = context.input;

    // Apply input mapping if specified
    if (node.config.inputMapping) {
      input = this.applyInputMapping(node.config.inputMapping, context, nodeOutputs);
    }

    return input;
  }

  private applyInputMapping(
    mapping: any,
    context: WorkflowContext,
    nodeOutputs: Record<string, any>,
  ): any {
    // Simple input mapping implementation
    // In real implementation, this would support complex transformations
    const result: any = {};

    for (const [key, value] of Object.entries(mapping)) {
      if (typeof value === 'string' && value.startsWith('$')) {
        // Variable reference
        const varName = value.substring(1);
        if (varName === 'input') {
          result[key] = context.input;
        } else if (varName.startsWith('step.')) {
          const stepId = varName.substring(5);
          result[key] = nodeOutputs[stepId];
        } else {
          result[key] = context.variables[varName];
        }
      } else {
        result[key] = value;
      }
    }

    return result;
  }

  private evaluateCondition(condition: string, input: any, context: WorkflowContext): boolean {
    try {
      // Simple condition evaluation
      // In production, use a safe expression evaluator
      const variables = {
        input,
        ...context.variables,
        ...context.stepResults,
      };

      // Replace variables in condition
      let evaluatedCondition = condition;
      for (const [key, value] of Object.entries(variables)) {
        const regex = new RegExp(`\\$${key}\\b`, 'g');
        evaluatedCondition = evaluatedCondition.replace(regex, JSON.stringify(value));
      }

      // Simple evaluation (in production, use a proper expression parser)
      return new Function(`return ${evaluatedCondition}`)();
    } catch (error) {
      console.error('Condition evaluation failed:', error);
      return false;
    }
  }

  private extractFinalOutput(stepResults: WorkflowStepResult[], nodes: any[]): any {
    // Find terminal nodes (nodes with no outgoing connections)
    const terminalResults = stepResults.filter(result => 
      result.status === 'completed' && 
      nodes.some(node => node.id === result.nodeId && node.config.isOutput)
    );

    if (terminalResults.length === 1) {
      return terminalResults[0].output;
    } else if (terminalResults.length > 1) {
      return terminalResults.map(result => ({
        nodeId: result.nodeId,
        nodeName: result.nodeName,
        output: result.output,
      }));
    } else {
      // Return the last completed result
      const completedResults = stepResults.filter(result => result.status === 'completed');
      return completedResults.length > 0 ? completedResults[completedResults.length - 1].output : null;
    }
  }

  private async trackUsage(
    organizationId: string,
    workflowId: string,
    executionId: string,
    executionTime: number,
  ) {
    try {
      await this.prisma.billingUsage.create({
        data: {
          organizationId,
          type: 'workflow_execution',
          quantity: 1,
          cost: Math.max(0.05, executionTime * 0.0001), // Minimum 5 cents, plus time-based cost
          metadata: {
            workflowId,
            executionId,
            executionTime,
          },
        },
      });
    } catch (error) {
      console.error('Failed to track workflow usage:', error);
    }
  }
}