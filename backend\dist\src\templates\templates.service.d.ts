import { PrismaService } from '../prisma/prisma.service';
import { APIMXService } from '../apix/apix.service';
import { CreateTemplateDto, UpdateTemplateDto, TemplateQueryDto, RateTemplateDto, ForkTemplateDto } from './dto/template.dto';
export declare class TemplatesService {
    private prisma;
    private apixService;
    constructor(prisma: PrismaService, apixService: APIMXService);
    create(userId: string, organizationId: string, createTemplateDto: CreateTemplateDto): Promise<any>;
    findAll(organizationId: string, query: TemplateQueryDto): Promise<{
        templates: any;
        pagination: {
            page: number;
            limit: number;
            total: any;
            pages: number;
        };
    }>;
    findOne(id: string, organizationId: string): Promise<any>;
    update(id: string, organizationId: string, updateTemplateDto: UpdateTemplateDto): Promise<any>;
    remove(id: string, organizationId: string): Promise<{
        message: string;
    }>;
    fork(id: string, organizationId: string, forkDto: ForkTemplateDto): Promise<any>;
    rate(id: string, organizationId: string, userId: string, rateDto: RateTemplateDto): Promise<any>;
    getPopularTemplates(organizationId: string, limit?: number): Promise<any>;
    getTemplatesByCategory(organizationId: string, category: string): Promise<any>;
    getTemplateStats(organizationId: string): Promise<{
        total: any;
        totalDownloads: any;
        byCategory: any;
        byVisibility: any;
    }>;
    private incrementDownloadCount;
    private incrementForkCount;
    searchTemplates(organizationId: string, searchTerm: string, filters?: any): Promise<any>;
}
