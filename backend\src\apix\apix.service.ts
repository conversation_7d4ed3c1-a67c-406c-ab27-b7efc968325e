import { Injectable } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { APIMXGateway } from './apix.gateway';

export interface APIMXEvent {
  id: string;
  type: string;
  organizationId: string;
  userId?: string;
  sessionId?: string;
  payload: any;
  timestamp: Date;
}

@Injectable()
export class APIMXService {
  constructor(
    private prisma: PrismaService,
    private gateway: APIMXGateway,
  ) {}

  async publishEvent(event: Omit<APIMXEvent, 'id' | 'timestamp'>) {
    const fullEvent: APIMXEvent = {
      ...event,
      id: this.generateEventId(),
      timestamp: new Date(),
    };

    await this.prisma.aPIMXEvent.create({
      data: {
        id: fullEvent.id,
        type: fullEvent.type,
        organizationId: fullEvent.organizationId,
        userId: fullEvent.userId,
        sessionId: fullEvent.sessionId,
        payload: fullEvent.payload,
        timestamp: fullEvent.timestamp,
      },
    });

    this.gateway.broadcastToOrganization(fullEvent.organizationId, fullEvent);

    if (fullEvent.userId) {
      this.gateway.broadcastToUser(fullEvent.userId, fullEvent);
    }

    if (fullEvent.sessionId) {
      this.gateway.broadcastToSession(fullEvent.sessionId, fullEvent);
    }

    return fullEvent;
  }

  async getEventHistory(organizationId: string, options?: {
    type?: string;
    userId?: string;
    sessionId?: string;
    limit?: number;
    offset?: number;
  }) {
    const { type, userId, sessionId, limit = 50, offset = 0 } = options || {};

    return this.prisma.aPIMXEvent.findMany({
      where: {
        organizationId,
        ...(type && { type }),
        ...(userId && { userId }),
        ...(sessionId && { sessionId }),
      },
      orderBy: { timestamp: 'desc' },
      take: limit,
      skip: offset,
    });
  }

  async cleanupOldEvents(daysToKeep = 30) {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);

    const deletedCount = await this.prisma.aPIMXEvent.deleteMany({
      where: {
        timestamp: {
          lt: cutoffDate,
        },
      },
    });

    return deletedCount.count;
  }

  private generateEventId(): string {
    return `apix_${Date.now()}_${Math.random().toString(36).substring(2)}`;
  }

  async publishAgentEvent(type: string, agentId: string, organizationId: string, data: any) {
    return this.publishEvent({
      type: `agent.${type}`,
      organizationId,
      payload: {
        agentId,
        ...data,
      },
    });
  }

  async publishToolEvent(type: string, toolId: string, organizationId: string, data: any) {
    return this.publishEvent({
      type: `tool.${type}`,
      organizationId,
      payload: {
        toolId,
        ...data,
      },
    });
  }

  async publishProviderEvent(type: string, providerId: string, organizationId: string, data: any) {
    return this.publishEvent({
      type: `provider.${type}`,
      organizationId,
      payload: {
        providerId,
        ...data,
      },
    });
  }

  async publishSessionEvent(type: string, sessionId: string, organizationId: string, userId: string, data: any) {
    return this.publishEvent({
      type: `session.${type}`,
      organizationId,
      userId,
      sessionId,
      payload: {
        sessionId,
        ...data,
      },
    });
  }

  async publishBillingEvent(type: string, organizationId: string, data: any) {
    return this.publishEvent({
      type: `billing.${type}`,
      organizationId,
      payload: data,
    });
  }

  async publishSystemEvent(type: string, organizationId: string, data: any) {
    return this.publishEvent({
      type: `system.${type}`,
      organizationId,
      payload: data,
    });
  }
}