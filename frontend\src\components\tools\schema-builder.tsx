'use client';

import React, { useState, useCallback } from 'react';
import { Plus, Trash2, Edit3, Copy, ChevronDown, ChevronRight } from 'lucide-react';

export interface SchemaProperty {
  name: string;
  type: 'string' | 'number' | 'boolean' | 'array' | 'object';
  description?: string;
  required?: boolean;
  default?: any;
  enum?: string[];
  properties?: Record<string, SchemaProperty>;
  items?: SchemaProperty;
}

export interface ToolSchema {
  type: 'object';
  properties: Record<string, SchemaProperty>;
  required: string[];
}

interface SchemaBuilderProps {
  schema: ToolSchema;
  onChange: (schema: ToolSchema) => void;
  className?: string;
}

const PropertyTypeSelect: React.FC<{
  value: SchemaProperty['type'];
  onChange: (type: SchemaProperty['type']) => void;
}> = ({ value, onChange }) => (
  <select
    value={value}
    onChange={(e) => onChange(e.target.value as SchemaProperty['type'])}
    className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
  >
    <option value="string">String</option>
    <option value="number">Number</option>
    <option value="boolean">Boolean</option>
    <option value="array">Array</option>
    <option value="object">Object</option>
  </select>
);

const PropertyEditor: React.FC<{
  name: string;
  property: SchemaProperty;
  isRequired: boolean;
  onUpdate: (updates: Partial<SchemaProperty>) => void;
  onDelete: () => void;
  onToggleRequired: () => void;
  level?: number;
}> = ({ name, property, isRequired, onUpdate, onDelete, onToggleRequired, level = 0 }) => {
  const [isExpanded, setIsExpanded] = useState(true);
  const [editingEnum, setEditingEnum] = useState(false);
  const [enumValue, setEnumValue] = useState('');

  const handleEnumAdd = () => {
    if (enumValue.trim()) {
      const newEnum = [...(property.enum || []), enumValue.trim()];
      onUpdate({ enum: newEnum });
      setEnumValue('');
    }
  };

  const handleEnumRemove = (index: number) => {
    const newEnum = property.enum?.filter((_, i) => i !== index);
    onUpdate({ enum: newEnum?.length ? newEnum : undefined });
  };

  const addNestedProperty = () => {
    if (property.type === 'object') {
      const newProperties = {
        ...property.properties,
        [`property_${Date.now()}`]: {
          name: 'newProperty',
          type: 'string' as const,
          description: '',
        },
      };
      onUpdate({ properties: newProperties });
    }
  };

  const updateNestedProperty = (propName: string, updates: Partial<SchemaProperty>) => {
    if (property.type === 'object' && property.properties) {
      const newProperties = {
        ...property.properties,
        [propName]: { ...property.properties[propName], ...updates },
      };
      onUpdate({ properties: newProperties });
    }
  };

  const deleteNestedProperty = (propName: string) => {
    if (property.type === 'object' && property.properties) {
      const newProperties = { ...property.properties };
      delete newProperties[propName];
      onUpdate({ properties: newProperties });
    }
  };

  return (
    <div className={`border border-gray-200 dark:border-gray-700 rounded-lg p-4 bg-gray-50 dark:bg-gray-800/50 ${level > 0 ? 'ml-4 mt-2' : ''}`}>
      <div className="flex items-center gap-3 mb-3">
        {(property.type === 'object' || property.type === 'array') && (
          <button
            onClick={() => setIsExpanded(!isExpanded)}
            className="p-1 hover:bg-gray-200 dark:hover:bg-gray-700 rounded"
          >
            {isExpanded ? (
              <ChevronDown className="h-4 w-4" />
            ) : (
              <ChevronRight className="h-4 w-4" />
            )}
          </button>
        )}
        
        <input
          type="text"
          value={name}
          onChange={(e) => onUpdate({ name: e.target.value })}
          className="font-medium px-2 py-1 border border-gray-300 dark:border-gray-600 rounded text-sm bg-white dark:bg-gray-800"
          placeholder="Property name"
        />
        
        <PropertyTypeSelect
          value={property.type}
          onChange={(type) => onUpdate({ type })}
        />

        <label className="flex items-center gap-1 text-sm">
          <input
            type="checkbox"
            checked={isRequired}
            onChange={onToggleRequired}
            className="rounded"
          />
          Required
        </label>

        <button
          onClick={onDelete}
          className="p-1 text-red-600 hover:bg-red-100 dark:hover:bg-red-900/20 rounded"
        >
          <Trash2 className="h-4 w-4" />
        </button>
      </div>

      {isExpanded && (
        <>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3 mb-3">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Description
              </label>
              <input
                type="text"
                value={property.description || ''}
                onChange={(e) => onUpdate({ description: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm bg-white dark:bg-gray-800"
                placeholder="Property description"
              />
            </div>

            {property.type !== 'object' && property.type !== 'array' && (
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Default Value
                </label>
                <input
                  type={property.type === 'number' ? 'number' : property.type === 'boolean' ? 'checkbox' : 'text'}
                  value={property.type === 'boolean' ? undefined : (property.default || '')}
                  checked={property.type === 'boolean' ? property.default : undefined}
                  onChange={(e) => {
                    let value: any = e.target.value;
                    if (property.type === 'number') value = parseFloat(value) || 0;
                    if (property.type === 'boolean') value = e.target.checked;
                    onUpdate({ default: value });
                  }}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm bg-white dark:bg-gray-800"
                  placeholder="Default value"
                />
              </div>
            )}
          </div>

          {/* Enum values for string types */}
          {property.type === 'string' && (
            <div className="mb-3">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Allowed Values (Enum)
              </label>
              <div className="flex flex-wrap gap-2 mb-2">
                {property.enum?.map((value, index) => (
                  <span
                    key={index}
                    className="inline-flex items-center gap-1 px-2 py-1 bg-blue-100 dark:bg-blue-900/20 text-blue-800 dark:text-blue-200 rounded text-sm"
                  >
                    {value}
                    <button
                      onClick={() => handleEnumRemove(index)}
                      className="text-blue-600 hover:text-blue-800"
                    >
                      <Trash2 className="h-3 w-3" />
                    </button>
                  </span>
                ))}
              </div>
              <div className="flex gap-2">
                <input
                  type="text"
                  value={enumValue}
                  onChange={(e) => setEnumValue(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && handleEnumAdd()}
                  className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm bg-white dark:bg-gray-800"
                  placeholder="Add enum value"
                />
                <button
                  onClick={handleEnumAdd}
                  className="px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-sm"
                >
                  Add
                </button>
              </div>
            </div>
          )}

          {/* Array items configuration */}
          {property.type === 'array' && (
            <div className="mb-3">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Array Item Type
              </label>
              <PropertyTypeSelect
                value={property.items?.type || 'string'}
                onChange={(type) => onUpdate({ items: { ...property.items, type } })}
              />
            </div>
          )}

          {/* Object properties */}
          {property.type === 'object' && (
            <div>
              <div className="flex justify-between items-center mb-2">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Object Properties
                </label>
                <button
                  onClick={addNestedProperty}
                  className="flex items-center gap-1 px-2 py-1 bg-green-600 text-white rounded text-sm hover:bg-green-700"
                >
                  <Plus className="h-3 w-3" />
                  Add Property
                </button>
              </div>
              
              {property.properties && Object.entries(property.properties).map(([propName, prop]) => (
                <PropertyEditor
                  key={propName}
                  name={propName}
                  property={prop}
                  isRequired={false} // Nested properties handle their own required status
                  onUpdate={(updates) => updateNestedProperty(propName, updates)}
                  onDelete={() => deleteNestedProperty(propName)}
                  onToggleRequired={() => {}} // Nested required handling would be more complex
                  level={level + 1}
                />
              ))}
            </div>
          )}
        </>
      )}
    </div>
  );
};

export const SchemaBuilder: React.FC<SchemaBuilderProps> = ({
  schema,
  onChange,
  className = '',
}) => {
  const addProperty = useCallback(() => {
    const newName = `property_${Date.now()}`;
    const newSchema = {
      ...schema,
      properties: {
        ...schema.properties,
        [newName]: {
          name: 'newProperty',
          type: 'string' as const,
          description: '',
        },
      },
    };
    onChange(newSchema);
  }, [schema, onChange]);

  const updateProperty = useCallback((propertyName: string, updates: Partial<SchemaProperty>) => {
    const newSchema = {
      ...schema,
      properties: {
        ...schema.properties,
        [propertyName]: { ...schema.properties[propertyName], ...updates },
      },
    };
    onChange(newSchema);
  }, [schema, onChange]);

  const deleteProperty = useCallback((propertyName: string) => {
    const newProperties = { ...schema.properties };
    delete newProperties[propertyName];
    
    const newRequired = schema.required.filter(name => name !== propertyName);
    
    onChange({
      ...schema,
      properties: newProperties,
      required: newRequired,
    });
  }, [schema, onChange]);

  const toggleRequired = useCallback((propertyName: string) => {
    const isRequired = schema.required.includes(propertyName);
    const newRequired = isRequired
      ? schema.required.filter(name => name !== propertyName)
      : [...schema.required, propertyName];
    
    onChange({
      ...schema,
      required: newRequired,
    });
  }, [schema, onChange]);

  const copySchema = useCallback(() => {
    navigator.clipboard.writeText(JSON.stringify(schema, null, 2));
  }, [schema]);

  return (
    <div className={`space-y-4 ${className}`}>
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white">
          Tool Input Schema
        </h3>
        <div className="flex gap-2">
          <button
            onClick={copySchema}
            className="flex items-center gap-1 px-3 py-2 text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-md text-sm"
          >
            <Copy className="h-4 w-4" />
            Copy JSON
          </button>
          <button
            onClick={addProperty}
            className="flex items-center gap-1 px-3 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 text-sm"
          >
            <Plus className="h-4 w-4" />
            Add Property
          </button>
        </div>
      </div>

      {Object.keys(schema.properties).length === 0 ? (
        <div className="text-center py-8 text-gray-500 dark:text-gray-400">
          <Edit3 className="h-12 w-12 mx-auto mb-3 opacity-50" />
          <p>No properties defined. Click "Add Property" to start building your schema.</p>
        </div>
      ) : (
        <div className="space-y-3">
          {Object.entries(schema.properties).map(([propertyName, property]) => (
            <PropertyEditor
              key={propertyName}
              name={propertyName}
              property={property}
              isRequired={schema.required.includes(propertyName)}
              onUpdate={(updates) => updateProperty(propertyName, updates)}
              onDelete={() => deleteProperty(propertyName)}
              onToggleRequired={() => toggleRequired(propertyName)}
            />
          ))}
        </div>
      )}

      {/* Schema Preview */}
      <div className="mt-6">
        <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Generated JSON Schema
        </h4>
        <pre className="p-4 bg-gray-100 dark:bg-gray-900 rounded-lg text-sm overflow-auto max-h-64 text-gray-800 dark:text-gray-200">
          {JSON.stringify(schema, null, 2)}
        </pre>
      </div>
    </div>
  );
};

export default SchemaBuilder;