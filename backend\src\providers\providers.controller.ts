import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  Request,
  HttpStatus,
  HttpCode,
  BadRequestException,
} from '@nestjs/common';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { ProvidersService } from './providers.service';
import { CircuitBreakerService } from './circuit-breaker.service';
import { ProviderExecutorService } from './provider-executor.service';
import {
  CreateProviderDto,
  UpdateProviderDto,
  ProviderQueryDto,
  ProviderSelectionDto,
  ProviderExecutionDto,
  ProviderHealthCheckDto,
  ProviderMetricsDto,
} from './dto/provider.dto';

@Controller('providers')
@UseGuards(JwtAuthGuard)
export class ProvidersController {
  constructor(
    private readonly providersService: ProvidersService,
    private readonly circuitBreakerService: CircuitBreakerService,
    private readonly providerExecutorService: ProviderExecutorService,
  ) {}

  @Post()
  @HttpCode(HttpStatus.CREATED)
  async create(@Request() req, @Body() createProviderDto: CreateProviderDto) {
    return this.providersService.create(req.user.organizationId, createProviderDto);
  }

  @Get()
  async findAll(@Request() req, @Query() query: ProviderQueryDto) {
    return this.providersService.findAll(req.user.organizationId, query);
  }

  @Get('stats')
  async getStats(@Request() req) {
    return this.providersService.getProviderStats(req.user.organizationId);
  }

  @Get('health')
  async getHealthSummary(@Request() req) {
    const providers = await this.providersService.findAll(req.user.organizationId, {
      status: 'active' as any,
    });

    const healthSummary = {
      totalProviders: providers.providers.length,
      healthyProviders: 0,
      unhealthyProviders: 0,
      circuitBreakerStats: {},
    };

    for (const provider of providers.providers) {
      const health = this.circuitBreakerService.getHealthStatus(provider.id);
      if (health.isHealthy) {
        healthSummary.healthyProviders++;
      } else {
        healthSummary.unhealthyProviders++;
      }
      healthSummary.circuitBreakerStats[provider.id] = health;
    }

    return healthSummary;
  }

  @Post('select')
  async selectOptimalProvider(
    @Request() req,
    @Body() selectionDto: ProviderSelectionDto,
  ) {
    return this.providersService.selectOptimalProvider(
      req.user.organizationId,
      selectionDto,
    );
  }

  @Post('execute')
  async executeProvider(@Request() req, @Body() executionDto: ProviderExecutionDto) {
    return this.providersService.executeProvider(
      req.user.organizationId,
      req.user.sub,
      executionDto,
    );
  }

  @Get('circuit-breaker')
  async getCircuitBreakerStats(@Request() req) {
    const providers = await this.providersService.findAll(req.user.organizationId, {});
    const stats = {};

    for (const provider of providers.providers) {
      stats[provider.id] = this.circuitBreakerService.getDetailedStats(provider.id);
    }

    return stats;
  }

  @Get(':id')
  async findOne(@Request() req, @Param('id') id: string) {
    return this.providersService.findOne(id, req.user.organizationId);
  }

  @Put(':id')
  async update(
    @Request() req,
    @Param('id') id: string,
    @Body() updateProviderDto: UpdateProviderDto,
  ) {
    return this.providersService.update(id, req.user.organizationId, updateProviderDto);
  }

  @Delete(':id')
  @HttpCode(HttpStatus.NO_CONTENT)
  async remove(@Request() req, @Param('id') id: string) {
    return this.providersService.remove(id, req.user.organizationId);
  }

  @Post(':id/health-check')
  async performHealthCheck(
    @Request() req,
    @Param('id') id: string,
    @Body() healthCheckDto?: ProviderHealthCheckDto,
  ) {
    return this.providersService.performHealthCheck(id, healthCheckDto);
  }

  @Get(':id/metrics')
  async getProviderMetrics(
    @Request() req,
    @Param('id') id: string,
    @Query() metricsDto: ProviderMetricsDto,
  ) {
    return this.providersService.getProviderMetrics(id, metricsDto);
  }

  @Get(':id/circuit-breaker')
  async getProviderCircuitBreaker(@Request() req, @Param('id') id: string) {
    const provider = await this.providersService.findOne(id, req.user.organizationId);
    return this.circuitBreakerService.getDetailedStats(id);
  }

  @Post(':id/circuit-breaker/reset')
  async resetCircuitBreaker(@Request() req, @Param('id') id: string) {
    const provider = await this.providersService.findOne(id, req.user.organizationId);
    this.circuitBreakerService.resetProvider(id);
    return { message: 'Circuit breaker reset successfully' };
  }

  @Post(':id/circuit-breaker/open')
  async openCircuitBreaker(@Request() req, @Param('id') id: string) {
    const provider = await this.providersService.findOne(id, req.user.organizationId);
    this.circuitBreakerService.forceOpen(id);
    return { message: 'Circuit breaker opened manually' };
  }

  @Post(':id/circuit-breaker/close')
  async closeCircuitBreaker(@Request() req, @Param('id') id: string) {
    const provider = await this.providersService.findOne(id, req.user.organizationId);
    this.circuitBreakerService.forceClose(id);
    return { message: 'Circuit breaker closed manually' };
  }

  @Post(':id/test')
  async testProvider(
    @Request() req,
    @Param('id') id: string,
    @Body() testData: { modelId?: string; prompt?: string },
  ) {
    const provider = await this.providersService.findOne(id, req.user.organizationId);
    
    let modelId = testData.modelId;
    const models = provider.models as any[];
    
    if (!modelId && models.length > 0) {
      modelId = models[0].id;
    }
    
    if (!modelId) {
      throw new BadRequestException('No model available for testing');
    }

    const model = models.find(m => m.id === modelId);
    if (!model) {
      throw new BadRequestException(`Model ${modelId} not found`);
    }

    const prompt = testData.prompt || 'This is a test message. Please respond briefly.';
    
    try {
      const isHealthy = await this.providerExecutorService.testModel(provider, model, prompt);
      return {
        success: isHealthy,
        message: isHealthy ? 'Provider test successful' : 'Provider test failed',
        providerId: id,
        modelId,
        testPrompt: prompt,
      };
    } catch (error) {
      return {
        success: false,
        message: `Provider test failed: ${error.message}`,
        providerId: id,
        modelId,
        testPrompt: prompt,
        error: error.message,
      };
    }
  }

  @Post(':id/validate')
  async validateConnection(@Request() req, @Param('id') id: string) {
    const provider = await this.providersService.findOne(id, req.user.organizationId);
    
    try {
      const isValid = await this.providerExecutorService.validateProviderConnection(provider);
      return {
        valid: isValid,
        message: isValid ? 'Provider connection is valid' : 'Provider connection failed',
        providerId: id,
        providerName: provider.name,
        providerType: provider.type,
      };
    } catch (error) {
      return {
        valid: false,
        message: `Connection validation failed: ${error.message}`,
        providerId: id,
        providerName: provider.name,
        providerType: provider.type,
        error: error.message,
      };
    }
  }

  @Get(':id/models')
  async getProviderModels(@Request() req, @Param('id') id: string) {
    const provider = await this.providersService.findOne(id, req.user.organizationId);
    return {
      providerId: id,
      providerName: provider.name,
      models: provider.models,
    };
  }

  @Post(':id/models/:modelId/test')
  async testSpecificModel(
    @Request() req,
    @Param('id') id: string,
    @Param('modelId') modelId: string,
    @Body() testData: { prompt?: string },
  ) {
    const provider = await this.providersService.findOne(id, req.user.organizationId);
    const models = provider.models as any[];
    const model = models.find(m => m.id === modelId);
    
    if (!model) {
      throw new BadRequestException(`Model ${modelId} not found`);
    }

    const prompt = testData.prompt || 'This is a test message for model testing.';
    
    try {
      const isHealthy = await this.providerExecutorService.testModel(provider, model, prompt);
      return {
        success: isHealthy,
        message: isHealthy ? 'Model test successful' : 'Model test failed',
        providerId: id,
        modelId,
        modelName: model.name,
        testPrompt: prompt,
      };
    } catch (error) {
      return {
        success: false,
        message: `Model test failed: ${error.message}`,
        providerId: id,
        modelId,
        modelName: model.name,
        testPrompt: prompt,
        error: error.message,
      };
    }
  }

  @Get(':id/usage')
  async getProviderUsage(
    @Request() req,
    @Param('id') id: string,
    @Query('days') days: string = '30',
  ) {
    const provider = await this.providersService.findOne(id, req.user.organizationId);
    const daysNum = parseInt(days) || 30;
    const since = new Date(Date.now() - daysNum * 24 * 60 * 60 * 1000);

    // Get usage data from billing
    const usage = await this.providersService['prisma'].billingUsage.findMany({
      where: {
        organizationId: req.user.organizationId,
        type: 'provider_execution',
        metadata: {
          path: ['providerId'],
          equals: id,
        },
        createdAt: { gte: since },
      },
      orderBy: { createdAt: 'desc' },
    });

    const summary = {
      providerId: id,
      providerName: provider.name,
      timeRange: `${daysNum} days`,
      totalExecutions: usage.length,
      totalCost: usage.reduce((sum, u) => sum + u.cost, 0),
      totalTokens: usage.reduce((sum, u) => {
        const tokens = u.metadata?.totalTokens || 0;
        return sum + tokens;
      }, 0),
      avgCostPerExecution: usage.length > 0 
        ? usage.reduce((sum, u) => sum + u.cost, 0) / usage.length 
        : 0,
      executions: usage.map(u => ({
        id: u.id,
        cost: u.cost,
        tokens: u.metadata?.totalTokens || 0,
        modelId: u.metadata?.modelId,
        executionTime: u.metadata?.executionTime,
        timestamp: u.createdAt,
      })),
    };

    return summary;
  }

  @Post('bulk-health-check')
  async bulkHealthCheck(@Request() req) {
    const providers = await this.providersService.findAll(req.user.organizationId, {
      status: 'active' as any,
    });

    const results = [];
    
    for (const provider of providers.providers) {
      try {
        const health = await this.providersService.performHealthCheck(provider.id);
        results.push({
          providerId: provider.id,
          providerName: provider.name,
          success: true,
          health,
        });
      } catch (error) {
        results.push({
          providerId: provider.id,
          providerName: provider.name,
          success: false,
          error: error.message,
        });
      }
    }

    return {
      totalProviders: providers.providers.length,
      results,
      summary: {
        healthy: results.filter(r => r.success && r.health?.status === 'active').length,
        unhealthy: results.filter(r => !r.success || r.health?.status !== 'active').length,
      },
    };
  }

  @Get('recommendations/cost-optimization')
  async getCostOptimizationRecommendations(@Request() req) {
    const providers = await this.providersService.findAll(req.user.organizationId, {});
    const recommendations = [];

    for (const provider of providers.providers) {
      try {
        const metrics = await this.providersService.getProviderMetrics(provider.id, {
          timeRange: '30d',
          includeCosts: true,
        });

        // Analyze cost patterns and suggest optimizations
        const costPerToken = metrics.totalCost / Math.max(metrics.totalExecutions, 1);
        const models = provider.models as any[];
        
        // Find cheaper alternatives
        const cheaperModels = models
          .filter(m => m.inputCostPer1KTokens < costPerToken * 1000)
          .sort((a, b) => a.inputCostPer1KTokens - b.inputCostPer1KTokens);

        if (cheaperModels.length > 0) {
          recommendations.push({
            type: 'cost_optimization',
            providerId: provider.id,
            providerName: provider.name,
            currentCostPerToken: costPerToken,
            recommendation: `Consider using ${cheaperModels[0].name} for cost savings`,
            potentialSavings: Math.max(0, costPerToken - (cheaperModels[0].inputCostPer1KTokens / 1000)),
            alternativeModel: cheaperModels[0],
          });
        }

        // Suggest provider switching based on success rate
        if (metrics.successRate < 95) {
          recommendations.push({
            type: 'reliability_improvement',
            providerId: provider.id,
            providerName: provider.name,
            currentSuccessRate: metrics.successRate,
            recommendation: 'Consider switching to a more reliable provider',
            issue: 'Low success rate detected',
          });
        }

      } catch (error) {
        // Skip providers with no metrics
      }
    }

    return {
      totalRecommendations: recommendations.length,
      recommendations,
      summary: {
        costOptimizations: recommendations.filter(r => r.type === 'cost_optimization').length,
        reliabilityImprovements: recommendations.filter(r => r.type === 'reliability_improvement').length,
      },
    };
  }
}