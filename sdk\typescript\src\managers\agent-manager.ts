import { 
  Agent, 
  CreateAgentRequest, 
  ExecuteAgentRequest, 
  ExecuteAgentResponse,
  PaginatedResponse,
  QueryParams,
  StreamingChunk
} from '../types';
import { BaseManager } from './base-manager';

export interface AgentQueryParams extends QueryParams {
  status?: 'draft' | 'active' | 'archived';
  tags?: string[];
}

export interface AgentExecutionStream {
  sessionId: string;
  subscribe(callback: (chunk: StreamingChunk) => void): void;
  unsubscribe(): void;
  close(): void;
}

export class AgentManager extends BaseManager {
  /**
   * Create a new agent
   */
  async create(agent: CreateAgentRequest): Promise<Agent> {
    return this.client.request({
      method: 'POST',
      url: '/agents',
      data: agent,
    });
  }

  /**
   * Get all agents
   */
  async list(params?: AgentQueryParams): Promise<PaginatedResponse<Agent>> {
    return this.client.request({
      method: 'GET',
      url: '/agents',
      params,
    });
  }

  /**
   * Get a specific agent by ID
   */
  async get(id: string): Promise<Agent> {
    return this.client.request({
      method: 'GET',
      url: `/agents/${id}`,
    });
  }

  /**
   * Update an agent
   */
  async update(id: string, updates: Partial<CreateAgentRequest>): Promise<Agent> {
    return this.client.request({
      method: 'PUT',
      url: `/agents/${id}`,
      data: updates,
    });
  }

  /**
   * Delete an agent
   */
  async delete(id: string): Promise<void> {
    await this.client.request({
      method: 'DELETE',
      url: `/agents/${id}`,
    });
  }

  /**
   * Execute an agent with streaming support
   */
  async execute(id: string, request: ExecuteAgentRequest): Promise<ExecuteAgentResponse> {
    if (request.stream) {
      throw new Error('Use executeStream() for streaming responses');
    }

    return this.client.request({
      method: 'POST',
      url: `/agents/${id}/execute`,
      data: request,
    });
  }

  /**
   * Execute an agent with streaming response
   */
  async executeStream(id: string, request: ExecuteAgentRequest): Promise<AgentExecutionStream> {
    const streamRequest = { ...request, stream: true };
    
    // Start the streaming execution
    const response = await this.client.request({
      method: 'POST',
      url: `/agents/${id}/execute-stream`,
      data: streamRequest,
    });

    const sessionId = response.sessionId;
    let subscriptionId: string | null = null;
    const callbacks = new Set<(chunk: StreamingChunk) => void>();

    const stream: AgentExecutionStream = {
      sessionId,
      
      subscribe(callback: (chunk: StreamingChunk) => void): void {
        callbacks.add(callback);
        
        // Subscribe to real-time events for this session if not already subscribed
        if (!subscriptionId) {
          subscriptionId = this.client.subscribe({
            eventTypes: ['session.message_added', 'session.updated'],
            sessionId,
            callback: (event) => {
              if (event.type === 'session.message_added' && event.payload.role === 'assistant') {
                const chunk: StreamingChunk = {
                  type: 'chunk',
                  content: event.payload.content,
                  sessionId,
                };
                callbacks.forEach(cb => cb(chunk));
              } else if (event.type === 'session.updated' && event.payload.status === 'completed') {
                const chunk: StreamingChunk = {
                  type: 'end',
                  sessionId,
                  usage: event.payload.usage,
                };
                callbacks.forEach(cb => cb(chunk));
              }
            },
          });
        }
      },

      unsubscribe(): void {
        callbacks.clear();
        if (subscriptionId) {
          this.client.unsubscribe(subscriptionId);
          subscriptionId = null;
        }
      },

      close(): void {
        this.unsubscribe();
      },
    };

    return stream;
  }

  /**
   * Clone an existing agent
   */
  async clone(id: string, newName: string, description?: string): Promise<Agent> {
    return this.client.request({
      method: 'POST',
      url: `/agents/${id}/clone`,
      data: {
        name: newName,
        description,
      },
    });
  }

  /**
   * Create a new version of an agent
   */
  async createVersion(id: string, version: string, description: string): Promise<Agent> {
    return this.client.request({
      method: 'POST',
      url: `/agents/${id}/versions`,
      data: {
        version,
        description,
      },
    });
  }

  /**
   * Get all versions of an agent
   */
  async getVersions(id: string): Promise<Array<{
    version: string;
    description: string;
    createdAt: string;
    metadata: any;
  }>> {
    const agent = await this.get(id);
    return (agent.metadata as any).versions || [];
  }

  /**
   * Rollback to a specific version
   */
  async rollbackToVersion(id: string, version: string): Promise<Agent> {
    return this.client.request({
      method: 'PUT',
      url: `/agents/${id}/versions/${version}/rollback`,
    });
  }

  /**
   * Get execution history for an agent
   */
  async getExecutionHistory(id: string, limit = 50): Promise<Array<{
    id: string;
    sessionId: string;
    input: string;
    output: string;
    usage: any;
    executionTime: number;
    createdAt: string;
  }>> {
    return this.client.request({
      method: 'GET',
      url: `/agents/${id}/executions`,
      params: { limit },
    });
  }

  /**
   * Get performance metrics for an agent
   */
  async getMetrics(id: string, timeRange = '24h'): Promise<{
    totalExecutions: number;
    avgExecutionTime: number;
    successRate: number;
    totalCost: number;
    tokensUsed: number;
    lastExecutedAt?: string;
    executionsByHour: Array<{
      hour: number;
      executions: number;
      avgTime: number;
    }>;
  }> {
    return this.client.request({
      method: 'GET',
      url: `/agents/${id}/metrics`,
      params: { timeRange },
    });
  }

  /**
   * Test an agent with sample input
   */
  async test(id: string, input: string): Promise<ExecuteAgentResponse> {
    return this.execute(id, {
      input,
      context: { isTest: true },
    });
  }

  /**
   * Get agent suggestions based on description
   */
  async getSuggestions(description: string): Promise<Array<{
    name: string;
    instructions: string;
    config: any;
    confidence: number;
  }>> {
    return this.client.request({
      method: 'POST',
      url: '/agents/suggestions',
      data: { description },
    });
  }

  /**
   * Export an agent configuration
   */
  async export(id: string): Promise<{
    agent: Agent;
    template: any;
    exportedAt: string;
  }> {
    return this.client.request({
      method: 'GET',
      url: `/agents/${id}/export`,
    });
  }

  /**
   * Import an agent from exported configuration
   */
  async import(exportedAgent: any, name?: string): Promise<Agent> {
    return this.client.request({
      method: 'POST',
      url: '/agents/import',
      data: {
        ...exportedAgent,
        ...(name && { name }),
      },
    });
  }

  /**
   * Get agent statistics for the organization
   */
  async getStats(): Promise<{
    total: number;
    active: number;
    draft: number;
    archived: number;
    totalExecutions: number;
    totalCost: number;
  }> {
    return this.client.request({
      method: 'GET',
      url: '/agents/stats',
    });
  }

  /**
   * Search agents by content
   */
  async search(query: string, filters?: {
    status?: string[];
    tags?: string[];
    createdAfter?: string;
    createdBefore?: string;
  }): Promise<PaginatedResponse<Agent>> {
    return this.client.request({
      method: 'POST',
      url: '/agents/search',
      data: {
        query,
        filters,
      },
    });
  }

  /**
   * Batch update multiple agents
   */
  async batchUpdate(updates: Array<{
    id: string;
    updates: Partial<CreateAgentRequest>;
  }>): Promise<Agent[]> {
    return this.client.request({
      method: 'PUT',
      url: '/agents/batch',
      data: { updates },
    });
  }

  /**
   * Archive multiple agents
   */
  async batchArchive(ids: string[]): Promise<void> {
    await this.client.request({
      method: 'PUT',
      url: '/agents/batch/archive',
      data: { ids },
    });
  }

  /**
   * Subscribe to agent events
   */
  onAgentEvent(agentId: string, callback: (event: any) => void): string {
    return this.client.subscribe({
      eventTypes: ['agent.executed', 'agent.updated', 'agent.deleted'],
      organizationId: this.client.getConfig().organizationId,
      callback: (event) => {
        if (event.payload.agentId === agentId) {
          callback(event);
        }
      },
    });
  }

  /**
   * Subscribe to all agent events in the organization
   */
  onAllAgentEvents(callback: (event: any) => void): string {
    return this.client.subscribe({
      eventTypes: ['agent.created', 'agent.updated', 'agent.executed', 'agent.deleted'],
      organizationId: this.client.getConfig().organizationId,
      callback,
    });
  }
}