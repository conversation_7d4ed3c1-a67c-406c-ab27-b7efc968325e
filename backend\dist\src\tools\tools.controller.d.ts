import { ToolsService } from './tools.service';
import { ToolExecutorService } from './tool-executor.service';
import { CreateToolDto, UpdateToolDto, ToolQueryDto, ExecuteToolDto, TestToolDto, ToolVersionDto } from './dto/tool.dto';
export declare class ToolsController {
    private readonly toolsService;
    private readonly toolExecutorService;
    constructor(toolsService: ToolsService, toolExecutorService: ToolExecutorService);
    create(req: any, createToolDto: CreateToolDto): Promise<any>;
    findAll(req: any, query: ToolQueryDto): Promise<{
        tools: any;
        pagination: {
            page: number;
            limit: number;
            total: any;
            pages: number;
        };
    }>;
    getStats(req: any): Promise<{
        total: any;
        executions: any;
        byType: any;
        byStatus: any;
    }>;
    getPopular(req: any, limit?: number): Promise<any>;
    searchByTags(req: any, tags: string[]): Promise<any>;
    getByCategory(req: any, category: string): Promise<any>;
    findOne(req: any, id: string): Promise<any>;
    update(req: any, id: string, updateToolDto: UpdateToolDto): Promise<any>;
    remove(req: any, id: string): Promise<{
        message: string;
    }>;
    execute(req: any, id: string, executeDto: ExecuteToolDto): Promise<import("./tool-executor.service").ToolExecutionResult>;
    test(req: any, id: string, testDto: TestToolDto): Promise<import("./tool-executor.service").ToolExecutionResult>;
    createVersion(req: any, id: string, versionDto: ToolVersionDto): Promise<any>;
    rollbackToVersion(req: any, id: string, version: string): Promise<any>;
    clone(req: any, id: string, cloneData: {
        name: string;
        description?: string;
    }): Promise<any>;
    getExecutionHistory(req: any, id: string, limit?: number): Promise<any>;
    getExecutionMetrics(req: any, id: string): Promise<{
        totalExecutions: any;
        avgExecutionTime: any;
        successRate: number;
        lastExecutedAt: any;
    }>;
}
