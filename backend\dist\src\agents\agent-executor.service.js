"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AgentExecutorService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
const apix_service_1 = require("../apix/apix.service");
const sessions_service_1 = require("../sessions/sessions.service");
const openai_1 = require("openai");
const config_1 = require("@nestjs/config");
let AgentExecutorService = class AgentExecutorService {
    constructor(prisma, apixService, sessionsService, configService) {
        this.prisma = prisma;
        this.apixService = apixService;
        this.sessionsService = sessionsService;
        this.configService = configService;
        this.openai = new openai_1.default({
            apiKey: this.configService.get('OPENAI_API_KEY'),
        });
    }
    async executeAgent(agentId, organizationId, userId, executeDto) {
        const startTime = Date.now();
        try {
            const agent = await this.prisma.agent.findFirst({
                where: {
                    id: agentId,
                    organizationId,
                    status: 'active',
                },
            });
            if (!agent) {
                throw new common_1.NotFoundException('Agent not found or not active');
            }
            const config = agent.config;
            let sessionId = executeDto.sessionId;
            if (!sessionId) {
                const session = await this.sessionsService.create(userId, organizationId, {
                    type: 'agent',
                    targetId: agentId,
                    metadata: {
                        agentName: agent.name,
                        model: config.model,
                    },
                });
                sessionId = session.id;
            }
            const session = await this.sessionsService.findOne(sessionId, organizationId, userId);
            const sessionMemory = session.memory;
            const messages = this.prepareMessages(config, executeDto.input, sessionMemory.messages, executeDto.context);
            const result = await this.executeWithProvider(config, messages, executeDto.overrides);
            await this.sessionsService.addMessage(sessionId, organizationId, userId, {
                role: 'user',
                content: executeDto.input,
                metadata: executeDto.context,
            });
            await this.sessionsService.addMessage(sessionId, organizationId, userId, {
                role: 'assistant',
                content: result.output,
                metadata: {
                    usage: result.usage,
                    model: config.model,
                    executionTime: result.executionTime,
                },
            });
            if (executeDto.context) {
                await this.sessionsService.updateContext(sessionId, organizationId, userId, executeDto.context);
            }
            await this.trackUsage(organizationId, agentId, result.usage);
            await this.apixService.publishAgentEvent('execution_completed', agentId, organizationId, {
                sessionId,
                usage: result.usage,
                executionTime: result.executionTime,
            });
            return {
                ...result,
                sessionId,
                executionTime: Date.now() - startTime,
            };
        }
        catch (error) {
            const executionTime = Date.now() - startTime;
            await this.apixService.publishAgentEvent('execution_failed', agentId, organizationId, {
                error: error.message,
                executionTime,
            });
            throw error;
        }
    }
    async *executeAgentStreaming(agentId, organizationId, userId, executeDto) {
        try {
            const agent = await this.prisma.agent.findFirst({
                where: {
                    id: agentId,
                    organizationId,
                    status: 'active',
                },
            });
            if (!agent) {
                throw new common_1.NotFoundException('Agent not found or not active');
            }
            const config = agent.config;
            let sessionId = executeDto.sessionId;
            if (!sessionId) {
                const session = await this.sessionsService.create(userId, organizationId, {
                    type: 'agent',
                    targetId: agentId,
                    metadata: {
                        agentName: agent.name,
                        model: config.model,
                    },
                });
                sessionId = session.id;
            }
            yield { type: 'start', sessionId };
            const session = await this.sessionsService.findOne(sessionId, organizationId, userId);
            const sessionMemory = session.memory;
            const messages = this.prepareMessages(config, executeDto.input, sessionMemory.messages, executeDto.context);
            let fullContent = '';
            const stream = await this.openai.chat.completions.create({
                model: executeDto.overrides?.model || config.model,
                messages,
                temperature: executeDto.overrides?.temperature ?? config.temperature,
                max_tokens: executeDto.overrides?.maxTokens ?? config.maxTokens,
                stream: true,
            });
            for await (const chunk of stream) {
                const content = chunk.choices[0]?.delta?.content || '';
                if (content) {
                    fullContent += content;
                    yield { type: 'chunk', content };
                }
            }
            await this.sessionsService.addMessage(sessionId, organizationId, userId, {
                role: 'user',
                content: executeDto.input,
                metadata: executeDto.context,
            });
            await this.sessionsService.addMessage(sessionId, organizationId, userId, {
                role: 'assistant',
                content: fullContent,
                metadata: {
                    model: config.model,
                    streaming: true,
                },
            });
            yield { type: 'end', sessionId };
            await this.apixService.publishAgentEvent('execution_completed', agentId, organizationId, {
                sessionId,
                streaming: true,
            });
        }
        catch (error) {
            yield { type: 'error', error: error.message };
            await this.apixService.publishAgentEvent('execution_failed', agentId, organizationId, {
                error: error.message,
                streaming: true,
            });
        }
    }
    prepareMessages(config, input, sessionMessages, context) {
        const messages = [];
        if (config.systemPrompt) {
            messages.push({
                role: 'system',
                content: config.systemPrompt,
            });
        }
        let promptContent = config.prompt;
        if (context) {
            Object.entries(context).forEach(([key, value]) => {
                const placeholder = `{{${key}}}`;
                promptContent = promptContent.replace(new RegExp(placeholder, 'g'), String(value));
            });
        }
        messages.push({
            role: 'system',
            content: promptContent,
        });
        if (config.memory?.enabled && sessionMessages.length > 0) {
            const memoryStrategy = config.memory.strategy || 'rolling';
            const maxMessages = config.memory.maxMessages || 50;
            let messagesToInclude = sessionMessages;
            if (memoryStrategy === 'rolling') {
                messagesToInclude = sessionMessages.slice(-maxMessages);
            }
            else if (memoryStrategy === 'summarize' && sessionMessages.length > maxMessages) {
                messagesToInclude = sessionMessages.slice(-maxMessages);
            }
            messagesToInclude.forEach((msg) => {
                if (msg.role !== 'system') {
                    messages.push({
                        role: msg.role,
                        content: msg.content,
                    });
                }
            });
        }
        messages.push({
            role: 'user',
            content: input,
        });
        return messages;
    }
    async executeWithProvider(config, messages, overrides) {
        const startTime = Date.now();
        try {
            const response = await this.openai.chat.completions.create({
                model: overrides?.model || config.model,
                messages,
                temperature: overrides?.temperature ?? config.temperature,
                max_tokens: overrides?.maxTokens ?? config.maxTokens,
            });
            const output = response.choices[0]?.message?.content || '';
            const usage = response.usage;
            return {
                success: true,
                output,
                usage: usage ? {
                    promptTokens: usage.prompt_tokens,
                    completionTokens: usage.completion_tokens,
                    totalTokens: usage.total_tokens,
                } : undefined,
                executionTime: Date.now() - startTime,
                sessionId: '',
            };
        }
        catch (error) {
            return {
                success: false,
                output: '',
                error: error.message,
                executionTime: Date.now() - startTime,
                sessionId: '',
            };
        }
    }
    async trackUsage(organizationId, agentId, usage) {
        if (!usage)
            return;
        await this.prisma.billingUsage.create({
            data: {
                organizationId,
                type: 'agent_execution',
                quantity: usage.totalTokens,
                cost: this.calculateCost(usage.totalTokens),
                metadata: {
                    agentId,
                    promptTokens: usage.promptTokens,
                    completionTokens: usage.completionTokens,
                },
            },
        });
        await this.prisma.quota.upsert({
            where: {
                organizationId_type: {
                    organizationId,
                    type: 'api_calls',
                },
            },
            create: {
                organizationId,
                type: 'api_calls',
                limit: 1000,
                used: 1,
                resetAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
            },
            update: {
                used: {
                    increment: 1,
                },
            },
        });
    }
    calculateCost(tokens) {
        return (tokens / 1000) * 0.002;
    }
    async getExecutionHistory(agentId, organizationId, limit = 50) {
        return this.prisma.session.findMany({
            where: {
                organizationId,
                type: 'agent',
                metadata: {
                    path: ['targetId'],
                    equals: agentId,
                },
            },
            include: {
                user: {
                    select: {
                        id: true,
                        name: true,
                        email: true,
                    },
                },
            },
            orderBy: { createdAt: 'desc' },
            take: limit,
        });
    }
    async getExecutionMetrics(agentId, organizationId) {
        const [totalExecutions, avgExecutionTime, totalTokens, lastExecution] = await Promise.all([
            this.prisma.session.count({
                where: {
                    organizationId,
                    type: 'agent',
                    metadata: {
                        path: ['targetId'],
                        equals: agentId,
                    },
                },
            }),
            this.prisma.billingUsage.aggregate({
                where: {
                    organizationId,
                    type: 'agent_execution',
                    metadata: {
                        path: ['agentId'],
                        equals: agentId,
                    },
                },
                _avg: {
                    quantity: true,
                },
            }),
            this.prisma.billingUsage.aggregate({
                where: {
                    organizationId,
                    type: 'agent_execution',
                    metadata: {
                        path: ['agentId'],
                        equals: agentId,
                    },
                },
                _sum: {
                    quantity: true,
                },
            }),
            this.prisma.session.findFirst({
                where: {
                    organizationId,
                    type: 'agent',
                    metadata: {
                        path: ['targetId'],
                        equals: agentId,
                    },
                },
                orderBy: { createdAt: 'desc' },
            }),
        ]);
        return {
            totalExecutions,
            avgTokensPerExecution: avgExecutionTime._avg.quantity || 0,
            totalTokensUsed: totalTokens._sum.quantity || 0,
            lastExecutedAt: lastExecution?.createdAt,
        };
    }
};
exports.AgentExecutorService = AgentExecutorService;
exports.AgentExecutorService = AgentExecutorService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService,
        apix_service_1.APIMXService,
        sessions_service_1.SessionsService, typeof (_a = typeof config_1.ConfigService !== "undefined" && config_1.ConfigService) === "function" ? _a : Object])
], AgentExecutorService);
//# sourceMappingURL=agent-executor.service.js.map