"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ToolExecutorService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
const apix_service_1 = require("../apix/apix.service");
const sessions_service_1 = require("../sessions/sessions.service");
const tools_service_1 = require("./tools.service");
const config_1 = require("@nestjs/config");
const axios_1 = require("axios");
let ToolExecutorService = class ToolExecutorService {
    constructor(prisma, apixService, sessionsService, toolsService, configService) {
        this.prisma = prisma;
        this.apixService = apixService;
        this.sessionsService = sessionsService;
        this.toolsService = toolsService;
        this.configService = configService;
    }
    async executeTool(toolId, organizationId, userId, executeDto) {
        const startTime = Date.now();
        try {
            const tool = await this.prisma.tool.findFirst({
                where: {
                    id: toolId,
                    organizationId,
                    status: 'active',
                },
            });
            if (!tool) {
                throw new common_1.NotFoundException('Tool not found or not active');
            }
            const config = tool.config;
            const schema = tool.schema;
            if (executeDto.validateInput) {
                this.validateInput(executeDto.input, schema.input);
            }
            let sessionId = executeDto.sessionId;
            if (!sessionId) {
                const session = await this.sessionsService.create(userId, organizationId, {
                    type: 'tool',
                    targetId: toolId,
                    metadata: {
                        toolName: tool.name,
                        toolType: tool.type,
                    },
                });
                sessionId = session.id;
            }
            let result;
            switch (tool.type) {
                case 'api':
                    result = await this.executeApiTool(config, executeDto.input, executeDto.overrides);
                    break;
                case 'function':
                    result = await this.executeFunctionTool(config, executeDto.input);
                    break;
                case 'workflow':
                    result = await this.executeWorkflowTool(config, executeDto.input, organizationId, userId);
                    break;
                default:
                    throw new common_1.BadRequestException('Unsupported tool type');
            }
            result.sessionId = sessionId;
            result.executionTime = Date.now() - startTime;
            await this.sessionsService.addMessage(sessionId, organizationId, userId, {
                role: 'user',
                content: JSON.stringify(executeDto.input),
                metadata: { toolId, type: 'tool_input' },
            });
            await this.sessionsService.addMessage(sessionId, organizationId, userId, {
                role: 'assistant',
                content: JSON.stringify(result.output),
                metadata: {
                    toolId,
                    type: 'tool_output',
                    executionTime: result.executionTime,
                    success: result.success,
                },
            });
            await this.trackUsage(organizationId, toolId, result);
            await this.toolsService.incrementExecutionCount(toolId);
            await this.apixService.publishToolEvent('execution_completed', toolId, organizationId, {
                sessionId,
                success: result.success,
                executionTime: result.executionTime,
            });
            return result;
        }
        catch (error) {
            const executionTime = Date.now() - startTime;
            await this.apixService.publishToolEvent('execution_failed', toolId, organizationId, {
                error: error.message,
                executionTime,
            });
            throw error;
        }
    }
    async testTool(toolId, organizationId, testDto) {
        const tool = await this.toolsService.findOne(toolId, organizationId);
        const config = tool.config;
        const schema = tool.schema;
        this.validateInput(testDto.input, schema.input);
        if (testDto.dryRun) {
            return {
                success: true,
                output: { message: 'Dry run successful', input: testDto.input },
                executionTime: 0,
            };
        }
        switch (tool.type) {
            case 'api':
                return this.executeApiTool(config, testDto.input);
            case 'function':
                return this.executeFunctionTool(config, testDto.input);
            case 'workflow':
                throw new common_1.BadRequestException('Workflow tools cannot be tested directly');
            default:
                throw new common_1.BadRequestException('Unsupported tool type');
        }
    }
    async executeApiTool(config, input, overrides) {
        const startTime = Date.now();
        try {
            const requestConfig = {
                method: config.method,
                url: this.interpolateUrl(config.endpoint, input),
                timeout: overrides?.timeout || config.timeout || 30000,
                headers: {
                    ...config.headers,
                    ...overrides?.headers,
                },
            };
            if (config.authentication && config.authentication.type !== 'none') {
                this.addAuthentication(requestConfig, config.authentication);
            }
            if (['POST', 'PUT', 'PATCH'].includes(config.method)) {
                requestConfig.data = input;
                requestConfig.headers['Content-Type'] = requestConfig.headers['Content-Type'] || 'application/json';
            }
            else if (config.method === 'GET') {
                requestConfig.params = input;
            }
            let lastError;
            const retries = overrides?.retries ?? config.retries ?? 3;
            for (let attempt = 0; attempt <= retries; attempt++) {
                try {
                    const response = await (0, axios_1.default)(requestConfig);
                    return {
                        success: true,
                        output: response.data,
                        executionTime: Date.now() - startTime,
                        metadata: {
                            httpStatus: response.status,
                            headers: response.headers,
                            retryCount: attempt,
                        },
                    };
                }
                catch (error) {
                    lastError = error;
                    if (error.response?.status >= 400 && error.response?.status < 500) {
                        break;
                    }
                    if (attempt < retries) {
                        await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt) * 1000));
                    }
                }
            }
            return {
                success: false,
                output: null,
                executionTime: Date.now() - startTime,
                error: lastError.message,
                metadata: {
                    httpStatus: lastError.response?.status,
                    retryCount: retries,
                },
            };
        }
        catch (error) {
            return {
                success: false,
                output: null,
                executionTime: Date.now() - startTime,
                error: error.message,
            };
        }
    }
    async executeFunctionTool(config, input) {
        const startTime = Date.now();
        try {
            const context = {
                input,
                console: {
                    log: (...args) => console.log('[Function Tool]', ...args),
                },
            };
            const functionCode = `
        return (function(context) {
          const { input } = context;
          ${config.functionCode}
        })(context);
      `;
            const result = eval(functionCode);
            return {
                success: true,
                output: result,
                executionTime: Date.now() - startTime,
            };
        }
        catch (error) {
            return {
                success: false,
                output: null,
                executionTime: Date.now() - startTime,
                error: error.message,
            };
        }
    }
    async executeWorkflowTool(config, input, organizationId, userId) {
        const startTime = Date.now();
        try {
            const steps = config.workflowSteps;
            let currentInput = input;
            const executionLog = [];
            for (const step of steps) {
                const stepResult = await this.executeWorkflowStep(step, currentInput, organizationId, userId);
                executionLog.push({
                    stepId: step.id,
                    input: currentInput,
                    output: stepResult.output,
                    success: stepResult.success,
                    executionTime: stepResult.executionTime,
                });
                if (!stepResult.success) {
                    return {
                        success: false,
                        output: { executionLog, error: stepResult.error },
                        executionTime: Date.now() - startTime,
                        error: `Step ${step.id} failed: ${stepResult.error}`,
                    };
                }
                currentInput = stepResult.output;
            }
            return {
                success: true,
                output: { result: currentInput, executionLog },
                executionTime: Date.now() - startTime,
            };
        }
        catch (error) {
            return {
                success: false,
                output: null,
                executionTime: Date.now() - startTime,
                error: error.message,
            };
        }
    }
    async executeWorkflowStep(step, input, organizationId, userId) {
        switch (step.type) {
            case 'api':
                return this.executeApiTool(step.config, input);
            case 'function':
                return this.executeFunctionTool(step.config, input);
            case 'condition':
                return this.executeConditionStep(step.config, input);
            default:
                throw new common_1.BadRequestException(`Unsupported workflow step type: ${step.type}`);
        }
    }
    async executeConditionStep(config, input) {
        const startTime = Date.now();
        try {
            const condition = config.condition;
            const result = this.evaluateCondition(condition, input);
            return {
                success: true,
                output: { condition: result, input },
                executionTime: Date.now() - startTime,
            };
        }
        catch (error) {
            return {
                success: false,
                output: null,
                executionTime: Date.now() - startTime,
                error: error.message,
            };
        }
    }
    evaluateCondition(condition, input) {
        try {
            const context = { input };
            return eval(`(function(input) { return ${condition}; })(context.input)`);
        }
        catch (error) {
            return false;
        }
    }
    interpolateUrl(url, input) {
        return url.replace(/\{(\w+)\}/g, (match, key) => {
            return input[key] !== undefined ? encodeURIComponent(input[key]) : match;
        });
    }
    addAuthentication(config, auth) {
        switch (auth.type) {
            case 'api_key':
                config.headers[auth.credentials.headerName || 'X-API-Key'] = auth.credentials.apiKey;
                break;
            case 'bearer':
                config.headers['Authorization'] = `Bearer ${auth.credentials.token}`;
                break;
            case 'basic':
                const basicAuth = Buffer.from(`${auth.credentials.username}:${auth.credentials.password}`).toString('base64');
                config.headers['Authorization'] = `Basic ${basicAuth}`;
                break;
        }
    }
    validateInput(input, schema) {
        for (const [key, fieldSchema] of Object.entries(schema)) {
            const field = fieldSchema;
            if (field.required && input[key] === undefined) {
                throw new common_1.BadRequestException(`Required field '${key}' is missing`);
            }
        }
    }
    async trackUsage(organizationId, toolId, result) {
        await this.prisma.billingUsage.create({
            data: {
                organizationId,
                type: 'tool_execution',
                quantity: 1,
                cost: this.calculateCost(result),
                metadata: {
                    toolId,
                    success: result.success,
                    executionTime: result.executionTime,
                },
            },
        });
        await this.prisma.quota.upsert({
            where: {
                organizationId_type: {
                    organizationId,
                    type: 'api_calls',
                },
            },
            create: {
                organizationId,
                type: 'api_calls',
                limit: 1000,
                used: 1,
                resetAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
            },
            update: {
                used: {
                    increment: 1,
                },
            },
        });
    }
    calculateCost(result) {
        return result.success ? 0.001 : 0.0005;
    }
    async getExecutionHistory(toolId, organizationId, limit = 50) {
        return this.prisma.session.findMany({
            where: {
                organizationId,
                type: 'tool',
                metadata: {
                    path: ['targetId'],
                    equals: toolId,
                },
            },
            include: {
                user: {
                    select: {
                        id: true,
                        name: true,
                        email: true,
                    },
                },
            },
            orderBy: { createdAt: 'desc' },
            take: limit,
        });
    }
    async getExecutionMetrics(toolId, organizationId) {
        const [totalExecutions, avgExecutionTime, successRate, lastExecution] = await Promise.all([
            this.prisma.session.count({
                where: {
                    organizationId,
                    type: 'tool',
                    metadata: {
                        path: ['targetId'],
                        equals: toolId,
                    },
                },
            }),
            this.prisma.billingUsage.aggregate({
                where: {
                    organizationId,
                    type: 'tool_execution',
                    metadata: {
                        path: ['toolId'],
                        equals: toolId,
                    },
                },
                _avg: {
                    quantity: true,
                },
            }),
            this.prisma.billingUsage.aggregate({
                where: {
                    organizationId,
                    type: 'tool_execution',
                    metadata: {
                        path: ['toolId'],
                        equals: toolId,
                        success: true,
                    },
                },
                _count: true,
            }),
            this.prisma.session.findFirst({
                where: {
                    organizationId,
                    type: 'tool',
                    metadata: {
                        path: ['targetId'],
                        equals: toolId,
                    },
                },
                orderBy: { createdAt: 'desc' },
            }),
        ]);
        const totalBillingRecords = await this.prisma.billingUsage.count({
            where: {
                organizationId,
                type: 'tool_execution',
                metadata: {
                    path: ['toolId'],
                    equals: toolId,
                },
            },
        });
        return {
            totalExecutions,
            avgExecutionTime: avgExecutionTime._avg.quantity || 0,
            successRate: totalBillingRecords > 0 ? (successRate._count / totalBillingRecords) * 100 : 0,
            lastExecutedAt: lastExecution?.createdAt,
        };
    }
};
exports.ToolExecutorService = ToolExecutorService;
exports.ToolExecutorService = ToolExecutorService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService,
        apix_service_1.APIMXService,
        sessions_service_1.SessionsService,
        tools_service_1.ToolsService, typeof (_a = typeof config_1.ConfigService !== "undefined" && config_1.ConfigService) === "function" ? _a : Object])
], ToolExecutorService);
//# sourceMappingURL=tool-executor.service.js.map