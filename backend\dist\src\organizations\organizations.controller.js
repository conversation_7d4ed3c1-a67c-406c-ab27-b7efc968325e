"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OrganizationsController = void 0;
const common_1 = require("@nestjs/common");
const passport_1 = require("@nestjs/passport");
const organizations_service_1 = require("./organizations.service");
const organization_guard_1 = require("../common/guards/organization.guard");
const current_user_decorator_1 = require("../common/decorators/current-user.decorator");
let OrganizationsController = class OrganizationsController {
    constructor(organizationsService) {
        this.organizationsService = organizationsService;
    }
    getCurrent(organizationId) {
        return this.organizationsService.findCurrent(organizationId);
    }
    updateSettings(organizationId, settings) {
        return this.organizationsService.updateSettings(organizationId, settings);
    }
    getQuotas(organizationId) {
        return this.organizationsService.getQuotas(organizationId);
    }
    updateQuota(organizationId, body) {
        return this.organizationsService.updateQuota(organizationId, body.limit.toString(), body.limit);
    }
    getUsage(organizationId, type, from, to) {
        const fromDate = from ? new Date(from) : undefined;
        const toDate = to ? new Date(to) : undefined;
        return this.organizationsService.getUsage(organizationId, type, fromDate, toDate);
    }
};
exports.OrganizationsController = OrganizationsController;
__decorate([
    (0, common_1.Get)('current'),
    __param(0, (0, current_user_decorator_1.CurrentOrganization)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], OrganizationsController.prototype, "getCurrent", null);
__decorate([
    (0, common_1.Patch)('current/settings'),
    __param(0, (0, current_user_decorator_1.CurrentOrganization)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", void 0)
], OrganizationsController.prototype, "updateSettings", null);
__decorate([
    (0, common_1.Get)('current/quotas'),
    __param(0, (0, current_user_decorator_1.CurrentOrganization)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], OrganizationsController.prototype, "getQuotas", null);
__decorate([
    (0, common_1.Patch)('current/quotas/:type'),
    __param(0, (0, current_user_decorator_1.CurrentOrganization)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", void 0)
], OrganizationsController.prototype, "updateQuota", null);
__decorate([
    (0, common_1.Get)('current/usage'),
    __param(0, (0, current_user_decorator_1.CurrentOrganization)('id')),
    __param(1, (0, common_1.Query)('type')),
    __param(2, (0, common_1.Query)('from')),
    __param(3, (0, common_1.Query)('to')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String, String]),
    __metadata("design:returntype", void 0)
], OrganizationsController.prototype, "getUsage", null);
exports.OrganizationsController = OrganizationsController = __decorate([
    (0, common_1.Controller)('organizations'),
    (0, common_1.UseGuards)((0, passport_1.AuthGuard)('jwt'), organization_guard_1.OrganizationGuard),
    __metadata("design:paramtypes", [organizations_service_1.OrganizationsService])
], OrganizationsController);
//# sourceMappingURL=organizations.controller.js.map