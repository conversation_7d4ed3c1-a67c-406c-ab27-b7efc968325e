"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OrganizationsService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
let OrganizationsService = class OrganizationsService {
    constructor(prisma) {
        this.prisma = prisma;
    }
    async findCurrent(organizationId) {
        const organization = await this.prisma.organization.findUnique({
            where: { id: organizationId },
        });
        if (!organization) {
            throw new common_1.NotFoundException('Organization not found');
        }
        return organization;
    }
    async updateSettings(organizationId, settings) {
        const organization = await this.findCurrent(organizationId);
        const updatedOrganization = await this.prisma.organization.update({
            where: { id: organizationId },
            data: {
                settings: {
                    ...organization.settings,
                    ...settings,
                },
                updatedAt: new Date(),
            },
        });
        return updatedOrganization;
    }
    async getQuotas(organizationId) {
        return this.prisma.quota.findMany({
            where: { organizationId },
            orderBy: { type: 'asc' },
        });
    }
    async updateQuota(organizationId, type, limit) {
        const quota = await this.prisma.quota.upsert({
            where: {
                organizationId_type: {
                    organizationId,
                    type,
                },
            },
            create: {
                organizationId,
                type,
                limit,
                used: 0,
                resetAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
            },
            update: {
                limit,
                updatedAt: new Date(),
            },
        });
        return quota;
    }
    async getUsage(organizationId, type, from, to) {
        const where = { organizationId };
        if (type) {
            where.type = type;
        }
        if (from || to) {
            where.timestamp = {};
            if (from)
                where.timestamp.gte = from;
            if (to)
                where.timestamp.lte = to;
        }
        return this.prisma.billingUsage.findMany({
            where,
            orderBy: { timestamp: 'desc' },
            take: 1000,
        });
    }
};
exports.OrganizationsService = OrganizationsService;
exports.OrganizationsService = OrganizationsService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], OrganizationsService);
//# sourceMappingURL=organizations.service.js.map