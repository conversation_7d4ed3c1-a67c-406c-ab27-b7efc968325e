"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const client_1 = require("@prisma/client");
const bcrypt = require("bcryptjs");
const prisma = new client_1.PrismaClient();
async function main() {
    console.log('🌱 Seeding database...');
    const org = await prisma.organization.create({
        data: {
            name: 'SynapseAI Demo',
            slug: 'synapseai-demo',
            settings: {
                branding: {
                    theme: 'dark',
                    primaryColor: '#6366f1',
                },
                features: ['agents', 'tools', 'hybrids', 'knowledge', 'widgets'],
            },
        },
    });
    const adminRole = await prisma.role.create({
        data: {
            name: 'Admin',
            description: 'Full system access',
            organizationId: org.id,
            permissions: [
                'agents:create', 'agents:read', 'agents:update', 'agents:delete',
                'tools:create', 'tools:read', 'tools:update', 'tools:delete',
                'sessions:create', 'sessions:read', 'sessions:update', 'sessions:delete',
                'analytics:read', 'billing:read', 'admin:manage',
            ],
        },
    });
    const developerRole = await prisma.role.create({
        data: {
            name: 'Developer',
            description: 'Development access',
            organizationId: org.id,
            permissions: [
                'agents:create', 'agents:read', 'agents:update',
                'tools:create', 'tools:read', 'tools:update',
                'sessions:create', 'sessions:read', 'sessions:update',
            ],
        },
    });
    const adminUser = await prisma.user.create({
        data: {
            email: '<EMAIL>',
            name: 'Admin User',
            passwordHash: await bcrypt.hash('admin123', 12),
            organizationId: org.id,
            settings: {
                theme: 'dark',
                notifications: {
                    email: true,
                    sms: false,
                    webhook: true,
                    push: true,
                },
            },
        },
    });
    await prisma.userRole.create({
        data: {
            userId: adminUser.id,
            roleId: adminRole.id,
        },
    });
    await prisma.quota.createMany({
        data: [
            {
                organizationId: org.id,
                type: 'agents',
                limit: 10,
                resetAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
            },
            {
                organizationId: org.id,
                type: 'tools',
                limit: 25,
                resetAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
            },
            {
                organizationId: org.id,
                type: 'api_calls',
                limit: 1000,
                resetAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
            },
        ],
    });
    await prisma.template.createMany({
        data: [
            {
                name: 'Customer Support Agent',
                description: 'Helpful customer service assistant',
                category: 'support',
                template: {
                    prompt: 'You are a helpful customer support agent. Be polite, professional, and solution-oriented.',
                    config: {
                        temperature: 0.7,
                        maxTokens: 2000,
                        memory: { enabled: true, strategy: 'rolling', maxMessages: 20 },
                    },
                },
                isPublic: true,
            },
            {
                name: 'Content Writer',
                description: 'Creative writing assistant',
                category: 'content',
                template: {
                    prompt: 'You are a skilled content writer. Create engaging, well-structured content.',
                    config: {
                        temperature: 0.9,
                        maxTokens: 4000,
                        memory: { enabled: true, strategy: 'summarize', maxMessages: 10 },
                    },
                },
                isPublic: true,
            },
            {
                name: 'Data Analyst',
                description: 'Data analysis and insights assistant',
                category: 'analytics',
                template: {
                    prompt: 'You are a data analyst. Provide clear insights and recommendations based on data.',
                    config: {
                        temperature: 0.3,
                        maxTokens: 3000,
                        memory: { enabled: true, strategy: 'vector', maxMessages: 30 },
                    },
                },
                isPublic: true,
            },
        ],
    });
    console.log('✅ Database seeded successfully!');
    console.log(`📧 Admin user: <EMAIL> / admin123`);
    console.log(`🏢 Organization: ${org.name} (${org.slug})`);
}
main()
    .catch((e) => {
    console.error('❌ Seeding failed:', e);
    process.exit(1);
})
    .finally(async () => {
    await prisma.$disconnect();
});
//# sourceMappingURL=seed.js.map