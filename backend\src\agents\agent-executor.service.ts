import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { APIMXService } from '../apix/apix.service';
import { SessionsService } from '../sessions/sessions.service';
import { ProvidersService } from '../providers/providers.service';
import { ProviderExecutorService } from '../providers/provider-executor.service';
import { ExecuteAgentDto } from './dto/agent.dto';
import { ConfigService } from '@nestjs/config';
import { ProviderSelectionDto, ProviderCapability } from '../providers/dto/provider.dto';

export interface ExecutionResult {
  success: boolean;
  output: string;
  usage?: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  };
  executionTime: number;
  sessionId: string;
  providerId?: string;
  modelId?: string;
  cost?: number;
  error?: string;
}

export interface StreamingChunk {
  type: 'start' | 'chunk' | 'end' | 'error';
  content?: string;
  usage?: any;
  sessionId?: string;
  error?: string;
}

@Injectable()
export class AgentExecutorService {
  constructor(
    private prisma: PrismaService,
    private apixService: APIMXService,
    private sessionsService: SessionsService,
    private providersService: ProvidersService,
    private providerExecutorService: ProviderExecutorService,
    private configService: ConfigService,
  ) {}

  async executeAgent(
    agentId: string,
    organizationId: string,
    userId: string,
    executeDto: ExecuteAgentDto,
  ): Promise<ExecutionResult> {
    const startTime = Date.now();

    try {
      // Get agent configuration
      const agent = await this.prisma.agent.findFirst({
        where: {
          id: agentId,
          organizationId,
          status: 'active',
        },
      });

      if (!agent) {
        throw new NotFoundException('Agent not found or not active');
      }

      const config = agent.config as any;

      // Create or get session
      let sessionId = executeDto.sessionId;
      if (!sessionId) {
        const session = await this.sessionsService.create(userId, organizationId, {
          type: 'agent' as any,
          targetId: agentId,
          metadata: {
            agentName: agent.name,
            modelPreferences: config.modelPreferences,
          },
        });
        sessionId = session.id;
      }

      // Get session for context
      const session = await this.sessionsService.findOne(sessionId, organizationId, userId);
      const sessionMemory = session.memory as any;

      // Prepare the complete prompt with agent instructions and context
      const completePrompt = this.buildCompletePrompt(
        config,
        executeDto.input,
        sessionMemory.messages,
        executeDto.context,
      );

      // Select optimal provider based on agent configuration
      const selectionCriteria: ProviderSelectionDto = {
        requiredCapabilities: [ProviderCapability.TEXT_GENERATION],
        preferredModelSize: config.modelPreferences?.size || 'medium',
        maxCostPer1KTokens: config.costLimits?.maxCostPer1K || undefined,
        preferLowLatency: config.preferences?.prioritizeLowLatency || false,
        preferLowCost: config.preferences?.prioritizeLowCost || false,
        organizationPreferences: {
          preferredProviders: config.preferredProviders,
          excludedProviders: config.excludedProviders,
          maxCostThreshold: config.costLimits?.maxTotal,
        },
      };

      const { provider, model } = await this.providersService.selectOptimalProvider(
        organizationId,
        selectionCriteria,
      );

      // Execute with selected provider
      const providerResult = await this.providerExecutorService.execute(
        provider,
        {
          providerId: provider.id,
          modelId: model.id,
          prompt: completePrompt,
          parameters: {
            temperature: config.temperature || 0.7,
            maxTokens: config.maxTokens || 2048,
            topP: config.topP,
            frequencyPenalty: config.frequencyPenalty,
            presencePenalty: config.presencePenalty,
            stopSequences: config.stopSequences,
          },
          sessionId,
          overrides: executeDto.overrides,
        },
        organizationId,
        userId,
      );

      // Add messages to session
      await this.sessionsService.addMessage(sessionId, organizationId, userId, {
        role: 'user',
        content: executeDto.input,
        metadata: {
          ...executeDto.context,
          agentId,
          providerId: provider.id,
          modelId: model.id,
        },
      });

      await this.sessionsService.addMessage(sessionId, organizationId, userId, {
        role: 'assistant',
        content: providerResult.response,
        metadata: {
          agentId,
          providerId: provider.id,
          modelId: model.id,
          usage: providerResult.usage,
          cost: providerResult.cost,
          executionTime: providerResult.executionTime,
        },
      });

      // Update session context if provided
      if (executeDto.context) {
        await this.sessionsService.updateContext(sessionId, organizationId, userId, executeDto.context);
      }

      // Track usage for billing (additional agent-specific billing)
      await this.trackAgentUsage(organizationId, agentId, providerResult);

      // Publish execution event
      await this.apixService.publishAgentEvent(
        'execution_completed',
        agentId,
        organizationId,
        {
          sessionId,
          providerId: provider.id,
          modelId: model.id,
          usage: providerResult.usage,
          cost: providerResult.cost,
          executionTime: providerResult.executionTime,
        }
      );

      const totalExecutionTime = Date.now() - startTime;

      return {
        success: providerResult.success,
        output: providerResult.response,
        usage: providerResult.usage,
        executionTime: totalExecutionTime,
        sessionId,
        providerId: provider.id,
        modelId: model.id,
        cost: providerResult.cost,
      };
    } catch (error) {
      const executionTime = Date.now() - startTime;

      await this.apixService.publishAgentEvent(
        'execution_failed',
        agentId,
        organizationId,
        {
          error: error.message,
          executionTime,
        }
      );

      throw error;
    }
  }

  private buildCompletePrompt(
    config: any,
    userInput: string,
    conversationHistory: any[] = [],
    context?: any,
  ): string {
    let prompt = '';

    // Add system instructions
    if (config.instructions) {
      prompt += `${config.instructions}\n\n`;
    }

    // Add conversation history (last N messages for context)
    const maxHistoryMessages = config.maxHistoryMessages || 10;
    const recentHistory = conversationHistory.slice(-maxHistoryMessages);
    
    if (recentHistory.length > 0) {
      prompt += 'Previous conversation:\n';
      recentHistory.forEach((msg: any) => {
        prompt += `${msg.role === 'user' ? 'Human' : 'Assistant'}: ${msg.content}\n`;
      });
      prompt += '\n';
    }

    // Add context if provided
    if (context && Object.keys(context).length > 0) {
      prompt += 'Additional context:\n';
      Object.entries(context).forEach(([key, value]) => {
        prompt += `${key}: ${JSON.stringify(value)}\n`;
      });
      prompt += '\n';
    }

    // Add current user input
    prompt += `Human: ${userInput}\n\nAssistant:`;

    return prompt;
  }

  private async trackAgentUsage(organizationId: string, agentId: string, providerResult: any) {
    // Create agent-specific billing record
    await this.prisma.billingUsage.create({
      data: {
        organizationId,
        type: 'agent_execution',
        quantity: 1,
        cost: providerResult.cost || 0,
        metadata: {
          agentId,
          providerId: providerResult.providerId,
          modelId: providerResult.modelId,
          promptTokens: providerResult.usage?.promptTokens || 0,
          completionTokens: providerResult.usage?.completionTokens || 0,
          totalTokens: providerResult.usage?.totalTokens || 0,
          executionTime: providerResult.executionTime,
          success: providerResult.success,
        },
      },
    });

    // Update agent execution count
    await this.prisma.agent.update({
      where: { id: agentId },
      data: {
        metadata: {
          path: ['executions'],
          increment: 1,
        },
      },
    });

    // Update quota usage
    await this.prisma.quota.upsert({
      where: {
        organizationId_type: {
          organizationId,
          type: 'agent_executions',
        },
      },
      create: {
        organizationId,
        type: 'agent_executions',
        limit: 1000,
        used: 1,
        resetAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
      },
      update: {
        used: {
          increment: 1,
        },
      },
    });
  }

  async *executeAgentStreaming(
    agentId: string,
    organizationId: string,
    userId: string,
    executeDto: ExecuteAgentDto,
  ): AsyncGenerator<StreamingChunk, void, unknown> {
    try {
      // Get agent configuration
      const agent = await this.prisma.agent.findFirst({
        where: {
          id: agentId,
          organizationId,
          status: 'active',
        },
      });

      if (!agent) {
        throw new NotFoundException('Agent not found or not active');
      }

      const config = agent.config as any;

      // Create or get session
      let sessionId = executeDto.sessionId;
      if (!sessionId) {
        const session = await this.sessionsService.create(userId, organizationId, {
          type: 'agent' as any,
          targetId: agentId,
          metadata: {
            agentName: agent.name,
            model: config.model,
          },
        });
        sessionId = session.id;
      }

      yield { type: 'start', sessionId };

      // Get session for context
      const session = await this.sessionsService.findOne(sessionId, organizationId, userId);
      const sessionMemory = session.memory as any;

      // Prepare messages
      const messages = this.prepareMessages(
        config,
        executeDto.input,
        sessionMemory.messages,
        executeDto.context,
      );

      // Select optimal provider
      const provider = await this.providersService.selectOptimalProvider(
        organizationId,
        {
          capabilities: [ProviderCapability.CHAT_COMPLETION],
          model: executeDto.overrides?.model || config.model,
        },
      );

      // Execute with streaming through provider system
      let fullContent = '';
      const executionConfig = {
        model: executeDto.overrides?.model || config.model,
        messages,
        temperature: executeDto.overrides?.temperature ?? config.temperature,
        maxTokens: executeDto.overrides?.maxTokens ?? config.maxTokens,
        stream: true,
      };

      const stream = this.providerExecutorService.executeStreamingChat(
        provider,
        executionConfig,
        organizationId,
        userId,
      );

      for await (const chunk of stream) {
        if (chunk.type === 'chunk' && chunk.content) {
          fullContent += chunk.content;
          yield { type: 'chunk', content: chunk.content };
        } else if (chunk.type === 'error') {
          yield { type: 'error', error: chunk.error };
          return;
        }
      }

      // Add messages to session
      await this.sessionsService.addMessage(sessionId, organizationId, userId, {
        role: 'user',
        content: executeDto.input,
        metadata: executeDto.context,
      });

      await this.sessionsService.addMessage(sessionId, organizationId, userId, {
        role: 'assistant',
        content: fullContent,
        metadata: {
          model: config.model,
          streaming: true,
        },
      });

      yield { type: 'end', sessionId };

      await this.apixService.publishAgentEvent(
        'execution_completed',
        agentId,
        organizationId,
        {
          sessionId,
          streaming: true,
        }
      );
    } catch (error) {
      yield { type: 'error', error: error.message };

      await this.apixService.publishAgentEvent(
        'execution_failed',
        agentId,
        organizationId,
        {
          error: error.message,
          streaming: true,
        }
      );
    }
  }

  private prepareMessages(
    config: any,
    input: string,
    sessionMessages: any[],
    context?: Record<string, any>,
  ): any[] {
    const messages: any[] = [];

    // Add system prompt if exists
    if (config.systemPrompt) {
      messages.push({
        role: 'system',
        content: config.systemPrompt,
      });
    }

    // Add main prompt as system message
    let promptContent = config.prompt;

    // Inject context variables into prompt
    if (context) {
      Object.entries(context).forEach(([key, value]) => {
        const placeholder = `{{${key}}}`;
        promptContent = promptContent.replace(new RegExp(placeholder, 'g'), String(value));
      });
    }

    messages.push({
      role: 'system',
      content: promptContent,
    });

    // Add session history if memory is enabled
    if (config.memory?.enabled && sessionMessages.length > 0) {
      const memoryStrategy = config.memory.strategy || 'rolling';
      const maxMessages = config.memory.maxMessages || 50;

      let messagesToInclude = sessionMessages;

      if (memoryStrategy === 'rolling') {
        messagesToInclude = sessionMessages.slice(-maxMessages);
      } else if (memoryStrategy === 'summarize' && sessionMessages.length > maxMessages) {
        // For now, just take the last messages. In a full implementation,
        // this would summarize older messages
        messagesToInclude = sessionMessages.slice(-maxMessages);
      }

      messagesToInclude.forEach((msg: any) => {
        if (msg.role !== 'system') {
          messages.push({
            role: msg.role,
            content: msg.content,
          });
        }
      });
    }

    // Add current input
    messages.push({
      role: 'user',
      content: input,
    });

    return messages;
  }

  private async executeWithProvider(
    config: any,
    messages: any[],
    overrides?: any,
    organizationId?: string,
    userId?: string,
  ): Promise<ExecutionResult> {
    const startTime = Date.now();

    try {
      // Select optimal provider
      const provider = await this.providersService.selectOptimalProvider(
        organizationId || '',
        {
          capabilities: [ProviderCapability.CHAT_COMPLETION],
          model: overrides?.model || config.model,
        },
      );

      // Execute through provider system
      const executionConfig = {
        model: overrides?.model || config.model,
        messages,
        temperature: overrides?.temperature ?? config.temperature,
        maxTokens: overrides?.maxTokens ?? config.maxTokens,
      };

      const result = await this.providerExecutorService.executeChat(
        provider,
        executionConfig,
        organizationId || '',
        userId || '',
      );

      return {
        success: result.success,
        output: result.output || '',
        usage: result.usage,
        executionTime: Date.now() - startTime,
        sessionId: '', // Will be set by caller
      };
    } catch (error) {
      return {
        success: false,
        output: '',
        error: error.message,
        executionTime: Date.now() - startTime,
        sessionId: '', // Will be set by caller
      };
    }
  }

  private async trackUsage(organizationId: string, agentId: string, usage?: any) {
    if (!usage) return;

    await this.prisma.billingUsage.create({
      data: {
        organizationId,
        type: 'agent_execution',
        quantity: usage.totalTokens,
        cost: this.calculateCost(usage.totalTokens),
        metadata: {
          agentId,
          promptTokens: usage.promptTokens,
          completionTokens: usage.completionTokens,
        },
      },
    });

    // Update quota usage
    await this.prisma.quota.upsert({
      where: {
        organizationId_type: {
          organizationId,
          type: 'api_calls',
        },
      },
      create: {
        organizationId,
        type: 'api_calls',
        limit: 1000,
        used: 1,
        resetAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
      },
      update: {
        used: {
          increment: 1,
        },
      },
    });
  }

  private calculateCost(tokens: number): number {
    // Simple cost calculation - in production this would be more sophisticated
    // Based on model pricing, etc.
    return (tokens / 1000) * 0.002; // $0.002 per 1K tokens
  }

  async getExecutionHistory(agentId: string, organizationId: string, limit = 50) {
    return this.prisma.session.findMany({
      where: {
        organizationId,
        type: 'agent',
        metadata: {
          path: ['targetId'],
          equals: agentId,
        },
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
      orderBy: { createdAt: 'desc' },
      take: limit,
    });
  }

  async getExecutionMetrics(agentId: string, organizationId: string) {
    const [totalExecutions, avgExecutionTime, totalTokens, lastExecution] = await Promise.all([
      this.prisma.session.count({
        where: {
          organizationId,
          type: 'agent',
          metadata: {
            path: ['targetId'],
            equals: agentId,
          },
        },
      }),
      this.prisma.billingUsage.aggregate({
        where: {
          organizationId,
          type: 'agent_execution',
          metadata: {
            path: ['agentId'],
            equals: agentId,
          },
        },
        _avg: {
          quantity: true,
        },
      }),
      this.prisma.billingUsage.aggregate({
        where: {
          organizationId,
          type: 'agent_execution',
          metadata: {
            path: ['agentId'],
            equals: agentId,
          },
        },
        _sum: {
          quantity: true,
        },
      }),
      this.prisma.session.findFirst({
        where: {
          organizationId,
          type: 'agent',
          metadata: {
            path: ['targetId'],
            equals: agentId,
          },
        },
        orderBy: { createdAt: 'desc' },
      }),
    ]);

    return {
      totalExecutions,
      avgTokensPerExecution: avgExecutionTime._avg.quantity || 0,
      totalTokensUsed: totalTokens._sum.quantity || 0,
      lastExecutedAt: lastExecution?.createdAt,
    };
  }
}