import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { APIMXService } from '../apix/apix.service';
import { SessionsService } from '../sessions/sessions.service';
import { ToolsService } from './tools.service';
import { ExecuteToolDto, TestToolDto } from './dto/tool.dto';
import { ConfigService } from '@nestjs/config';
import { SafeExecutorService } from './safe-executor.service';
import axios, { AxiosRequestConfig } from 'axios';

export interface ToolExecutionResult {
  success: boolean;
  output: any;
  executionTime: number;
  sessionId?: string;
  error?: string;
  metadata?: {
    httpStatus?: number;
    headers?: Record<string, string>;
    retryCount?: number;
  };
}

@Injectable()
export class ToolExecutorService {
  constructor(
    private prisma: PrismaService,
    private apixService: APIMXService,
    private sessionsService: SessionsService,
    private toolsService: ToolsService,
    private configService: ConfigService,
    private safeExecutor: SafeExecutorService,
  ) {}

  async executeTool(
    toolId: string,
    organizationId: string,
    userId: string,
    executeDto: ExecuteToolDto,
  ): Promise<ToolExecutionResult> {
    const startTime = Date.now();

    try {
      // Get tool configuration
      const tool = await this.prisma.tool.findFirst({
        where: {
          id: toolId,
          organizationId,
          status: 'active',
        },
      });

      if (!tool) {
        throw new NotFoundException('Tool not found or not active');
      }

      const config = tool.config as any;
      const schema = tool.schema as any;

      // Validate input if required
      if (executeDto.validateInput) {
        this.validateInput(executeDto.input, schema.input);
      }

      // Create or get session
      let sessionId = executeDto.sessionId;
      if (!sessionId) {
        const session = await this.sessionsService.create(userId, organizationId, {
          type: 'tool' as any,
          targetId: toolId,
          metadata: {
            toolName: tool.name,
            toolType: tool.type,
          },
        });
        sessionId = session.id;
      }

      // Execute based on tool type
      let result: ToolExecutionResult;
      switch (tool.type) {
        case 'api':
          result = await this.executeApiTool(config, executeDto.input, executeDto.overrides);
          break;
        case 'function':
          result = await this.executeFunctionTool(config, executeDto.input);
          break;
        case 'workflow':
          result = await this.executeWorkflowTool(config, executeDto.input, organizationId, userId);
          break;
        default:
          throw new BadRequestException('Unsupported tool type');
      }

      result.sessionId = sessionId;
      result.executionTime = Date.now() - startTime;

      // Add execution to session
      await this.sessionsService.addMessage(sessionId, organizationId, userId, {
        role: 'user',
        content: JSON.stringify(executeDto.input),
        metadata: { toolId, type: 'tool_input' },
      });

      await this.sessionsService.addMessage(sessionId, organizationId, userId, {
        role: 'assistant',
        content: JSON.stringify(result.output),
        metadata: {
          toolId,
          type: 'tool_output',
          executionTime: result.executionTime,
          success: result.success,
        },
      });

      // Track usage for billing
      await this.trackUsage(organizationId, toolId, result);

      // Increment execution count
      await this.toolsService.incrementExecutionCount(toolId);

      // Publish execution event
      await this.apixService.publishToolEvent(
        'execution_completed',
        toolId,
        organizationId,
        {
          sessionId,
          success: result.success,
          executionTime: result.executionTime,
        }
      );

      return result;
    } catch (error) {
      const executionTime = Date.now() - startTime;

      await this.apixService.publishToolEvent(
        'execution_failed',
        toolId,
        organizationId,
        {
          error: error.message,
          executionTime,
        }
      );

      throw error;
    }
  }

  async testTool(
    toolId: string,
    organizationId: string,
    testDto: TestToolDto,
  ): Promise<ToolExecutionResult> {
    const tool = await this.toolsService.findOne(toolId, organizationId);
    const config = tool.config as any;
    const schema = tool.schema as any;

    // Validate input
    this.validateInput(testDto.input, schema.input);

    if (testDto.dryRun) {
      return {
        success: true,
        output: { message: 'Dry run successful', input: testDto.input },
        executionTime: 0,
      };
    }

    // Execute without session tracking
    switch (tool.type) {
      case 'api':
        return this.executeApiTool(config, testDto.input);
      case 'function':
        return this.executeFunctionTool(config, testDto.input);
      case 'workflow':
        throw new BadRequestException('Workflow tools cannot be tested directly');
      default:
        throw new BadRequestException('Unsupported tool type');
    }
  }

  private async executeApiTool(
    config: any,
    input: any,
    overrides?: any,
  ): Promise<ToolExecutionResult> {
    const startTime = Date.now();

    try {
      // Prepare request configuration
      const requestConfig: AxiosRequestConfig = {
        method: config.method,
        url: this.interpolateUrl(config.endpoint, input),
        timeout: overrides?.timeout || config.timeout || 30000,
        headers: {
          ...config.headers,
          ...overrides?.headers,
        },
      };

      // Add authentication
      if (config.authentication && config.authentication.type !== 'none') {
        this.addAuthentication(requestConfig, config.authentication);
      }

      // Add request body for POST/PUT/PATCH
      if (['POST', 'PUT', 'PATCH'].includes(config.method)) {
        requestConfig.data = input;
        requestConfig.headers['Content-Type'] = requestConfig.headers['Content-Type'] || 'application/json';
      } else if (config.method === 'GET') {
        requestConfig.params = input;
      }

      // Execute with retries
      let lastError: any;
      const retries = overrides?.retries ?? config.retries ?? 3;

      for (let attempt = 0; attempt <= retries; attempt++) {
        try {
          const response = await axios(requestConfig);
          
          return {
            success: true,
            output: response.data,
            executionTime: Date.now() - startTime,
            metadata: {
              httpStatus: response.status,
              headers: response.headers as any,
              retryCount: attempt,
            },
          };
        } catch (error) {
          lastError = error;
          
          // Don't retry on client errors (4xx)
          if (error.response?.status >= 400 && error.response?.status < 500) {
            break;
          }
          
          // Wait before retry (exponential backoff)
          if (attempt < retries) {
            await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt) * 1000));
          }
        }
      }

      // All retries failed
      return {
        success: false,
        output: null,
        executionTime: Date.now() - startTime,
        error: lastError.message,
        metadata: {
          httpStatus: lastError.response?.status,
          retryCount: retries,
        },
      };
    } catch (error) {
      return {
        success: false,
        output: null,
        executionTime: Date.now() - startTime,
        error: error.message,
      };
    }
  }

  private async executeFunctionTool(config: any, input: any): Promise<ToolExecutionResult> {
    try {
      // Validate function code before execution
      const validation = this.safeExecutor.validateFunctionCode(config.functionCode);
      if (!validation.valid) {
        return {
          success: false,
          output: null,
          executionTime: 0,
          error: `Function validation failed: ${validation.errors.join(', ')}`,
        };
      }

      // Create execution context
      const context = {
        input,
        config,
        metadata: {
          timestamp: new Date(),
        },
      };

      // Execute the function code safely
      const result = await this.safeExecutor.executeFunctionCode(
        config.functionCode,
        context,
      );

      return {
        success: result.success,
        output: result.output,
        executionTime: result.executionTime,
        error: result.error,
        logs: result.logs,
      };
    } catch (error) {
      return {
        success: false,
        output: null,
        executionTime: 0,
        error: error.message,
      };
    }
  }

  private async executeWorkflowTool(
    config: any,
    input: any,
    organizationId: string,
    userId: string,
  ): Promise<ToolExecutionResult> {
    const startTime = Date.now();

    try {
      const steps = config.workflowSteps;
      let currentInput = input;
      const executionLog = [];

      for (const step of steps) {
        const stepResult = await this.executeWorkflowStep(
          step,
          currentInput,
          organizationId,
          userId,
        );

        executionLog.push({
          stepId: step.id,
          input: currentInput,
          output: stepResult.output,
          success: stepResult.success,
          executionTime: stepResult.executionTime,
        });

        if (!stepResult.success) {
          return {
            success: false,
            output: { executionLog, error: stepResult.error },
            executionTime: Date.now() - startTime,
            error: `Step ${step.id} failed: ${stepResult.error}`,
          };
        }

        currentInput = stepResult.output;
      }

      return {
        success: true,
        output: { result: currentInput, executionLog },
        executionTime: Date.now() - startTime,
      };
    } catch (error) {
      return {
        success: false,
        output: null,
        executionTime: Date.now() - startTime,
        error: error.message,
      };
    }
  }

  private async executeWorkflowStep(
    step: any,
    input: any,
    organizationId: string,
    userId: string,
  ): Promise<ToolExecutionResult> {
    switch (step.type) {
      case 'api':
        return this.executeApiTool(step.config, input);
      case 'function':
        return this.executeFunctionTool(step.config, input);
      case 'condition':
        return this.executeConditionStep(step.config, input);
      default:
        throw new BadRequestException(`Unsupported workflow step type: ${step.type}`);
    }
  }

  private async executeConditionStep(config: any, input: any): Promise<ToolExecutionResult> {
    const startTime = Date.now();

    try {
      // Simple condition evaluation
      const condition = config.condition;
      const result = this.evaluateCondition(condition, input);

      return {
        success: true,
        output: { condition: result, input },
        executionTime: Date.now() - startTime,
      };
    } catch (error) {
      return {
        success: false,
        output: null,
        executionTime: Date.now() - startTime,
        error: error.message,
      };
    }
  }

  private evaluateCondition(condition: string, input: any): boolean {
    try {
      return this.safeExecutor.evaluateCondition(condition, input);
    } catch (error) {
      return false;
    }
  }

  private interpolateUrl(url: string, input: any): string {
    return url.replace(/\{(\w+)\}/g, (match, key) => {
      return input[key] !== undefined ? encodeURIComponent(input[key]) : match;
    });
  }

  private addAuthentication(config: AxiosRequestConfig, auth: any) {
    switch (auth.type) {
      case 'api_key':
        config.headers[auth.credentials.headerName || 'X-API-Key'] = auth.credentials.apiKey;
        break;
      case 'bearer':
        config.headers['Authorization'] = `Bearer ${auth.credentials.token}`;
        break;
      case 'basic':
        const basicAuth = Buffer.from(`${auth.credentials.username}:${auth.credentials.password}`).toString('base64');
        config.headers['Authorization'] = `Basic ${basicAuth}`;
        break;
      // OAuth2 would require more complex implementation
    }
  }

  private validateInput(input: any, schema: any) {
    // Simple validation - in production, use a proper JSON schema validator
    for (const [key, fieldSchema] of Object.entries(schema)) {
      const field = fieldSchema as any;
      if (field.required && input[key] === undefined) {
        throw new BadRequestException(`Required field '${key}' is missing`);
      }
    }
  }

  private async trackUsage(organizationId: string, toolId: string, result: ToolExecutionResult) {
    await this.prisma.billingUsage.create({
      data: {
        organizationId,
        type: 'tool_execution',
        quantity: 1,
        cost: this.calculateCost(result),
        metadata: {
          toolId,
          success: result.success,
          executionTime: result.executionTime,
        },
      },
    });

    // Update quota usage
    await this.prisma.quota.upsert({
      where: {
        organizationId_type: {
          organizationId,
          type: 'api_calls',
        },
      },
      create: {
        organizationId,
        type: 'api_calls',
        limit: 1000,
        used: 1,
        resetAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
      },
      update: {
        used: {
          increment: 1,
        },
      },
    });
  }

  private calculateCost(result: ToolExecutionResult): number {
    // Simple cost calculation - in production this would be more sophisticated
    return result.success ? 0.001 : 0.0005; // $0.001 per successful execution
  }

  async getExecutionHistory(toolId: string, organizationId: string, limit = 50) {
    return this.prisma.session.findMany({
      where: {
        organizationId,
        type: 'tool',
        metadata: {
          path: ['targetId'],
          equals: toolId,
        },
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
      orderBy: { createdAt: 'desc' },
      take: limit,
    });
  }

  async getExecutionMetrics(toolId: string, organizationId: string) {
    const [totalExecutions, avgExecutionTime, successRate, lastExecution] = await Promise.all([
      this.prisma.session.count({
        where: {
          organizationId,
          type: 'tool',
          metadata: {
            path: ['targetId'],
            equals: toolId,
          },
        },
      }),
      this.prisma.billingUsage.aggregate({
        where: {
          organizationId,
          type: 'tool_execution',
          metadata: {
            path: ['toolId'],
            equals: toolId,
          },
        },
        _avg: {
          quantity: true,
        },
      }),
      this.prisma.billingUsage.aggregate({
        where: {
          organizationId,
          type: 'tool_execution',
          metadata: {
            path: ['toolId'],
            equals: toolId,
            success: true,
          },
        },
        _count: true,
      }),
      this.prisma.session.findFirst({
        where: {
          organizationId,
          type: 'tool',
          metadata: {
            path: ['targetId'],
            equals: toolId,
          },
        },
        orderBy: { createdAt: 'desc' },
      }),
    ]);

    const totalBillingRecords = await this.prisma.billingUsage.count({
      where: {
        organizationId,
        type: 'tool_execution',
        metadata: {
          path: ['toolId'],
          equals: toolId,
        },
      },
    });

    return {
      totalExecutions,
      avgExecutionTime: avgExecutionTime._avg.quantity || 0,
      successRate: totalBillingRecords > 0 ? (successRate._count / totalBillingRecords) * 100 : 0,
      lastExecutedAt: lastExecution?.createdAt,
    };
  }
}