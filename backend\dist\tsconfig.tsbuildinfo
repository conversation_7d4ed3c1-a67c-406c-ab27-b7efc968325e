{"fileNames": ["../../../../../../../../Users/<USER>/AppData/Local/pnpm/global/5/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es5.d.ts", "../../../../../../../../Users/<USER>/AppData/Local/pnpm/global/5/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2015.d.ts", "../../../../../../../../Users/<USER>/AppData/Local/pnpm/global/5/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2016.d.ts", "../../../../../../../../Users/<USER>/AppData/Local/pnpm/global/5/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2017.d.ts", "../../../../../../../../Users/<USER>/AppData/Local/pnpm/global/5/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2018.d.ts", "../../../../../../../../Users/<USER>/AppData/Local/pnpm/global/5/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2019.d.ts", "../../../../../../../../Users/<USER>/AppData/Local/pnpm/global/5/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2020.d.ts", "../../../../../../../../Users/<USER>/AppData/Local/pnpm/global/5/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.dom.d.ts", "../../../../../../../../Users/<USER>/AppData/Local/pnpm/global/5/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.dom.iterable.d.ts", "../../../../../../../../Users/<USER>/AppData/Local/pnpm/global/5/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.dom.asynciterable.d.ts", "../../../../../../../../Users/<USER>/AppData/Local/pnpm/global/5/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.webworker.importscripts.d.ts", "../../../../../../../../Users/<USER>/AppData/Local/pnpm/global/5/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.scripthost.d.ts", "../../../../../../../../Users/<USER>/AppData/Local/pnpm/global/5/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2015.core.d.ts", "../../../../../../../../Users/<USER>/AppData/Local/pnpm/global/5/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2015.collection.d.ts", "../../../../../../../../Users/<USER>/AppData/Local/pnpm/global/5/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2015.generator.d.ts", "../../../../../../../../Users/<USER>/AppData/Local/pnpm/global/5/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2015.iterable.d.ts", "../../../../../../../../Users/<USER>/AppData/Local/pnpm/global/5/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2015.promise.d.ts", "../../../../../../../../Users/<USER>/AppData/Local/pnpm/global/5/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2015.proxy.d.ts", "../../../../../../../../Users/<USER>/AppData/Local/pnpm/global/5/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2015.reflect.d.ts", "../../../../../../../../Users/<USER>/AppData/Local/pnpm/global/5/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2015.symbol.d.ts", "../../../../../../../../Users/<USER>/AppData/Local/pnpm/global/5/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2015.symbol.wellknown.d.ts", "../../../../../../../../Users/<USER>/AppData/Local/pnpm/global/5/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2016.array.include.d.ts", "../../../../../../../../Users/<USER>/AppData/Local/pnpm/global/5/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2016.intl.d.ts", "../../../../../../../../Users/<USER>/AppData/Local/pnpm/global/5/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2017.arraybuffer.d.ts", "../../../../../../../../Users/<USER>/AppData/Local/pnpm/global/5/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2017.date.d.ts", "../../../../../../../../Users/<USER>/AppData/Local/pnpm/global/5/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2017.object.d.ts", "../../../../../../../../Users/<USER>/AppData/Local/pnpm/global/5/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2017.sharedmemory.d.ts", "../../../../../../../../Users/<USER>/AppData/Local/pnpm/global/5/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2017.string.d.ts", "../../../../../../../../Users/<USER>/AppData/Local/pnpm/global/5/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2017.intl.d.ts", "../../../../../../../../Users/<USER>/AppData/Local/pnpm/global/5/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2017.typedarrays.d.ts", "../../../../../../../../Users/<USER>/AppData/Local/pnpm/global/5/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2018.asyncgenerator.d.ts", "../../../../../../../../Users/<USER>/AppData/Local/pnpm/global/5/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2018.asynciterable.d.ts", "../../../../../../../../Users/<USER>/AppData/Local/pnpm/global/5/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2018.intl.d.ts", "../../../../../../../../Users/<USER>/AppData/Local/pnpm/global/5/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2018.promise.d.ts", "../../../../../../../../Users/<USER>/AppData/Local/pnpm/global/5/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2018.regexp.d.ts", "../../../../../../../../Users/<USER>/AppData/Local/pnpm/global/5/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2019.array.d.ts", "../../../../../../../../Users/<USER>/AppData/Local/pnpm/global/5/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2019.object.d.ts", "../../../../../../../../Users/<USER>/AppData/Local/pnpm/global/5/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2019.string.d.ts", "../../../../../../../../Users/<USER>/AppData/Local/pnpm/global/5/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2019.symbol.d.ts", "../../../../../../../../Users/<USER>/AppData/Local/pnpm/global/5/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2019.intl.d.ts", "../../../../../../../../Users/<USER>/AppData/Local/pnpm/global/5/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2020.bigint.d.ts", "../../../../../../../../Users/<USER>/AppData/Local/pnpm/global/5/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2020.date.d.ts", "../../../../../../../../Users/<USER>/AppData/Local/pnpm/global/5/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2020.promise.d.ts", "../../../../../../../../Users/<USER>/AppData/Local/pnpm/global/5/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2020.sharedmemory.d.ts", "../../../../../../../../Users/<USER>/AppData/Local/pnpm/global/5/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2020.string.d.ts", "../../../../../../../../Users/<USER>/AppData/Local/pnpm/global/5/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2020.symbol.wellknown.d.ts", "../../../../../../../../Users/<USER>/AppData/Local/pnpm/global/5/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2020.intl.d.ts", "../../../../../../../../Users/<USER>/AppData/Local/pnpm/global/5/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2020.number.d.ts", "../../../../../../../../Users/<USER>/AppData/Local/pnpm/global/5/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.decorators.d.ts", "../../../../../../../../Users/<USER>/AppData/Local/pnpm/global/5/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.decorators.legacy.d.ts", "../../../../../../../../Users/<USER>/AppData/Local/pnpm/global/5/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2020.full.d.ts", "../prisma/seed.ts", "../src/prisma/prisma.service.ts", "../src/prisma/prisma.module.ts", "../src/auth/dto/auth.dto.ts", "../src/auth/auth.service.ts", "../src/auth/auth.controller.ts", "../src/auth/strategies/jwt.strategy.ts", "../src/auth/strategies/local.strategy.ts", "../src/common/guards/organization.guard.ts", "../src/auth/auth.module.ts", "../src/apix/apix.service.ts", "../src/apix/apix.gateway.ts", "../src/apix/apix.module.ts", "../src/sessions/dto/session.dto.ts", "../src/sessions/sessions.service.ts", "../src/auth/jwt-auth.guard.ts", "../src/common/decorators/current-user.decorator.ts", "../src/sessions/sessions.controller.ts", "../src/sessions/sessions.module.ts", "../src/organizations/organizations.service.ts", "../src/organizations/organizations.controller.ts", "../src/organizations/organizations.module.ts", "../src/agents/dto/agent.dto.ts", "../src/agents/agents.service.ts", "../src/providers/dto/provider.dto.ts", "../src/providers/circuit-breaker.service.ts", "../../../node_modules/.pnpm/axios@1.10.0/node_modules/axios/index.d.ts", "../src/providers/provider-executor.service.ts", "../src/providers/providers.service.ts", "../src/agents/agent-executor.service.ts", "../src/agents/agents.controller.ts", "../src/providers/providers.controller.ts", "../src/providers/providers.module.ts", "../src/agents/agents.module.ts", "../src/templates/dto/template.dto.ts", "../src/templates/templates.service.ts", "../src/templates/templates.controller.ts", "../src/templates/templates.module.ts", "../src/tools/dto/tool.dto.ts", "../src/tools/tools.service.ts", "../../../node_modules/.pnpm/vm2@3.9.19/node_modules/vm2/index.d.ts", "../src/tools/safe-executor.service.ts", "../src/tools/tool-executor.service.ts", "../src/tools/tools.controller.ts", "../src/tools/tools.module.ts", "../src/workflows/dto/workflow.dto.ts", "../src/workflows/workflows.service.ts", "../src/workflows/workflow-executor.service.ts", "../src/workflows/workflows.controller.ts", "../src/workflows/workflows.module.ts", "../src/app.module.ts", "../src/common/logger/winston.logger.ts", "../src/main.ts", "../src/common/decorators/public.decorator.ts"], "fileIdsList": [[53, 62, 66, 74, 76, 79, 80], [60, 67, 68, 74, 75, 81], [61, 64, 70, 75, 81, 82, 84], [53, 62, 74], [62], [61, 62, 63], [53, 63], [54, 61, 64, 70, 73, 84, 85, 89, 96, 101], [55, 56], [56, 57, 58, 59, 60], [53, 55], [53], [56], [53, 102, 103], [60, 67, 68, 71], [61, 71, 72], [53, 62, 66, 76, 78], [67, 76, 77, 79, 80], [61, 64, 70, 77, 79, 80, 83], [53, 62, 76, 77, 79], [60, 65, 66, 67, 68], [61, 64, 66, 69], [53, 62, 65], [60, 67, 68, 86, 87], [61, 64, 87, 88], [53, 62, 86], [92], [53, 62, 66, 78, 90, 91, 93], [67, 90, 91, 94], [61, 64, 70, 91, 93, 94, 95], [53, 62, 90], [53, 62, 66, 81, 94, 97], [60, 67, 68, 97, 98, 99], [61, 64, 70, 84, 85, 96, 98, 99, 100], [53, 62, 97]], "fileInfos": [{"version": "69684132aeb9b5642cbcd9e22dff7818ff0ee1aa831728af0ecf97d3364d5546", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "45b7ab580deca34ae9729e97c13cfd999df04416a79116c3bfb483804f85ded4", "impliedFormat": 1}, {"version": "3facaf05f0c5fc569c5649dd359892c98a85557e3e0c847964caeb67076f4d75", "impliedFormat": 1}, {"version": "e44bb8bbac7f10ecc786703fe0a6a4b952189f908707980ba8f3c8975a760962", "impliedFormat": 1}, {"version": "5e1c4c362065a6b95ff952c0eab010f04dcd2c3494e813b493ecfd4fcb9fc0d8", "impliedFormat": 1}, {"version": "68d73b4a11549f9c0b7d352d10e91e5dca8faa3322bfb77b661839c42b1ddec7", "impliedFormat": 1}, {"version": "5efce4fc3c29ea84e8928f97adec086e3dc876365e0982cc8479a07954a3efd4", "impliedFormat": 1}, {"version": "092c2bfe125ce69dbb1223c85d68d4d2397d7d8411867b5cc03cec902c233763", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "07f073f19d67f74d732b1adea08e1dc66b1b58d77cb5b43931dee3d798a2fd53", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "d7a3c8b952931daebdfc7a2897c53c0a1c73624593fa070e46bd537e64dcd20a", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "80e18897e5884b6723488d4f5652167e7bb5024f946743134ecc4aa4ee731f89", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "cd034f499c6cdca722b60c04b5b1b78e058487a7085a8e0d6fb50809947ee573", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "c57796738e7f83dbc4b8e65132f11a377649c00dd3eee333f672b8f0a6bea671", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "dc2df20b1bcdc8c2d34af4926e2c3ab15ffe1160a63e58b7e09833f616efff44", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "515d0b7b9bea2e31ea4ec968e9edd2c39d3eebf4a2d5cbd04e88639819ae3b71", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "0559b1f683ac7505ae451f9a96ce4c3c92bdc71411651ca6ddb0e88baaaad6a3", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "0dc1e7ceda9b8b9b455c3a2d67b0412feab00bd2f66656cd8850e8831b08b537", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "ce691fb9e5c64efb9547083e4a34091bcbe5bdb41027e310ebba8f7d96a98671", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "8d697a2a929a5fcb38b7a65594020fcef05ec1630804a33748829c5ff53640d0", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "4ff2a353abf8a80ee399af572debb8faab2d33ad38c4b4474cff7f26e7653b8d", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "936e80ad36a2ee83fc3caf008e7c4c5afe45b3cf3d5c24408f039c1d47bdc1df", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "d15bea3d62cbbdb9797079416b8ac375ae99162a7fba5de2c6c505446486ac0a", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "68d18b664c9d32a7336a70235958b8997ebc1c3b8505f4f1ae2b7e7753b87618", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "eb3d66c8327153d8fa7dd03f9c58d351107fe824c79e9b56b462935176cdf12a", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "38f0219c9e23c915ef9790ab1d680440d95419ad264816fa15009a8851e79119", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "69ab18c3b76cd9b1be3d188eaf8bba06112ebbe2f47f6c322b5105a6fbc45a2e", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "fef8cfad2e2dc5f5b3d97a6f4f2e92848eb1b88e897bb7318cef0e2820bceaab", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "2f11ff796926e0832f9ae148008138ad583bd181899ab7dd768a2666700b1893", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "4de680d5bb41c17f7f68e0419412ca23c98d5749dcaaea1896172f06435891fc", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "954296b30da6d508a104a3a0b5d96b76495c709785c1d11610908e63481ee667", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "ac9538681b19688c8eae65811b329d3744af679e0bdfa5d842d0e32524c73e1c", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "0a969edff4bd52585473d24995c5ef223f6652d6ef46193309b3921d65dd4376", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "9e9fbd7030c440b33d021da145d3232984c8bb7916f277e8ffd3dc2e3eae2bdb", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "811ec78f7fefcabbda4bfa93b3eb67d9ae166ef95f9bff989d964061cbf81a0c", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "717937616a17072082152a2ef351cb51f98802fb4b2fdabd32399843875974ca", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "d7e7d9b7b50e5f22c915b525acc5a49a7a6584cf8f62d0569e557c5cfc4b2ac2", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "71c37f4c9543f31dfced6c7840e068c5a5aacb7b89111a4364b1d5276b852557", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "576711e016cf4f1804676043e6a0a5414252560eb57de9faceee34d79798c850", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "89c1b1281ba7b8a96efc676b11b264de7a8374c5ea1e6617f11880a13fc56dc6", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "74f7fa2d027d5b33eb0471c8e82a6c87216223181ec31247c357a3e8e2fddc5b", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "d6d7ae4d1f1f3772e2a3cde568ed08991a8ae34a080ff1151af28b7f798e22ca", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "063600664504610fe3e99b717a1223f8b1900087fab0b4cad1496a114744f8df", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "934019d7e3c81950f9a8426d093458b65d5aff2c7c1511233c0fd5b941e608ab", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "52ada8e0b6e0482b728070b7639ee42e83a9b1c22d205992756fe020fd9f4a47", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "3bdefe1bfd4d6dee0e26f928f93ccc128f1b64d5d501ff4a8cf3c6371200e5e6", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "59fb2c069260b4ba00b5643b907ef5d5341b167e7d1dbf58dfd895658bda2867", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "639e512c0dfc3fad96a84caad71b8834d66329a1f28dc95e3946c9b58176c73a", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "368af93f74c9c932edd84c58883e736c9e3d53cec1fe24c0b0ff451f529ceab1", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "8e7f8264d0fb4c5339605a15daadb037bf238c10b654bb3eee14208f860a32ea", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "782dec38049b92d4e85c1585fbea5474a219c6984a35b004963b00beb1aab538", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "1305d1e76ca44e30fb8b2b8075fa522b83f60c0bcf5d4326a9d2cf79b53724f8", "impliedFormat": 1}, {"version": "a82ddd467d9427f681c8a64720435c31920815b64cd21c14941ee7fbe67f232c", "signature": "8e609bb71c20b858c77f0e9f90bb1319db8477b13f9f965f1a1e18524bf50881"}, {"version": "82b238d8b80324667de4ab4d2a170f5e1c03ef8d2898aad4469284388410d307", "signature": "0fc9bafee292ed294ba65c0707f71049ef19d75d1089a6442c153903ee6d313e"}, {"version": "dd39faf514c3da7d53fd82a1259c1a1166d2d758eb64cbc492c79130dde4e655", "signature": "bbc394ad2a2ec9c5dbcd9f60d4b365bf809989c90dd3c202b20fddc20e699bdf"}, {"version": "90309e1998e926431dd39295c387dc0155b542d9809931d7f3c23bfa3ff67a69", "signature": "56bdef4d35f6a6be6089223c4c05969e2d2e4ba222406c6da1606af7f2b7cc9b"}, {"version": "919f7ed8ec5a12fbbcef5467505314ceffdf56e3554aed83656a1ca3aa58de54", "signature": "ba827d60198b0634dce6c4e0749fca6a1799d85dc1a01f6e44d8cdfa04087738"}, {"version": "43d3473028c5fdae0160d4acd32376025b0bd9910e21344a1b25660cc8b9a42d", "signature": "459331aed1567d5f009fdec2523dad3f8d034f427b888b69353ae3775cdd351a"}, {"version": "e4a5e0b96973373c11a6df09f40b4aed55dd9c1f7d0e810fe659a6245f88400f", "signature": "d8447024dcd082098a39c80bf47654c42017ec119e140c01540f4e2dd9f6061c"}, {"version": "2fbdf9e3f9a94a3f98724d76f09bb85df75c6a0761b21561546b7d97ccc1e055", "signature": "0f3ec048f5337a4c90e2a0ef3879aa38db8d8f5a7a7116fc3917fa66a4e7244f"}, {"version": "66b8f697aadd282418447f91c84e01c89d092e8a08bb4eed01f1b31100d77225", "signature": "9175d65c13d5af160dd6ec6b1dbb5b230f54dece45f576c3ec35bd3c52771581"}, {"version": "8dfb83bce35d3dea883833b2237833b4c129f34801e7aefec8fed5cb0bf78569", "signature": "a28b5c0c372fb375910b3fe3c3ce4331509bc18ccef7cc39c9ee9d8daf8225d1"}, {"version": "8f653415fb290cefd9b16f61128c548c2c749e333983488a53282d9b41773f0e", "signature": "a356a4d6e0e508c5c23cd6caadf1213cc3b3bfca2be4cff17ffaffb20aabf9f6"}, {"version": "ca5a705db5e285edbdee6a856f6c236362046b489f3f0f679f1bc422666d9852", "signature": "ad41b6ac80b4e13b3c79b889647f2c2e58b59ccf453a74b4455415902f080639"}, {"version": "5fe400a594d99f0e820fb82f672a037c86cef02b7a67bbdca5f059f61c784a7a", "signature": "bb7bdf5f43a301a08726a030581f1e1fe9d07b3470adaaadc3c6496d9fa95d75"}, {"version": "1c9b1e1ba0fd2a9644141fad49b26d48142588d00b14973205ddacf1cd47bc4f", "signature": "2fff6ccffff814a5b558ba462cb8179574a769c928daa9091f9cad6f7d2524c3"}, {"version": "97ca59bc085cc4766e4a27567d2d9cf0ffef7cfe76079c77678902bda8fbb007", "signature": "1ef441938530dda1a9c178e7b3897780cb5951de3c7931b48b9328b7dfffc766"}, {"version": "6b9c1bc32e6baadd039ed0846539783dfc22abdb3b73a4e2bfa9521e348f79c0", "signature": "0b272bea29097b4ba0c592cdf9f445b04eeb070d265e8d38923d29b3291df97c"}, {"version": "b07d578272383f5d9a71684f14da22ddb6cfa553bce1259b07b24566c94aab9e", "signature": "626511247cd572556678bf33bef3652dc3146328293ddf2042d510b9bfa7b7fe"}, {"version": "e855b5d403bc928959266f11f6be9fc448f597b9b0064e0bca8efd17dc0afc44", "signature": "f63bc25c23ab01ff5614407d4a6f70812d8abe8621eb766c6aeee6d2536086a0"}, {"version": "1b5acde31ba29df2019b42a93dda887f0e308738e959765f5b4655812c9074c0", "signature": "0529292d9763ea410a482da3134375895e664a822e2bb200eaae1896d893b1a2"}, {"version": "2cf76eccfa80ac83efc3825787f42d193fe91e6c6f2be3de97423ef2e2527dff", "signature": "57cc8b30f818989aaa132e65b2c84e68c396d690d10f1c42f8a99865b9d80551"}, {"version": "9a5d3a8d18d2b0ab2d5ffdc08432908cc1661f8e40553b05636f6de15d94658f", "signature": "5695c31f088ca1d62fd209c653e558e743390f8655a199766bea6a7daf6aedba"}, {"version": "799bfbb93973d398ab9aab4e1cd803dd072b8f439419426e9e0d68aa0ab05c6e", "signature": "fc78ae4b1da9e295b475ebd6013dba84a96e09291ff9dd0ac6c9243527a5af41"}, {"version": "16882fd9e9dbd07908f9d78f4a3c33988cfbdd31c90dec8743d8e70f6a7aabcc", "signature": "4f2bf8a4f4fbbf7f7764c770608d2d942eff7a95d39928c3eb8b6d1772d490be"}, {"version": "0363ad078cb9fe6e99613412ab883dc8464616296dac2e43420512839aa66e89", "signature": "6a66b90ba9cd6f71f2f610b3d8db6accb1bfc382acbc1fc27dbcd2caab4be8e2"}, {"version": "3fa2ee86cc167e9e917a6c7002e40e88044ce7ce867316d7da790c531ed87ec7", "signature": "8813c2bac7423557770175d77443acaf013f6d9197c79e3f98bd53943252cef8"}, {"version": "385a17cfa8329b79b70d487395e85a543f4cbe437c9b1505a32a2a7fea7da64e", "signature": "243a636433f51f874f37b77b7d19a1376dcb80a7de4de1da51372fa5bf592d81"}, {"version": "1d7ee0c4eb734d59b6d962bc9151f6330895067cd3058ce6a3cd95347ef5c6e8", "impliedFormat": 99}, {"version": "5ca9a1cf8200c50b8ff03074e0d496c47d150c16792213e672a466a7bf382655", "signature": "8c003f4125424dc72a27c1360e64123a6d06288c90cc5c79ad25bb40fb9ca4cf"}, {"version": "4949eb234c8e65e18daf26e6eb0faa2f8d8285f8cf121ef9475c07ba89b0621a", "signature": "426bd01e9c5c28fef75c625a75812d73298ab14951bb54840ec68aae6c03a7e1"}, {"version": "aba72f88c66c47b38bd8dc94f00ec5a5647fb8528577e2bf7a94f44aeb010315", "signature": "3a98b458420048f6c6207bec0ec089776bf89eca9b458c29606533bb61590a8a"}, {"version": "0fc6089622db560d801b00051e5030be32abf0343b9ec1b74690d8916f2653ad", "signature": "5909cb2d7a58524b840130914a57c357d42704ed06f14e6c510cfeb6b6f7d16c"}, {"version": "bd104bd4df3265387f94c6d65d383a46d420b767f28089851fb096c026804288", "signature": "9fd4a7411e65e8be2f4e0c680dd18f1078d29846e9ffd1b6779fad56668947ed"}, {"version": "b782b342463a5541acc2245478cac80c103cf0ed40d39ed179a07ac704ee12f6", "signature": "029649377e649f7661ca2808fdf68bbb6a71ff3999116a3f6dce5fe828c2376f"}, {"version": "44a86135e720e85cc9971459da4c6d227ee403dd2a87f2238726b4e8cb6e464f", "signature": "2305bbb92e469e3cd93fafe7b1b681259ccf689201a3ad27f2a0807fc7759618"}, {"version": "46ac17a66b393438c7180035f5e961556be65ec44c7774626487231d52d10168", "signature": "119c028f420e6aad2edac03f9769fbe08f92aa352f872af800198085bfe19674"}, {"version": "8634874290b4d15c9b20afdc436bdd8d0ab81e57b7d4c67f2dd4414b7cb641ab", "signature": "171c5d690f6dbec4de175535d1269e9b65ced244332df72974bf1da32b10bc88"}, {"version": "579f911e8250b6a0aa10f74a83bcaff4d0fc692acc60a183d18262c2374248c3", "signature": "559d8105e881770ac69f45e3722b8f54d14de08ef6290d978844724c623df0a3"}, {"version": "8927bc5092cbb6c82e0b35033fcf73928abad9633b9c39492e476af5696fa597", "signature": "d86c8a6052dfb244a17ae3c137e492a3c1fe3dad16d512c1348f6bec8c35e173"}, {"version": "9393a2face880525ca8fa70126b9e4e6da8da522240c57690cd22e7a8f3342a1", "signature": "7f67b66fdc00e62f37f48f5a458a16ec7ae76df17781d3e3b8c932d3b5b05fc1"}, {"version": "6936d64a5f738cd84c62bd52d436f38884225736f4ba1e17a6b7a0ab16fc3f4d", "signature": "e5bcbec27e3bd5153814baf7d3ac073c8bf6288d722ad8fc8fcf54347a6d216c"}, {"version": "47f99be907d29eb34194d881a0b2d302a09cca1d60c0569b06ec6d6c3cf84049", "impliedFormat": 1}, {"version": "dba92bc77804919a2e4a377c4024621fe35723eeb3b807dbd04d6af7353ab6f1", "signature": "dd58575cc65eb62c1d1e4a5cc3396dfd2d7498f1710485c18200c7fe23b824d7"}, {"version": "da5b712e76da67bf9ecb0f4618a8dd6cfc5c7b2910f0b1860b510a2181b4e667", "signature": "2215d1e030f417376d310a8dd219cf73993d04eef415e9d6bfb5b6866d9a68f2"}, {"version": "ad799cff819f80c2b2bc3853331b9dba8e59caae151be1661c5c831f31da1a70", "signature": "883e37eb80c6e8eab3efd8722668d56db83502368260b2cc2af5094720d4add4"}, {"version": "88e69a0cd35f2808e5981cda78fb964fbb07f2d6adad1246f07270a3e741f927", "signature": "6560c1a6122e7e371e8b65746b40196af3bcf4a09b3bbaa580e4b6a5315e78c3"}, {"version": "67a64347696e4cdc0fdcd926a3952aa3b329d9a18f1085df3379be8769a0c80a", "signature": "32f749490fdd42bd923fbef854284375a583b07b7705dc6ba8057fbadc827e77"}, {"version": "2615043af6befce5a5981a4442627aebfe9b9ea3a89ba7243b9b7664d9557039", "signature": "c492bc52d40c916dbbf4e2c51dab854b993f252f46ab0fdf8a872b41b2eb45ae"}, {"version": "fc4a88c66b8e150fdde02820af1eecbce5d08faee229d45aa3cd0de291dec793", "signature": "a1872b8473be7a050b0a531b5017fabb4baef1d56bf55b26b2a6a9389975cbee"}, {"version": "6fe5fbc365fbc462c251a01423a04624f4b553bdfa1cec2542ebb3ac4fbd6847", "signature": "b3bc71c12a495e59f63a4f3380e77c4f4cdbbee614894eed6afff200f3a64869"}, {"version": "f2ad5ea25171d4d6ffbfb0be160dee0cfd0945f3197d24b3f9918e7211cfca44", "signature": "508d95109c96fdd5c8b83d4dd1baf03021bfc45f7518abe45ffc51e0a2b1d70d"}, {"version": "66021824d2b5b8ff973d811a78f7cc1c2a60e315e7861a39ca4f073ccd8ba895", "signature": "b82491e2990291580288c5602d4c017238977749d52b17391f0e45d9a29be644"}, {"version": "a67ac48c3bccbfbb8a2d1b3e124ae0e6708226bc905d8954ec1a4ae48d9236ff", "signature": "44c4efb68ad04ba6fdc47bc6469f76535e31f279332a8605653ee893cf11abac"}, {"version": "4e710ecd444677aa731116dfae3187be6ed84a4f88bcd1f9827241148ffdf0bb", "signature": "8e609bb71c20b858c77f0e9f90bb1319db8477b13f9f965f1a1e18524bf50881"}, {"version": "8f5ec76bfcd77b77f75f97d377d2a56e609ab320771bbea6f4b4c1b1734d7368", "signature": "5e8c81351dba92fe6416926335e650c266e48ef847b75c9d57f241f47231b490"}], "root": [[52, 77], [79, 91], [93, 105]], "options": {"allowSyntheticDefaultImports": true, "declaration": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "module": 1, "noFallthroughCasesInSwitch": false, "noImplicitAny": false, "outDir": "./", "removeComments": true, "skipLibCheck": true, "sourceMap": true, "strictBindCallApply": false, "strictNullChecks": false, "target": 7}, "referencedMap": [[81, 1], [82, 2], [85, 3], [75, 4], [63, 5], [64, 6], [62, 7], [102, 8], [57, 9], [61, 10], [56, 11], [58, 12], [59, 13], [104, 14], [72, 15], [73, 16], [71, 12], [54, 12], [79, 17], [83, 18], [84, 19], [80, 20], [69, 21], [70, 22], [66, 23], [88, 24], [89, 25], [87, 26], [93, 27], [94, 28], [95, 29], [96, 30], [91, 31], [99, 32], [100, 33], [101, 34], [98, 35]], "semanticDiagnosticsPerFile": [[52, [{"start": 29, "length": 16, "messageText": "Cannot find module '@prisma/client' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 71, "length": 10, "messageText": "Cannot find module 'bcryptjs' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 4132, "length": 7, "messageText": "Cannot find name 'process'. Do you need to install type definitions for node? Try `npm i --save-dev @types/node`.", "category": 1, "code": 2580}]], [53, [{"start": 76, "length": 16, "messageText": "Cannot find module '@nestjs/common' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 123, "length": 16, "messageText": "Cannot find module '@prisma/client' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 171, "length": 16, "messageText": "Cannot find module '@nestjs/config' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 628, "length": 8, "code": 2339, "category": 1, "messageText": "Property '$connect' does not exist on type 'PrismaService'."}, {"start": 688, "length": 11, "code": 2339, "category": 1, "messageText": "Property '$disconnect' does not exist on type 'PrismaService'."}, {"start": 770, "length": 3, "code": 2339, "category": 1, "messageText": "Property '$on' does not exist on type 'PrismaService'."}, {"start": 1072, "length": 12, "code": 2551, "category": 1, "messageText": "Property 'organization' does not exist on type 'PrismaService'. Did you mean 'withOrganization'?", "relatedInformation": [{"start": 848, "length": 16, "messageText": "'withOrganization' is declared here.", "category": 3, "code": 2728}]}]], [54, [{"start": 31, "length": 16, "messageText": "Cannot find module '@nestjs/common' or its corresponding type declarations.", "category": 1, "code": 2307}]], [55, [{"start": 65, "length": 17, "messageText": "Cannot find module 'class-validator' or its corresponding type declarations.", "category": 1, "code": 2307}]], [56, [{"start": 71, "length": 16, "messageText": "Cannot find module '@nestjs/common' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 116, "length": 13, "messageText": "Cannot find module '@nestjs/jwt' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 155, "length": 10, "messageText": "Cannot find module 'bcryptjs' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 509, "length": 4, "code": 2339, "category": 1, "messageText": "Property 'user' does not exist on type 'PrismaService'."}, {"start": 1220, "length": 4, "code": 2339, "category": 1, "messageText": "Property 'user' does not exist on type 'PrismaService'."}, {"start": 1949, "length": 4, "code": 2339, "category": 1, "messageText": "Property 'user' does not exist on type 'PrismaService'."}, {"start": 2152, "length": 12, "code": 2551, "category": 1, "messageText": "Property 'organization' does not exist on type 'PrismaService'. Did you mean 'withOrganization'?", "relatedInformation": [{"file": "../src/prisma/prisma.service.ts", "start": 848, "length": 16, "messageText": "'withOrganization' is declared here.", "category": 3, "code": 2728}]}, {"start": 2452, "length": 12, "code": 2339, "category": 1, "messageText": "Property '$transaction' does not exist on type 'PrismaService'."}, {"start": 5313, "length": 4, "code": 2339, "category": 1, "messageText": "Property 'user' does not exist on type 'PrismaService'."}]], [57, [{"start": 64, "length": 16, "messageText": "Cannot find module '@nestjs/common' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 108, "length": 18, "messageText": "Cannot find module '@nestjs/passport' or its corresponding type declarations.", "category": 1, "code": 2307}]], [58, [{"start": 50, "length": 16, "messageText": "Cannot find module '@nestjs/common' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 101, "length": 18, "messageText": "Cannot find module '@nestjs/passport' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 158, "length": 14, "messageText": "Cannot find module 'passport-jwt' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 204, "length": 16, "messageText": "Cannot find module '@nestjs/config' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 707, "length": 4, "code": 2339, "category": 1, "messageText": "Property 'user' does not exist on type 'PrismaService'."}]], [59, [{"start": 50, "length": 16, "messageText": "Cannot find module '@nestjs/common' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 101, "length": 18, "messageText": "Cannot find module '@nestjs/passport' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 146, "length": 16, "messageText": "Cannot find module 'passport-local' or its corresponding type declarations.", "category": 1, "code": 2307}]], [60, [{"start": 78, "length": 16, "messageText": "Cannot find module '@nestjs/common' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 122, "length": 14, "messageText": "Cannot find module '@nestjs/core' or its corresponding type declarations.", "category": 1, "code": 2307}]], [61, [{"start": 23, "length": 16, "messageText": "Cannot find module '@nestjs/common' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 67, "length": 13, "messageText": "Cannot find module '@nestjs/jwt' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 113, "length": 18, "messageText": "Cannot find module '@nestjs/passport' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 163, "length": 16, "messageText": "Cannot find module '@nestjs/config' or its corresponding type declarations.", "category": 1, "code": 2307}]], [62, [{"start": 27, "length": 16, "messageText": "Cannot find module '@nestjs/common' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 664, "length": 10, "code": 2339, "category": 1, "messageText": "Property 'aPIMXEvent' does not exist on type 'PrismaService'."}, {"start": 1563, "length": 10, "code": 2339, "category": 1, "messageText": "Property 'aPIMXEvent' does not exist on type 'PrismaService'."}, {"start": 2012, "length": 10, "code": 2339, "category": 1, "messageText": "Property 'aPIMXEvent' does not exist on type 'PrismaService'."}]], [63, [{"start": 155, "length": 20, "messageText": "Cannot find module '@nestjs/websockets' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 208, "length": 11, "messageText": "Cannot find module 'socket.io' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 267, "length": 16, "messageText": "Cannot find module '@nestjs/common' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 312, "length": 13, "messageText": "Cannot find module '@nestjs/jwt' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 549, "length": 7, "messageText": "Cannot find name 'process'. Do you need to install type definitions for node? Try `npm i --save-dev @types/node`.", "category": 1, "code": 2580}, {"start": 1226, "length": 9, "code": 2339, "category": 1, "messageText": "Property 'handshake' does not exist on type 'AuthenticatedSocket'."}, {"start": 1257, "length": 9, "code": 2339, "category": 1, "messageText": "Property 'handshake' does not exist on type 'AuthenticatedSocket'."}, {"start": 1356, "length": 10, "code": 2339, "category": 1, "messageText": "Property 'disconnect' does not exist on type 'AuthenticatedSocket'."}, {"start": 1617, "length": 2, "code": 2339, "category": 1, "messageText": "Property 'id' does not exist on type 'AuthenticatedSocket'."}, {"start": 1798, "length": 2, "code": 2339, "category": 1, "messageText": "Property 'id' does not exist on type 'AuthenticatedSocket'."}, {"start": 2019, "length": 2, "code": 2339, "category": 1, "messageText": "Property 'id' does not exist on type 'AuthenticatedSocket'."}, {"start": 2044, "length": 4, "code": 2339, "category": 1, "messageText": "Property 'join' does not exist on type 'AuthenticatedSocket'."}, {"start": 2101, "length": 4, "code": 2339, "category": 1, "messageText": "Property 'join' does not exist on type 'AuthenticatedSocket'."}, {"start": 2183, "length": 2, "code": 2339, "category": 1, "messageText": "Property 'id' does not exist on type 'AuthenticatedSocket'."}, {"start": 2260, "length": 4, "code": 2339, "category": 1, "messageText": "Property 'emit' does not exist on type 'AuthenticatedSocket'."}, {"start": 2305, "length": 2, "code": 2339, "category": 1, "messageText": "Property 'id' does not exist on type 'AuthenticatedSocket'."}, {"start": 2518, "length": 2, "code": 2339, "category": 1, "messageText": "Property 'id' does not exist on type 'AuthenticatedSocket'."}, {"start": 2546, "length": 10, "code": 2339, "category": 1, "messageText": "Property 'disconnect' does not exist on type 'AuthenticatedSocket'."}, {"start": 2667, "length": 2, "code": 2339, "category": 1, "messageText": "Property 'id' does not exist on type 'AuthenticatedSocket'."}, {"start": 2793, "length": 2, "code": 2339, "category": 1, "messageText": "Property 'id' does not exist on type 'AuthenticatedSocket'."}, {"start": 3081, "length": 2, "code": 2339, "category": 1, "messageText": "Property 'id' does not exist on type 'AuthenticatedSocket'."}, {"start": 3292, "length": 2, "code": 2339, "category": 1, "messageText": "Property 'id' does not exist on type 'AuthenticatedSocket'."}, {"start": 3666, "length": 2, "code": 2339, "category": 1, "messageText": "Property 'id' does not exist on type 'AuthenticatedSocket'."}, {"start": 3689, "length": 4, "code": 2339, "category": 1, "messageText": "Property 'join' does not exist on type 'AuthenticatedSocket'."}, {"start": 3735, "length": 4, "code": 2339, "category": 1, "messageText": "Property 'emit' does not exist on type 'AuthenticatedSocket'."}, {"start": 3834, "length": 2, "code": 2339, "category": 1, "messageText": "Property 'id' does not exist on type 'AuthenticatedSocket'."}, {"start": 4184, "length": 2, "code": 2339, "category": 1, "messageText": "Property 'id' does not exist on type 'AuthenticatedSocket'."}, {"start": 4327, "length": 5, "code": 2339, "category": 1, "messageText": "Property 'leave' does not exist on type 'AuthenticatedSocket'."}, {"start": 4374, "length": 4, "code": 2339, "category": 1, "messageText": "Property 'emit' does not exist on type 'AuthenticatedSocket'."}, {"start": 4471, "length": 2, "code": 2339, "category": 1, "messageText": "Property 'id' does not exist on type 'AuthenticatedSocket'."}, {"start": 4612, "length": 4, "code": 2339, "category": 1, "messageText": "Property 'emit' does not exist on type 'AuthenticatedSocket'."}]], [64, [{"start": 23, "length": 16, "messageText": "Cannot find module '@nestjs/common' or its corresponding type declarations.", "category": 1, "code": 2307}]], [65, [{"start": 83, "length": 17, "messageText": "Cannot find module 'class-validator' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 123, "length": 19, "messageText": "Cannot find module 'class-transformer' or its corresponding type declarations.", "category": 1, "code": 2307}]], [66, [{"start": 87, "length": 16, "messageText": "Cannot find module '@nestjs/common' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 329, "length": 16, "messageText": "Cannot find module '@prisma/client' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 686, "length": 7, "code": 2339, "category": 1, "messageText": "Property 'session' does not exist on type 'PrismaService'."}, {"start": 1906, "length": 7, "code": 2339, "category": 1, "messageText": "Property 'session' does not exist on type 'PrismaService'."}, {"start": 2251, "length": 7, "code": 2339, "category": 1, "messageText": "Property 'session' does not exist on type 'PrismaService'."}, {"start": 2550, "length": 7, "code": 2339, "category": 1, "messageText": "Property 'session' does not exist on type 'PrismaService'."}, {"start": 3334, "length": 7, "code": 2339, "category": 1, "messageText": "Property 'session' does not exist on type 'PrismaService'."}, {"start": 3834, "length": 7, "code": 2339, "category": 1, "messageText": "Property 'session' does not exist on type 'PrismaService'."}, {"start": 5062, "length": 7, "code": 2339, "category": 1, "messageText": "Property 'session' does not exist on type 'PrismaService'."}, {"start": 5811, "length": 7, "code": 2339, "category": 1, "messageText": "Property 'session' does not exist on type 'PrismaService'."}, {"start": 6527, "length": 7, "code": 2339, "category": 1, "messageText": "Property 'session' does not exist on type 'PrismaService'."}, {"start": 7176, "length": 7, "code": 2339, "category": 1, "messageText": "Property 'session' does not exist on type 'PrismaService'."}, {"start": 7220, "length": 7, "code": 2339, "category": 1, "messageText": "Property 'session' does not exist on type 'PrismaService'."}, {"start": 7296, "length": 7, "code": 2339, "category": 1, "messageText": "Property 'session' does not exist on type 'PrismaService'."}, {"start": 7401, "length": 7, "code": 2339, "category": 1, "messageText": "Property 'session' does not exist on type 'PrismaService'."}]], [67, [{"start": 68, "length": 16, "messageText": "Cannot find module '@nestjs/common' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 112, "length": 18, "messageText": "Cannot find module '@nestjs/passport' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 158, "length": 14, "messageText": "Cannot find module '@nestjs/core' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 201, "length": 6, "messageText": "Cannot find module 'rxjs' or its corresponding type declarations.", "category": 1, "code": 2307}]], [68, [{"start": 55, "length": 16, "messageText": "Cannot find module '@nestjs/common' or its corresponding type declarations.", "category": 1, "code": 2307}]], [69, [{"start": 114, "length": 16, "messageText": "Cannot find module '@nestjs/common' or its corresponding type declarations.", "category": 1, "code": 2307}]], [70, [{"start": 23, "length": 16, "messageText": "Cannot find module '@nestjs/common' or its corresponding type declarations.", "category": 1, "code": 2307}]], [71, [{"start": 66, "length": 16, "messageText": "Cannot find module '@nestjs/common' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 331, "length": 12, "code": 2551, "category": 1, "messageText": "Property 'organization' does not exist on type 'PrismaService'. Did you mean 'withOrganization'?", "relatedInformation": [{"file": "../src/prisma/prisma.service.ts", "start": 848, "length": 16, "messageText": "'withOrganization' is declared here.", "category": 3, "code": 2728}]}, {"start": 706, "length": 12, "code": 2551, "category": 1, "messageText": "Property 'organization' does not exist on type 'PrismaService'. Did you mean 'withOrganization'?", "relatedInformation": [{"file": "../src/prisma/prisma.service.ts", "start": 848, "length": 16, "messageText": "'withOrganization' is declared here.", "category": 3, "code": 2728}]}, {"start": 1022, "length": 5, "code": 2339, "category": 1, "messageText": "Property 'quota' does not exist on type 'PrismaService'."}, {"start": 1228, "length": 5, "code": 2339, "category": 1, "messageText": "Property 'quota' does not exist on type 'PrismaService'."}, {"start": 1972, "length": 12, "code": 2339, "category": 1, "messageText": "Property 'billingUsage' does not exist on type 'PrismaService'."}]], [72, [{"start": 63, "length": 16, "messageText": "Cannot find module '@nestjs/common' or its corresponding type declarations.", "category": 1, "code": 2307}]], [73, [{"start": 23, "length": 16, "messageText": "Cannot find module '@nestjs/common' or its corresponding type declarations.", "category": 1, "code": 2307}]], [74, [{"start": 103, "length": 17, "messageText": "Cannot find module 'class-validator' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 143, "length": 19, "messageText": "Cannot find module 'class-transformer' or its corresponding type declarations.", "category": 1, "code": 2307}]], [75, [{"start": 87, "length": 16, "messageText": "Cannot find module '@nestjs/common' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 736, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'template' does not exist on type 'PrismaService'."}, {"start": 1547, "length": 5, "code": 2339, "category": 1, "messageText": "Property 'agent' does not exist on type 'PrismaService'."}, {"start": 3025, "length": 5, "code": 2339, "category": 1, "messageText": "Property 'agent' does not exist on type 'PrismaService'."}, {"start": 3344, "length": 5, "code": 2339, "category": 1, "messageText": "Property 'agent' does not exist on type 'PrismaService'."}, {"start": 3620, "length": 5, "code": 2339, "category": 1, "messageText": "Property 'agent' does not exist on type 'PrismaService'."}, {"start": 4264, "length": 5, "code": 2339, "category": 1, "messageText": "Property 'agent' does not exist on type 'PrismaService'."}, {"start": 5070, "length": 7, "code": 2339, "category": 1, "messageText": "Property 'session' does not exist on type 'PrismaService'."}, {"start": 5415, "length": 5, "code": 2339, "category": 1, "messageText": "Property 'agent' does not exist on type 'PrismaService'."}, {"start": 6178, "length": 5, "code": 2339, "category": 1, "messageText": "Property 'agent' does not exist on type 'PrismaService'."}, {"start": 7077, "length": 5, "code": 2339, "category": 1, "messageText": "Property 'agent' does not exist on type 'PrismaService'."}, {"start": 7756, "length": 5, "code": 2339, "category": 1, "messageText": "Property 'agent' does not exist on type 'PrismaService'."}, {"start": 8691, "length": 5, "code": 2339, "category": 1, "messageText": "Property 'agent' does not exist on type 'PrismaService'."}, {"start": 8753, "length": 5, "code": 2339, "category": 1, "messageText": "Property 'agent' does not exist on type 'PrismaService'."}, {"start": 8878, "length": 5, "code": 2339, "category": 1, "messageText": "Property 'agent' does not exist on type 'PrismaService'."}, {"start": 9003, "length": 7, "code": 2339, "category": 1, "messageText": "Property 'session' does not exist on type 'PrismaService'."}, {"start": 9184, "length": 5, "code": 2339, "category": 1, "messageText": "Property 'agent' does not exist on type 'PrismaService'."}, {"start": 9745, "length": 5, "code": 2339, "category": 1, "messageText": "Property 'agent' does not exist on type 'PrismaService'."}, {"start": 10312, "length": 5, "code": 2339, "category": 1, "messageText": "Property 'agent' does not exist on type 'PrismaService'."}]], [76, [{"start": 102, "length": 17, "messageText": "Cannot find module 'class-validator' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 142, "length": 19, "messageText": "Cannot find module 'class-transformer' or its corresponding type declarations.", "category": 1, "code": 2307}]], [77, [{"start": 35, "length": 16, "messageText": "Cannot find module '@nestjs/common' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 83, "length": 16, "messageText": "Cannot find module '@nestjs/config' or its corresponding type declarations.", "category": 1, "code": 2307}]], [79, [{"start": 56, "length": 16, "messageText": "Cannot find module '@nestjs/common' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 104, "length": 16, "messageText": "Cannot find module '@nestjs/config' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 389, "length": 8, "messageText": "Cannot find module 'openai' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 421, "length": 19, "messageText": "Cannot find module '@anthropic-ai/sdk' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 477, "length": 23, "messageText": "Cannot find module '@google/generative-ai' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 519, "length": 10, "messageText": "Cannot find module 'groq-sdk' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 19118, "length": 12, "code": 2339, "category": 1, "messageText": "Property 'billingUsage' does not exist on type 'PrismaService'."}, {"start": 19657, "length": 5, "code": 2339, "category": 1, "messageText": "Property 'quota' does not exist on type 'PrismaService'."}, {"start": 20115, "length": 5, "code": 2339, "category": 1, "messageText": "Property 'quota' does not exist on type 'PrismaService'."}]], [80, [{"start": 75, "length": 16, "messageText": "Cannot find module '@nestjs/common' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 234, "length": 16, "messageText": "Cannot find module '@nestjs/config' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 1554, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'provider' does not exist on type 'PrismaService'."}, {"start": 3366, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'provider' does not exist on type 'PrismaService'."}, {"start": 3506, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'provider' does not exist on type 'PrismaService'."}, {"start": 3791, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'provider' does not exist on type 'PrismaService'."}, {"start": 4409, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'provider' does not exist on type 'PrismaService'."}, {"start": 5495, "length": 7, "code": 2339, "category": 1, "messageText": "Property 'session' does not exist on type 'PrismaService'."}, {"start": 5826, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'provider' does not exist on type 'PrismaService'."}, {"start": 6341, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'provider' does not exist on type 'PrismaService'."}, {"start": 9373, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'provider' does not exist on type 'PrismaService'."}, {"start": 11625, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'provider' does not exist on type 'PrismaService'."}, {"start": 12443, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'provider' does not exist on type 'PrismaService'."}, {"start": 12897, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'provider' does not exist on type 'PrismaService'."}, {"start": 13269, "length": 7, "code": 2339, "category": 1, "messageText": "Property 'session' does not exist on type 'PrismaService'."}, {"start": 13666, "length": 12, "code": 2339, "category": 1, "messageText": "Property 'billingUsage' does not exist on type 'PrismaService'."}, {"start": 14005, "length": 10, "code": 2339, "category": 1, "messageText": "Property 'aPIMXEvent' does not exist on type 'PrismaService'."}, {"start": 15290, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'provider' does not exist on type 'PrismaService'."}, {"start": 15355, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'provider' does not exist on type 'PrismaService'."}, {"start": 15481, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'provider' does not exist on type 'PrismaService'."}, {"start": 15609, "length": 7, "code": 2339, "category": 1, "messageText": "Property 'session' does not exist on type 'PrismaService'."}, {"start": 15799, "length": 12, "code": 2339, "category": 1, "messageText": "Property 'billingUsage' does not exist on type 'PrismaService'."}, {"start": 20489, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'provider' does not exist on type 'PrismaService'."}, {"start": 21331, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'provider' does not exist on type 'PrismaService'."}]], [81, [{"start": 67, "length": 16, "messageText": "Cannot find module '@nestjs/common' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 490, "length": 16, "messageText": "Cannot find module '@nestjs/config' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 1671, "length": 5, "code": 2339, "category": 1, "messageText": "Property 'agent' does not exist on type 'PrismaService'."}, {"start": 4233, "length": 9, "code": 2559, "category": 1, "messageText": "Type '{ temperature?: number; maxTokens?: number; model?: string; }' has no properties in common with type '{ timeout?: number; maxRetries?: number; }'.", "relatedInformation": [{"file": "../src/providers/dto/provider.dto.ts", "start": 5793, "length": 9, "messageText": "The expected type comes from property 'overrides' which is declared here on type 'ProviderExecutionDto'", "category": 3, "code": 6500}]}, {"start": 7742, "length": 12, "code": 2339, "category": 1, "messageText": "Property 'billingUsage' does not exist on type 'PrismaService'."}, {"start": 8413, "length": 5, "code": 2339, "category": 1, "messageText": "Property 'agent' does not exist on type 'PrismaService'."}, {"start": 8625, "length": 5, "code": 2339, "category": 1, "messageText": "Property 'quota' does not exist on type 'PrismaService'."}, {"start": 9312, "length": 5, "code": 2339, "category": 1, "messageText": "Property 'agent' does not exist on type 'PrismaService'."}, {"start": 10543, "length": 12, "code": 2353, "category": 1, "messageText": "Object literal may only specify known properties, and 'capabilities' does not exist in type 'ProviderSelectionDto'."}, {"start": 10577, "length": 15, "code": 2339, "category": 1, "messageText": "Property 'CHAT_COMPLETION' does not exist on type 'typeof ProviderCapability'."}, {"start": 11104, "length": 20, "code": 2339, "category": 1, "messageText": "Property 'executeStreamingChat' does not exist on type 'ProviderExecutorService'."}, {"start": 14709, "length": 12, "code": 2353, "category": 1, "messageText": "Object literal may only specify known properties, and 'capabilities' does not exist in type 'ProviderSelectionDto'."}, {"start": 14743, "length": 15, "code": 2339, "category": 1, "messageText": "Property 'CHAT_COMPLETION' does not exist on type 'typeof ProviderCapability'."}, {"start": 15167, "length": 11, "code": 2339, "category": 1, "messageText": "Property 'executeChat' does not exist on type 'ProviderExecutorService'."}, {"start": 15859, "length": 12, "code": 2339, "category": 1, "messageText": "Property 'billingUsage' does not exist on type 'PrismaService'."}, {"start": 16254, "length": 5, "code": 2339, "category": 1, "messageText": "Property 'quota' does not exist on type 'PrismaService'."}, {"start": 16996, "length": 7, "code": 2339, "category": 1, "messageText": "Property 'session' does not exist on type 'PrismaService'."}, {"start": 17586, "length": 7, "code": 2339, "category": 1, "messageText": "Property 'session' does not exist on type 'PrismaService'."}, {"start": 17805, "length": 12, "code": 2339, "category": 1, "messageText": "Property 'billingUsage' does not exist on type 'PrismaService'."}, {"start": 18095, "length": 12, "code": 2339, "category": 1, "messageText": "Property 'billingUsage' does not exist on type 'PrismaService'."}, {"start": 18385, "length": 7, "code": 2339, "category": 1, "messageText": "Property 'session' does not exist on type 'PrismaService'."}]], [82, [{"start": 117, "length": 16, "messageText": "Cannot find module '@nestjs/common' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 160, "length": 9, "messageText": "Cannot find module 'express' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 214, "length": 6, "messageText": "Cannot find module 'rxjs' or its corresponding type declarations.", "category": 1, "code": 2307}]], [83, [{"start": 161, "length": 16, "messageText": "Cannot find module '@nestjs/common' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 9192, "length": 12, "code": 2339, "category": 1, "messageText": "Property 'billingUsage' does not exist on type 'PrismaService'."}]], [84, [{"start": 23, "length": 16, "messageText": "Cannot find module '@nestjs/common' or its corresponding type declarations.", "category": 1, "code": 2307}]], [85, [{"start": 23, "length": 16, "messageText": "Cannot find module '@nestjs/common' or its corresponding type declarations.", "category": 1, "code": 2307}]], [86, [{"start": 95, "length": 17, "messageText": "Cannot find module 'class-validator' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 135, "length": 19, "messageText": "Cannot find module 'class-transformer' or its corresponding type declarations.", "category": 1, "code": 2307}]], [87, [{"start": 87, "length": 16, "messageText": "Cannot find module '@nestjs/common' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 699, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'template' does not exist on type 'PrismaService'."}, {"start": 2369, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'template' does not exist on type 'PrismaService'."}, {"start": 2691, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'template' does not exist on type 'PrismaService'."}, {"start": 2976, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'template' does not exist on type 'PrismaService'."}, {"start": 3789, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'template' does not exist on type 'PrismaService'."}, {"start": 4162, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'template' does not exist on type 'PrismaService'."}, {"start": 4958, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'template' does not exist on type 'PrismaService'."}, {"start": 5384, "length": 5, "code": 2339, "category": 1, "messageText": "Property 'agent' does not exist on type 'PrismaService'."}, {"start": 5596, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'template' does not exist on type 'PrismaService'."}, {"start": 6082, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'template' does not exist on type 'PrismaService'."}, {"start": 8098, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'template' does not exist on type 'PrismaService'."}, {"start": 8662, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'template' does not exist on type 'PrismaService'."}, {"start": 9233, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'template' does not exist on type 'PrismaService'."}, {"start": 9776, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'template' does not exist on type 'PrismaService'."}, {"start": 9856, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'template' does not exist on type 'PrismaService'."}, {"start": 9986, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'template' does not exist on type 'PrismaService'."}, {"start": 10116, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'template' does not exist on type 'PrismaService'."}, {"start": 10734, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'template' does not exist on type 'PrismaService'."}, {"start": 10891, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'template' does not exist on type 'PrismaService'."}, {"start": 11202, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'template' does not exist on type 'PrismaService'."}, {"start": 11359, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'template' does not exist on type 'PrismaService'."}, {"start": 12282, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'template' does not exist on type 'PrismaService'."}]], [88, [{"start": 103, "length": 16, "messageText": "Cannot find module '@nestjs/common' or its corresponding type declarations.", "category": 1, "code": 2307}]], [89, [{"start": 23, "length": 16, "messageText": "Cannot find module '@nestjs/common' or its corresponding type declarations.", "category": 1, "code": 2307}]], [90, [{"start": 102, "length": 17, "messageText": "Cannot find module 'class-validator' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 142, "length": 19, "messageText": "Cannot find module 'class-transformer' or its corresponding type declarations.", "category": 1, "code": 2307}]], [91, [{"start": 87, "length": 16, "messageText": "Cannot find module '@nestjs/common' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 762, "length": 4, "code": 2339, "category": 1, "messageText": "Property 'tool' does not exist on type 'PrismaService'."}, {"start": 2227, "length": 4, "code": 2339, "category": 1, "messageText": "Property 'tool' does not exist on type 'PrismaService'."}, {"start": 2363, "length": 4, "code": 2339, "category": 1, "messageText": "Property 'tool' does not exist on type 'PrismaService'."}, {"start": 2636, "length": 4, "code": 2339, "category": 1, "messageText": "Property 'tool' does not exist on type 'PrismaService'."}, {"start": 3228, "length": 4, "code": 2339, "category": 1, "messageText": "Property 'tool' does not exist on type 'PrismaService'."}, {"start": 4157, "length": 7, "code": 2339, "category": 1, "messageText": "Property 'session' does not exist on type 'PrismaService'."}, {"start": 4500, "length": 4, "code": 2339, "category": 1, "messageText": "Property 'tool' does not exist on type 'PrismaService'."}, {"start": 5288, "length": 4, "code": 2339, "category": 1, "messageText": "Property 'tool' does not exist on type 'PrismaService'."}, {"start": 6215, "length": 4, "code": 2339, "category": 1, "messageText": "Property 'tool' does not exist on type 'PrismaService'."}, {"start": 6952, "length": 4, "code": 2339, "category": 1, "messageText": "Property 'tool' does not exist on type 'PrismaService'."}, {"start": 7825, "length": 4, "code": 2339, "category": 1, "messageText": "Property 'tool' does not exist on type 'PrismaService'."}, {"start": 7886, "length": 4, "code": 2339, "category": 1, "messageText": "Property 'tool' does not exist on type 'PrismaService'."}, {"start": 8008, "length": 4, "code": 2339, "category": 1, "messageText": "Property 'tool' does not exist on type 'PrismaService'."}, {"start": 8132, "length": 7, "code": 2339, "category": 1, "messageText": "Property 'session' does not exist on type 'PrismaService'."}, {"start": 8639, "length": 4, "code": 2339, "category": 1, "messageText": "Property 'tool' does not exist on type 'PrismaService'."}, {"start": 9031, "length": 4, "code": 2339, "category": 1, "messageText": "Property 'tool' does not exist on type 'PrismaService'."}, {"start": 9447, "length": 4, "code": 2339, "category": 1, "messageText": "Property 'tool' does not exist on type 'PrismaService'."}, {"start": 10572, "length": 4, "code": 2339, "category": 1, "messageText": "Property 'tool' does not exist on type 'PrismaService'."}, {"start": 10713, "length": 4, "code": 2339, "category": 1, "messageText": "Property 'tool' does not exist on type 'PrismaService'."}]], [93, [{"start": 35, "length": 16, "messageText": "Cannot find module '@nestjs/common' or its corresponding type declarations.", "category": 1, "code": 2307}]], [94, [{"start": 67, "length": 16, "messageText": "Cannot find module '@nestjs/common' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 400, "length": 16, "messageText": "Cannot find module '@nestjs/config' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 1375, "length": 4, "code": 2339, "category": 1, "messageText": "Property 'tool' does not exist on type 'PrismaService'."}, {"start": 8727, "length": 4, "code": 2353, "category": 1, "messageText": "Object literal may only specify known properties, and 'logs' does not exist in type 'ToolExecutionResult'."}, {"start": 12231, "length": 6, "messageText": "Cannot find name '<PERSON><PERSON>er'. Do you need to install type definitions for node? Try `npm i --save-dev @types/node`.", "category": 1, "code": 2580}, {"start": 12975, "length": 12, "code": 2339, "category": 1, "messageText": "Property 'billingUsage' does not exist on type 'PrismaService'."}, {"start": 13327, "length": 5, "code": 2339, "category": 1, "messageText": "Property 'quota' does not exist on type 'PrismaService'."}, {"start": 14064, "length": 7, "code": 2339, "category": 1, "messageText": "Property 'session' does not exist on type 'PrismaService'."}, {"start": 14651, "length": 7, "code": 2339, "category": 1, "messageText": "Property 'session' does not exist on type 'PrismaService'."}, {"start": 14868, "length": 12, "code": 2339, "category": 1, "messageText": "Property 'billingUsage' does not exist on type 'PrismaService'."}, {"start": 15155, "length": 12, "code": 2339, "category": 1, "messageText": "Property 'billingUsage' does not exist on type 'PrismaService'."}, {"start": 15438, "length": 7, "code": 2339, "category": 1, "messageText": "Property 'session' does not exist on type 'PrismaService'."}, {"start": 15740, "length": 12, "code": 2339, "category": 1, "messageText": "Property 'billingUsage' does not exist on type 'PrismaService'."}]], [95, [{"start": 138, "length": 16, "messageText": "Cannot find module '@nestjs/common' or its corresponding type declarations.", "category": 1, "code": 2307}]], [96, [{"start": 23, "length": 16, "messageText": "Cannot find module '@nestjs/common' or its corresponding type declarations.", "category": 1, "code": 2307}]], [97, [{"start": 99, "length": 17, "messageText": "Cannot find module 'class-validator' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 150, "length": 19, "messageText": "Cannot find module 'class-transformer' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 2356, "length": 17, "code": 2415, "category": 1, "messageText": {"messageText": "Class 'UpdateWorkflowDto' incorrectly extends base class 'CreateWorkflowDto'.", "category": 1, "code": 2415, "next": [{"messageText": "Property 'name' is optional in type 'UpdateWorkflowDto' but required in type 'CreateWorkflowDto'.", "category": 1, "code": 2327}]}}]], [98, [{"start": 67, "length": 16, "messageText": "Cannot find module '@nestjs/common' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 725, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'workflow' does not exist on type 'PrismaService'."}, {"start": 1586, "length": 23, "code": 2339, "category": 1, "messageText": "Property 'broadcastToOrganization' does not exist on type 'APIMXService'."}, {"start": 2564, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'workflow' does not exist on type 'PrismaService'."}, {"start": 3091, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'workflow' does not exist on type 'PrismaService'."}, {"start": 3549, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'workflow' does not exist on type 'PrismaService'."}, {"start": 4652, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'workflow' does not exist on type 'PrismaService'."}, {"start": 4967, "length": 23, "code": 2339, "category": 1, "messageText": "Property 'broadcastToOrganization' does not exist on type 'APIMXService'."}, {"start": 5424, "length": 17, "code": 2339, "category": 1, "messageText": "Property 'workflowExecution' does not exist on type 'PrismaService'."}, {"start": 5724, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'workflow' does not exist on type 'PrismaService'."}, {"start": 5826, "length": 23, "code": 2339, "category": 1, "messageText": "Property 'broadcastToOrganization' does not exist on type 'APIMXService'."}, {"start": 6297, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'workflow' does not exist on type 'PrismaService'."}, {"start": 6984, "length": 23, "code": 2339, "category": 1, "messageText": "Property 'broadcastToOrganization' does not exist on type 'APIMXService'."}, {"start": 7506, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'workflow' does not exist on type 'PrismaService'."}, {"start": 7571, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'workflow' does not exist on type 'PrismaService'."}, {"start": 7683, "length": 17, "code": 2339, "category": 1, "messageText": "Property 'workflowExecution' does not exist on type 'PrismaService'."}, {"start": 7786, "length": 17, "code": 2339, "category": 1, "messageText": "Property 'workflowExecution' does not exist on type 'PrismaService'."}, {"start": 7978, "length": 17, "code": 2339, "category": 1, "messageText": "Property 'workflowExecution' does not exist on type 'PrismaService'."}, {"start": 11648, "length": 12, "code": 2339, "category": 1, "messageText": "Property 'billingUsage' does not exist on type 'PrismaService'."}]], [99, [{"start": 67, "length": 16, "messageText": "Cannot find module '@nestjs/common' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 1794, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'workflow' does not exist on type 'PrismaService'."}, {"start": 2139, "length": 17, "code": 2339, "category": 1, "messageText": "Property 'workflowExecution' does not exist on type 'PrismaService'."}, {"start": 2915, "length": 23, "code": 2339, "category": 1, "messageText": "Property 'broadcastToOrganization' does not exist on type 'APIMXService'."}, {"start": 3598, "length": 17, "code": 2339, "category": 1, "messageText": "Property 'workflowExecution' does not exist on type 'PrismaService'."}, {"start": 3927, "length": 23, "code": 2339, "category": 1, "messageText": "Property 'broadcastToOrganization' does not exist on type 'APIMXService'."}, {"start": 4654, "length": 17, "code": 2339, "category": 1, "messageText": "Property 'workflowExecution' does not exist on type 'PrismaService'."}, {"start": 4953, "length": 23, "code": 2339, "category": 1, "messageText": "Property 'broadcastToOrganization' does not exist on type 'APIMXService'."}, {"start": 5860, "length": 17, "code": 2339, "category": 1, "messageText": "Property 'workflowExecution' does not exist on type 'PrismaService'."}, {"start": 6118, "length": 17, "code": 2339, "category": 1, "messageText": "Property 'workflowExecution' does not exist on type 'PrismaService'."}, {"start": 6433, "length": 17, "code": 2339, "category": 1, "messageText": "Property 'workflowExecution' does not exist on type 'PrismaService'."}, {"start": 7052, "length": 17, "code": 2339, "category": 1, "messageText": "Property 'workflowExecution' does not exist on type 'PrismaService'."}, {"start": 7323, "length": 23, "code": 2339, "category": 1, "messageText": "Property 'broadcastToOrganization' does not exist on type 'APIMXService'."}, {"start": 9314, "length": 23, "code": 2339, "category": 1, "messageText": "Property 'broadcastToOrganization' does not exist on type 'APIMXService'."}, {"start": 15600, "length": 11, "code": 2339, "category": 1, "messageText": "Property 'hITLRequest' does not exist on type 'PrismaService'."}, {"start": 16217, "length": 11, "code": 2339, "category": 1, "messageText": "Property 'hITLRequest' does not exist on type 'PrismaService'."}, {"start": 19579, "length": 12, "code": 2339, "category": 1, "messageText": "Property 'billingUsage' does not exist on type 'PrismaService'."}]], [100, [{"start": 140, "length": 16, "messageText": "Cannot find module '@nestjs/common' or its corresponding type declarations.", "category": 1, "code": 2307}]], [101, [{"start": 23, "length": 16, "messageText": "Cannot find module '@nestjs/common' or its corresponding type declarations.", "category": 1, "code": 2307}]], [102, [{"start": 23, "length": 16, "messageText": "Cannot find module '@nestjs/common' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 70, "length": 16, "messageText": "Cannot find module '@nestjs/config' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 120, "length": 19, "messageText": "Cannot find module '@nestjs/throttler' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 169, "length": 23, "messageText": "Cannot find module '@nestjs/cache-manager' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 221, "length": 27, "messageText": "Cannot find module 'cache-manager-redis-store' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 1194, "length": 7, "messageText": "Cannot find name 'process'. Do you need to install type definitions for node? Try `npm i --save-dev @types/node`.", "category": 1, "code": 2580}, {"start": 1256, "length": 7, "messageText": "Cannot find name 'process'. Do you need to install type definitions for node? Try `npm i --save-dev @types/node`.", "category": 1, "code": 2580}, {"start": 1309, "length": 7, "messageText": "Cannot find name 'process'. Do you need to install type definitions for node? Try `npm i --save-dev @types/node`.", "category": 1, "code": 2580}, {"start": 1358, "length": 7, "messageText": "Cannot find name 'process'. Do you need to install type definitions for node? Try `npm i --save-dev @types/node`.", "category": 1, "code": 2580}]], [103, [{"start": 30, "length": 16, "messageText": "Cannot find module '@nestjs/common' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 73, "length": 9, "messageText": "Cannot find module 'winston' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 246, "length": 7, "messageText": "Cannot find name 'process'. Do you need to install type definitions for node? Try `npm i --save-dev @types/node`.", "category": 1, "code": 2580}, {"start": 724, "length": 7, "messageText": "Cannot find name 'process'. Do you need to install type definitions for node? Try `npm i --save-dev @types/node`.", "category": 1, "code": 2580}]], [104, [{"start": 28, "length": 14, "messageText": "Cannot find module '@nestjs/core' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 75, "length": 16, "messageText": "Cannot find module '@nestjs/common' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 123, "length": 16, "messageText": "Cannot find module '@nestjs/config' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 160, "length": 8, "messageText": "Cannot find module 'helmet' or its corresponding type declarations.", "category": 1, "code": 2307}]], [105, [{"start": 28, "length": 16, "messageText": "Cannot find module '@nestjs/common' or its corresponding type declarations.", "category": 1, "code": 2307}]]], "affectedFilesPendingEmit": [52, 81, 82, 85, 75, 74, 63, 64, 62, 102, 57, 61, 56, 55, 67, 58, 59, 68, 105, 60, 103, 104, 72, 73, 71, 54, 53, 77, 76, 79, 83, 84, 80, 65, 69, 70, 66, 86, 88, 89, 87, 90, 93, 94, 95, 96, 91, 97, 99, 100, 101, 98], "version": "5.8.3"}