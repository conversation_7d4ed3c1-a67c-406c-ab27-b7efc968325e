"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.APIMXService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
const apix_gateway_1 = require("./apix.gateway");
let APIMXService = class APIMXService {
    constructor(prisma, gateway) {
        this.prisma = prisma;
        this.gateway = gateway;
    }
    async publishEvent(event) {
        const fullEvent = {
            ...event,
            id: this.generateEventId(),
            timestamp: new Date(),
        };
        await this.prisma.aPIMXEvent.create({
            data: {
                id: fullEvent.id,
                type: fullEvent.type,
                organizationId: fullEvent.organizationId,
                userId: fullEvent.userId,
                sessionId: fullEvent.sessionId,
                payload: fullEvent.payload,
                timestamp: fullEvent.timestamp,
            },
        });
        this.gateway.broadcastToOrganization(fullEvent.organizationId, fullEvent);
        if (fullEvent.userId) {
            this.gateway.broadcastToUser(fullEvent.userId, fullEvent);
        }
        if (fullEvent.sessionId) {
            this.gateway.broadcastToSession(fullEvent.sessionId, fullEvent);
        }
        return fullEvent;
    }
    async getEventHistory(organizationId, options) {
        const { type, userId, sessionId, limit = 50, offset = 0 } = options || {};
        return this.prisma.aPIMXEvent.findMany({
            where: {
                organizationId,
                ...(type && { type }),
                ...(userId && { userId }),
                ...(sessionId && { sessionId }),
            },
            orderBy: { timestamp: 'desc' },
            take: limit,
            skip: offset,
        });
    }
    async cleanupOldEvents(daysToKeep = 30) {
        const cutoffDate = new Date();
        cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);
        const deletedCount = await this.prisma.aPIMXEvent.deleteMany({
            where: {
                timestamp: {
                    lt: cutoffDate,
                },
            },
        });
        return deletedCount.count;
    }
    generateEventId() {
        return `apix_${Date.now()}_${Math.random().toString(36).substring(2)}`;
    }
    async publishAgentEvent(type, agentId, organizationId, data) {
        return this.publishEvent({
            type: `agent.${type}`,
            organizationId,
            payload: {
                agentId,
                ...data,
            },
        });
    }
    async publishToolEvent(type, toolId, organizationId, data) {
        return this.publishEvent({
            type: `tool.${type}`,
            organizationId,
            payload: {
                toolId,
                ...data,
            },
        });
    }
    async publishSessionEvent(type, sessionId, organizationId, userId, data) {
        return this.publishEvent({
            type: `session.${type}`,
            organizationId,
            userId,
            sessionId,
            payload: {
                sessionId,
                ...data,
            },
        });
    }
    async publishBillingEvent(type, organizationId, data) {
        return this.publishEvent({
            type: `billing.${type}`,
            organizationId,
            payload: data,
        });
    }
    async publishSystemEvent(type, organizationId, data) {
        return this.publishEvent({
            type: `system.${type}`,
            organizationId,
            payload: data,
        });
    }
};
exports.APIMXService = APIMXService;
exports.APIMXService = APIMXService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService,
        apix_gateway_1.APIMXGateway])
], APIMXService);
//# sourceMappingURL=apix.service.js.map