"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ToolsController = void 0;
const common_1 = require("@nestjs/common");
const jwt_auth_guard_1 = require("../auth/jwt-auth.guard");
const tools_service_1 = require("./tools.service");
const tool_executor_service_1 = require("./tool-executor.service");
const tool_dto_1 = require("./dto/tool.dto");
let ToolsController = class ToolsController {
    constructor(toolsService, toolExecutorService) {
        this.toolsService = toolsService;
        this.toolExecutorService = toolExecutorService;
    }
    async create(req, createToolDto) {
        return this.toolsService.create(req.user.sub, req.user.organizationId, createToolDto);
    }
    async findAll(req, query) {
        return this.toolsService.findAll(req.user.organizationId, query);
    }
    async getStats(req) {
        return this.toolsService.getToolStats(req.user.organizationId);
    }
    async getPopular(req, limit) {
        return this.toolsService.getPopularTools(req.user.organizationId, limit);
    }
    async searchByTags(req, tags) {
        const tagArray = Array.isArray(tags) ? tags : [tags];
        return this.toolsService.searchToolsByTags(req.user.organizationId, tagArray);
    }
    async getByCategory(req, category) {
        return this.toolsService.getToolsByCategory(req.user.organizationId, category);
    }
    async findOne(req, id) {
        return this.toolsService.findOne(id, req.user.organizationId);
    }
    async update(req, id, updateToolDto) {
        return this.toolsService.update(id, req.user.organizationId, updateToolDto);
    }
    async remove(req, id) {
        return this.toolsService.remove(id, req.user.organizationId);
    }
    async execute(req, id, executeDto) {
        return this.toolExecutorService.executeTool(id, req.user.organizationId, req.user.sub, executeDto);
    }
    async test(req, id, testDto) {
        return this.toolExecutorService.testTool(id, req.user.organizationId, testDto);
    }
    async createVersion(req, id, versionDto) {
        return this.toolsService.createVersion(id, req.user.organizationId, versionDto);
    }
    async rollbackToVersion(req, id, version) {
        return this.toolsService.rollbackToVersion(id, req.user.organizationId, version);
    }
    async clone(req, id, cloneData) {
        return this.toolsService.clone(id, req.user.organizationId, cloneData);
    }
    async getExecutionHistory(req, id, limit) {
        return this.toolExecutorService.getExecutionHistory(id, req.user.organizationId, limit);
    }
    async getExecutionMetrics(req, id) {
        return this.toolExecutorService.getExecutionMetrics(id, req.user.organizationId);
    }
};
exports.ToolsController = ToolsController;
__decorate([
    (0, common_1.Post)(),
    (0, common_1.HttpCode)(common_1.HttpStatus.CREATED),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, tool_dto_1.CreateToolDto]),
    __metadata("design:returntype", Promise)
], ToolsController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, tool_dto_1.ToolQueryDto]),
    __metadata("design:returntype", Promise)
], ToolsController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('stats'),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], ToolsController.prototype, "getStats", null);
__decorate([
    (0, common_1.Get)('popular'),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Query)('limit')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Number]),
    __metadata("design:returntype", Promise)
], ToolsController.prototype, "getPopular", null);
__decorate([
    (0, common_1.Get)('search/tags'),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Query)('tags')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Array]),
    __metadata("design:returntype", Promise)
], ToolsController.prototype, "searchByTags", null);
__decorate([
    (0, common_1.Get)('category/:category'),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Param)('category')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], ToolsController.prototype, "getByCategory", null);
__decorate([
    (0, common_1.Get)(':id'),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], ToolsController.prototype, "findOne", null);
__decorate([
    (0, common_1.Put)(':id'),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Param)('id')),
    __param(2, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, tool_dto_1.UpdateToolDto]),
    __metadata("design:returntype", Promise)
], ToolsController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, common_1.HttpCode)(common_1.HttpStatus.NO_CONTENT),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], ToolsController.prototype, "remove", null);
__decorate([
    (0, common_1.Post)(':id/execute'),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Param)('id')),
    __param(2, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, tool_dto_1.ExecuteToolDto]),
    __metadata("design:returntype", Promise)
], ToolsController.prototype, "execute", null);
__decorate([
    (0, common_1.Post)(':id/test'),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Param)('id')),
    __param(2, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, tool_dto_1.TestToolDto]),
    __metadata("design:returntype", Promise)
], ToolsController.prototype, "test", null);
__decorate([
    (0, common_1.Post)(':id/version'),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Param)('id')),
    __param(2, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, tool_dto_1.ToolVersionDto]),
    __metadata("design:returntype", Promise)
], ToolsController.prototype, "createVersion", null);
__decorate([
    (0, common_1.Put)(':id/version/:version/rollback'),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Param)('id')),
    __param(2, (0, common_1.Param)('version')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, String]),
    __metadata("design:returntype", Promise)
], ToolsController.prototype, "rollbackToVersion", null);
__decorate([
    (0, common_1.Post)(':id/clone'),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Param)('id')),
    __param(2, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, Object]),
    __metadata("design:returntype", Promise)
], ToolsController.prototype, "clone", null);
__decorate([
    (0, common_1.Get)(':id/execution/history'),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Param)('id')),
    __param(2, (0, common_1.Query)('limit')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, Number]),
    __metadata("design:returntype", Promise)
], ToolsController.prototype, "getExecutionHistory", null);
__decorate([
    (0, common_1.Get)(':id/execution/metrics'),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], ToolsController.prototype, "getExecutionMetrics", null);
exports.ToolsController = ToolsController = __decorate([
    (0, common_1.Controller)('tools'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __metadata("design:paramtypes", [tools_service_1.ToolsService,
        tool_executor_service_1.ToolExecutorService])
], ToolsController);
//# sourceMappingURL=tools.controller.js.map