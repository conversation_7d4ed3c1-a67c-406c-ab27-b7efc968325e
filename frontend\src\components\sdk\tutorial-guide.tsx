'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  Play, 
  CheckCircle, 
  ArrowRight, 
  User, 
  Building2, 
  Rocket,
  MessageSquare,
  Cog,
  Zap,
  Book,
  Clock,
  Star
} from 'lucide-react';

interface TutorialGuideProps {
  onStepComplete: (stepId: string) => void;
  completedSteps: string[];
}

export function SDKTutorialGuide({ onStepComplete, completedSteps }: TutorialGuideProps) {
  const [selectedPath, setSelectedPath] = useState<string | null>(null);
  const [activeLesson, setActiveLesson] = useState<string | null>(null);

  const learningPaths = [
    {
      id: 'business-user',
      title: 'Business User',
      description: 'Perfect for managers, marketers, and business professionals',
      icon: <User className="w-6 h-6" />,
      color: 'bg-blue-500',
      duration: '30 minutes',
      difficulty: 'Beginner',
      lessons: [
        {
          id: 'intro-to-ai',
          title: 'What is AI Automation?',
          description: 'Understanding AI and how it can help your business',
          duration: '5 min',
          type: 'Video + Interactive'
        },
        {
          id: 'create-first-agent',
          title: 'Create Your First AI Agent',
          description: 'Build a customer service chatbot in 3 clicks',
          duration: '10 min',
          type: 'Hands-on Tutorial'
        },
        {
          id: 'connect-tools',
          title: 'Connect to Your Tools',
          description: 'Link your CRM, email, and other business tools',
          duration: '8 min',
          type: 'Step-by-step Guide'
        },
        {
          id: 'monitor-results',
          title: 'Monitor and Improve',
          description: 'Track performance and optimize your AI workflows',
          duration: '7 min',
          type: 'Dashboard Tour'
        }
      ]
    },
    {
      id: 'developer',
      title: 'Developer',
      description: 'For developers wanting to integrate AI into applications',
      icon: <Building2 className="w-6 h-6" />,
      color: 'bg-green-500',
      duration: '45 minutes',
      difficulty: 'Intermediate',
      lessons: [
        {
          id: 'sdk-setup',
          title: 'SDK Installation & Setup',
          description: 'Get the SDK running in your development environment',
          duration: '10 min',
          type: 'Code Tutorial'
        },
        {
          id: 'first-api-call',
          title: 'Your First API Call',
          description: 'Make your first request to the SynapseAI API',
          duration: '8 min',
          type: 'Interactive Code'
        },
        {
          id: 'build-workflow',
          title: 'Build Complex Workflows',
          description: 'Create multi-step AI automation workflows',
          duration: '15 min',
          type: 'Project-based'
        },
        {
          id: 'production-deployment',
          title: 'Production Best Practices',
          description: 'Error handling, monitoring, and scaling',
          duration: '12 min',
          type: 'Best Practices'
        }
      ]
    },
    {
      id: 'startup',
      title: 'Startup Founder',
      description: 'Build and scale AI-powered products quickly',
      icon: <Rocket className="w-6 h-6" />,
      color: 'bg-purple-500',
      duration: '25 minutes',
      difficulty: 'Beginner',
      lessons: [
        {
          id: 'mvp-ai-features',
          title: 'Add AI to Your MVP',
          description: 'Quick wins to make your product more intelligent',
          duration: '8 min',
          type: 'Strategy Guide'
        },
        {
          id: 'customer-automation',
          title: 'Automate Customer Interactions',
          description: 'Scale support and sales with AI agents',
          duration: '10 min',
          type: 'Use Case Demo'
        },
        {
          id: 'analytics-insights',
          title: 'AI-Powered Analytics',
          description: 'Get insights from your data automatically',
          duration: '7 min',
          type: 'Feature Overview'
        }
      ]
    }
  ];

  const interactiveLessons = {
    'create-first-agent': {
      title: 'Create Your First AI Agent',
      steps: [
        {
          id: 'step-1',
          title: 'Choose Agent Type',
          description: 'Select what kind of AI agent you want to create',
          instruction: 'Click on "Customer Service Agent" below',
          component: (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Card className="cursor-pointer hover:shadow-lg transition-all border-2 border-blue-500 bg-blue-50">
                <CardContent className="p-6 text-center">
                  <MessageSquare className="w-8 h-8 text-blue-600 mx-auto mb-3" />
                  <h3 className="font-semibold mb-2">Customer Service Agent</h3>
                  <p className="text-sm text-gray-600">Handle customer inquiries automatically</p>
                  <Badge className="mt-2 bg-blue-100 text-blue-700">Recommended</Badge>
                </CardContent>
              </Card>
              <Card className="cursor-pointer hover:shadow-lg transition-all">
                <CardContent className="p-6 text-center">
                  <Cog className="w-8 h-8 text-gray-600 mx-auto mb-3" />
                  <h3 className="font-semibold mb-2">Data Processing Agent</h3>
                  <p className="text-sm text-gray-600">Analyze and process data automatically</p>
                </CardContent>
              </Card>
              <Card className="cursor-pointer hover:shadow-lg transition-all">
                <CardContent className="p-6 text-center">
                  <Zap className="w-8 h-8 text-gray-600 mx-auto mb-3" />
                  <h3 className="font-semibold mb-2">Sales Assistant</h3>
                  <p className="text-sm text-gray-600">Help with lead qualification and follow-up</p>
                </CardContent>
              </Card>
            </div>
          )
        },
        {
          id: 'step-2',
          title: 'Configure Agent Behavior',
          description: 'Tell your agent how to respond to customers',
          instruction: 'Fill in the agent description and click "Generate Response Style"',
          component: (
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-2">Agent Description</label>
                <textarea 
                  className="w-full p-3 border rounded-lg resize-none"
                  rows={3}
                  placeholder="Describe what your agent should do... (e.g., 'Help customers with product questions, check order status, and escalate complex issues to human support')"
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-2">Response Style</label>
                <div className="grid grid-cols-3 gap-2">
                  <Button variant="outline" size="sm">Friendly</Button>
                  <Button variant="outline" size="sm">Professional</Button>
                  <Button variant="outline" size="sm">Casual</Button>
                </div>
              </div>
              <Button className="w-full bg-blue-600 hover:bg-blue-700">
                <Zap className="w-4 h-4 mr-2" />
                Generate Response Style
              </Button>
            </div>
          )
        },
        {
          id: 'step-3',
          title: 'Test Your Agent',
          description: 'Try out your agent with a sample conversation',
          instruction: 'Type a customer question and see how your agent responds',
          component: (
            <div className="space-y-4">
              <div className="bg-gray-50 rounded-lg p-4 h-48 overflow-y-auto">
                <div className="space-y-3">
                  <div className="flex justify-end">
                    <div className="bg-blue-600 text-white rounded-lg px-4 py-2 max-w-xs">
                      Hi, I need help with my order
                    </div>
                  </div>
                  <div className="flex justify-start">
                    <div className="bg-white border rounded-lg px-4 py-2 max-w-xs">
                      Hello! I'd be happy to help you with your order. Could you please provide your order number so I can look it up for you?
                    </div>
                  </div>
                </div>
              </div>
              <div className="flex gap-2">
                <input 
                  type="text" 
                  className="flex-1 p-3 border rounded-lg"
                  placeholder="Type your message..."
                />
                <Button>Send</Button>
              </div>
              <div className="flex items-center justify-between">
                <div className="text-sm text-gray-600">Response generated in 0.8s</div>
                <Button variant="outline" size="sm">
                  <Star className="w-4 h-4 mr-1" />
                  Good Response
                </Button>
              </div>
            </div>
          )
        }
      ]
    }
  };

  const getProgress = (pathId: string) => {
    const path = learningPaths.find(p => p.id === pathId);
    if (!path) return 0;
    
    const completedLessons = path.lessons.filter(lesson => 
      completedSteps.includes(lesson.id)
    ).length;
    
    return (completedLessons / path.lessons.length) * 100;
  };

  if (activeLesson && interactiveLessons[activeLesson as keyof typeof interactiveLessons]) {
    const lesson = interactiveLessons[activeLesson as keyof typeof interactiveLessons];
    const [currentStep, setCurrentStep] = useState(0);
    
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <Button 
            variant="outline" 
            onClick={() => setActiveLesson(null)}
          >
            ← Back to Path
          </Button>
          <Badge variant="secondary">
            Step {currentStep + 1} of {lesson.steps.length}
          </Badge>
        </div>

        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="flex items-center gap-2">
                  <Play className="w-5 h-5 text-blue-600" />
                  {lesson.title}
                </CardTitle>
                <CardDescription>
                  {lesson.steps[currentStep].description}
                </CardDescription>
              </div>
              <Progress value={(currentStep / lesson.steps.length) * 100} className="w-32" />
            </div>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="bg-blue-50 border-l-4 border-blue-500 p-4">
              <h4 className="font-medium text-blue-900 mb-1">
                {lesson.steps[currentStep].title}
              </h4>
              <p className="text-blue-700 text-sm">
                {lesson.steps[currentStep].instruction}
              </p>
            </div>

            {lesson.steps[currentStep].component}

            <div className="flex justify-between">
              <Button 
                variant="outline" 
                disabled={currentStep === 0}
                onClick={() => setCurrentStep(prev => prev - 1)}
              >
                Previous
              </Button>
              <Button 
                onClick={() => {
                  if (currentStep < lesson.steps.length - 1) {
                    setCurrentStep(prev => prev + 1);
                  } else {
                    onStepComplete(activeLesson);
                    setActiveLesson(null);
                  }
                }}
              >
                {currentStep === lesson.steps.length - 1 ? 'Complete Lesson' : 'Next Step'}
                <ArrowRight className="w-4 h-4 ml-2" />
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (selectedPath) {
    const path = learningPaths.find(p => p.id === selectedPath);
    if (!path) return null;

    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <Button 
            variant="outline" 
            onClick={() => setSelectedPath(null)}
          >
            ← Choose Different Path
          </Button>
          <div className="flex items-center gap-4">
            <Progress value={getProgress(selectedPath)} className="w-32" />
            <span className="text-sm text-gray-600">
              {Math.round(getProgress(selectedPath))}% Complete
            </span>
          </div>
        </div>

        <Card className="border-2 border-blue-200 bg-gradient-to-r from-blue-50 to-purple-50">
          <CardHeader>
            <CardTitle className="flex items-center gap-3">
              <div className={`${path.color} text-white rounded-lg p-2`}>
                {path.icon}
              </div>
              {path.title} Learning Path
            </CardTitle>
            <CardDescription>{path.description}</CardDescription>
            <div className="flex gap-4 text-sm">
              <div className="flex items-center gap-1">
                <Clock className="w-4 h-4" />
                {path.duration}
              </div>
              <Badge variant="secondary">{path.difficulty}</Badge>
            </div>
          </CardHeader>
        </Card>

        <div className="grid gap-4">
          {path.lessons.map((lesson, index) => (
            <Card 
              key={lesson.id} 
              className={`cursor-pointer transition-all hover:shadow-lg ${
                completedSteps.includes(lesson.id) 
                  ? 'border-green-500 bg-green-50' 
                  : 'hover:border-blue-300'
              }`}
              onClick={() => {
                if (lesson.id === 'create-first-agent') {
                  setActiveLesson(lesson.id);
                } else {
                  onStepComplete(lesson.id);
                }
              }}
            >
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-4">
                    <div className={`rounded-full p-2 ${
                      completedSteps.includes(lesson.id) 
                        ? 'bg-green-500 text-white' 
                        : 'bg-gray-100 text-gray-600'
                    }`}>
                      {completedSteps.includes(lesson.id) ? (
                        <CheckCircle className="w-5 h-5" />
                      ) : (
                        <span className="font-semibold">{index + 1}</span>
                      )}
                    </div>
                    <div>
                      <h3 className="font-semibold text-lg">{lesson.title}</h3>
                      <p className="text-gray-600 text-sm mb-2">{lesson.description}</p>
                      <div className="flex gap-2">
                        <Badge variant="outline" className="text-xs">
                          <Clock className="w-3 h-3 mr-1" />
                          {lesson.duration}
                        </Badge>
                        <Badge variant="outline" className="text-xs">
                          {lesson.type}
                        </Badge>
                      </div>
                    </div>
                  </div>
                  <ArrowRight className="w-5 h-5 text-gray-400" />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <Card className="bg-gradient-to-r from-blue-600 to-purple-600 text-white border-0">
        <CardContent className="p-8 text-center">
          <Book className="w-12 h-12 mx-auto mb-4 opacity-90" />
          <h2 className="text-2xl font-bold mb-2">Choose Your Learning Path</h2>
          <p className="text-blue-100 max-w-2xl mx-auto">
            Select the path that best matches your role and goals. Each path is designed 
            with interactive tutorials and real examples.
          </p>
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {learningPaths.map((path) => (
          <Card 
            key={path.id} 
            className="cursor-pointer transition-all hover:shadow-xl hover:-translate-y-1 group"
            onClick={() => setSelectedPath(path.id)}
          >
            <CardHeader>
              <div className="flex items-center justify-between">
                <div className={`${path.color} text-white rounded-lg p-3 group-hover:scale-110 transition-transform`}>
                  {path.icon}
                </div>
                <Badge variant="secondary">{path.difficulty}</Badge>
              </div>
              <CardTitle className="text-xl">{path.title}</CardTitle>
              <CardDescription className="text-sm">{path.description}</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between text-sm">
                  <div className="flex items-center gap-1 text-gray-600">
                    <Clock className="w-4 h-4" />
                    {path.duration}
                  </div>
                  <div className="flex items-center gap-1 text-gray-600">
                    <Book className="w-4 h-4" />
                    {path.lessons.length} lessons
                  </div>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center justify-between text-sm">
                    <span>Progress</span>
                    <span>{Math.round(getProgress(path.id))}%</span>
                  </div>
                  <Progress value={getProgress(path.id)} />
                </div>

                <Button className="w-full group-hover:bg-blue-700">
                  Start Learning
                  <ArrowRight className="w-4 h-4 ml-2" />
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}