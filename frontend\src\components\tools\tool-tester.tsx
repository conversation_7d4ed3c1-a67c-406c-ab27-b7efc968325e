'use client';

import React, { useState, useCallback } from 'react';
import { Play, Square, RotateCcw, Copy, Download, AlertCircle, CheckCircle, Clock } from 'lucide-react';
import { toolsApi } from '@/lib/api/tools';
import { ToolSchema } from './schema-builder';

interface ToolTesterProps {
  toolId?: string;
  toolConfig: any;
  schema: ToolSchema;
  className?: string;
}

interface TestResult {
  success: boolean;
  output: any;
  executionTime: number;
  error?: string;
  logs?: string[];
  timestamp: Date;
}

const InputRenderer: React.FC<{
  name: string;
  property: any;
  value: any;
  onChange: (value: any) => void;
}> = ({ name, property, value, onChange }) => {
  const handleChange = (newValue: any) => {
    onChange(newValue);
  };

  switch (property.type) {
    case 'string':
      if (property.enum) {
        return (
          <select
            value={value || ''}
            onChange={(e) => handleChange(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
          >
            <option value="">Select value...</option>
            {property.enum.map((option: string) => (
              <option key={option} value={option}>
                {option}
              </option>
            ))}
          </select>
        );
      }
      return (
        <input
          type="text"
          value={value || ''}
          onChange={(e) => handleChange(e.target.value)}
          placeholder={property.description}
          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
        />
      );

    case 'number':
      return (
        <input
          type="number"
          value={value || ''}
          onChange={(e) => handleChange(parseFloat(e.target.value) || 0)}
          placeholder={property.description}
          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
        />
      );

    case 'boolean':
      return (
        <label className="flex items-center gap-2">
          <input
            type="checkbox"
            checked={value || false}
            onChange={(e) => handleChange(e.target.checked)}
            className="rounded"
          />
          <span className="text-sm text-gray-700 dark:text-gray-300">
            {property.description || name}
          </span>
        </label>
      );

    case 'array':
      const arrayValue = Array.isArray(value) ? value : [];
      return (
        <div className="space-y-2">
          {arrayValue.map((item: any, index: number) => (
            <div key={index} className="flex gap-2">
              <input
                type="text"
                value={item || ''}
                onChange={(e) => {
                  const newArray = [...arrayValue];
                  newArray[index] = e.target.value;
                  handleChange(newArray);
                }}
                className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
                placeholder={`Item ${index + 1}`}
              />
              <button
                onClick={() => {
                  const newArray = arrayValue.filter((_, i) => i !== index);
                  handleChange(newArray);
                }}
                className="px-2 py-2 text-red-600 hover:bg-red-100 dark:hover:bg-red-900/20 rounded"
              >
                Remove
              </button>
            </div>
          ))}
          <button
            onClick={() => handleChange([...arrayValue, ''])}
            className="px-3 py-2 text-primary-600 hover:bg-primary-100 dark:hover:bg-primary-900/20 rounded text-sm"
          >
            Add Item
          </button>
        </div>
      );

    case 'object':
      return (
        <textarea
          value={typeof value === 'object' ? JSON.stringify(value, null, 2) : value || '{}'}
          onChange={(e) => {
            try {
              const parsed = JSON.parse(e.target.value);
              handleChange(parsed);
            } catch {
              // Invalid JSON, keep as string for now
              handleChange(e.target.value);
            }
          }}
          rows={4}
          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white font-mono text-sm"
          placeholder="JSON object"
        />
      );

    default:
      return (
        <input
          type="text"
          value={value || ''}
          onChange={(e) => handleChange(e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
        />
      );
  }
};

export const ToolTester: React.FC<ToolTesterProps> = ({
  toolId,
  toolConfig,
  schema,
  className = '',
}) => {
  const [testInput, setTestInput] = useState<Record<string, any>>({});
  const [isRunning, setIsRunning] = useState(false);
  const [results, setResults] = useState<TestResult[]>([]);
  const [selectedResult, setSelectedResult] = useState<TestResult | null>(null);

  const initializeInput = useCallback(() => {
    const initialInput: Record<string, any> = {};
    Object.entries(schema.properties || {}).forEach(([name, property]) => {
      if (property.default !== undefined) {
        initialInput[name] = property.default;
      } else {
        switch (property.type) {
          case 'string':
            initialInput[name] = '';
            break;
          case 'number':
            initialInput[name] = 0;
            break;
          case 'boolean':
            initialInput[name] = false;
            break;
          case 'array':
            initialInput[name] = [];
            break;
          case 'object':
            initialInput[name] = {};
            break;
        }
      }
    });
    setTestInput(initialInput);
  }, [schema]);

  React.useEffect(() => {
    initializeInput();
  }, [initializeInput]);

  const runTest = async () => {
    if (!toolId && !toolConfig) return;

    setIsRunning(true);
    try {
      let result;
      if (toolId) {
        result = await toolsApi.test(toolId, { input: testInput });
      } else {
        // For testing tool configurations before saving
        result = await toolsApi.testConfig({
          config: toolConfig,
          input: testInput,
        });
      }

      const testResult: TestResult = {
        ...result,
        timestamp: new Date(),
      };

      setResults(prev => [testResult, ...prev]);
      setSelectedResult(testResult);
    } catch (error: any) {
      const testResult: TestResult = {
        success: false,
        output: null,
        executionTime: 0,
        error: error.message || 'Test failed',
        timestamp: new Date(),
      };
      
      setResults(prev => [testResult, ...prev]);
      setSelectedResult(testResult);
    } finally {
      setIsRunning(false);
    }
  };

  const clearResults = () => {
    setResults([]);
    setSelectedResult(null);
  };

  const copyResult = (result: TestResult) => {
    navigator.clipboard.writeText(JSON.stringify(result, null, 2));
  };

  const downloadResults = () => {
    const data = JSON.stringify(results, null, 2);
    const blob = new Blob([data], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `tool-test-results-${Date.now()}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Test Configuration */}
      <div className="card p-6">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">
            Test Configuration
          </h3>
          <div className="flex gap-2">
            <button
              onClick={initializeInput}
              className="flex items-center gap-1 px-3 py-2 text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-md text-sm"
            >
              <RotateCcw className="h-4 w-4" />
              Reset
            </button>
            <button
              onClick={runTest}
              disabled={isRunning}
              className="flex items-center gap-2 px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isRunning ? (
                <>
                  <Square className="h-4 w-4" />
                  Running...
                </>
              ) : (
                <>
                  <Play className="h-4 w-4" />
                  Run Test
                </>
              )}
            </button>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {Object.entries(schema.properties || {}).map(([name, property]) => (
            <div key={name}>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                {property.name || name}
                {schema.required?.includes(name) && (
                  <span className="text-red-500 ml-1">*</span>
                )}
              </label>
              {property.description && (
                <p className="text-xs text-gray-500 dark:text-gray-400 mb-2">
                  {property.description}
                </p>
              )}
              <InputRenderer
                name={name}
                property={property}
                value={testInput[name]}
                onChange={(value) => setTestInput(prev => ({ ...prev, [name]: value }))}
              />
            </div>
          ))}
        </div>
      </div>

      {/* Results */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Results List */}
        <div className="card p-6">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">
              Test Results ({results.length})
            </h3>
            <div className="flex gap-2">
              {results.length > 0 && (
                <>
                  <button
                    onClick={downloadResults}
                    className="flex items-center gap-1 px-2 py-1 text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-800 rounded text-sm"
                  >
                    <Download className="h-4 w-4" />
                  </button>
                  <button
                    onClick={clearResults}
                    className="flex items-center gap-1 px-2 py-1 text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-800 rounded text-sm"
                  >
                    Clear
                  </button>
                </>
              )}
            </div>
          </div>

          {results.length === 0 ? (
            <div className="text-center py-8 text-gray-500 dark:text-gray-400">
              <Clock className="h-12 w-12 mx-auto mb-3 opacity-50" />
              <p>No test results yet. Run a test to see results here.</p>
            </div>
          ) : (
            <div className="space-y-2 max-h-96 overflow-y-auto">
              {results.map((result, index) => (
                <div
                  key={index}
                  onClick={() => setSelectedResult(result)}
                  className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                    selectedResult === result
                      ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20'
                      : 'border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-800'
                  }`}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      {result.success ? (
                        <CheckCircle className="h-4 w-4 text-green-600" />
                      ) : (
                        <AlertCircle className="h-4 w-4 text-red-600" />
                      )}
                      <span className="text-sm font-medium">
                        {result.success ? 'Success' : 'Failed'}
                      </span>
                    </div>
                    <span className="text-xs text-gray-500">
                      {result.executionTime}ms
                    </span>
                  </div>
                  <div className="text-xs text-gray-500 mt-1">
                    {result.timestamp.toLocaleTimeString()}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Result Details */}
        <div className="card p-6">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">
              Result Details
            </h3>
            {selectedResult && (
              <button
                onClick={() => copyResult(selectedResult)}
                className="flex items-center gap-1 px-2 py-1 text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-800 rounded text-sm"
              >
                <Copy className="h-4 w-4" />
                Copy
              </button>
            )}
          </div>

          {!selectedResult ? (
            <div className="text-center py-8 text-gray-500 dark:text-gray-400">
              Select a test result to view details
            </div>
          ) : (
            <div className="space-y-4">
              {/* Status */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Status
                </label>
                <div className={`inline-flex items-center gap-1 px-2 py-1 rounded text-sm ${
                  selectedResult.success
                    ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-200'
                    : 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-200'
                }`}>
                  {selectedResult.success ? (
                    <CheckCircle className="h-4 w-4" />
                  ) : (
                    <AlertCircle className="h-4 w-4" />
                  )}
                  {selectedResult.success ? 'Success' : 'Failed'}
                </div>
              </div>

              {/* Execution Time */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Execution Time
                </label>
                <span className="text-sm text-gray-900 dark:text-white">
                  {selectedResult.executionTime}ms
                </span>
              </div>

              {/* Error */}
              {selectedResult.error && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Error
                  </label>
                  <div className="p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded text-sm text-red-800 dark:text-red-200">
                    {selectedResult.error}
                  </div>
                </div>
              )}

              {/* Logs */}
              {selectedResult.logs && selectedResult.logs.length > 0 && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Logs
                  </label>
                  <div className="p-3 bg-gray-50 dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded">
                    {selectedResult.logs.map((log, index) => (
                      <div key={index} className="text-sm text-gray-700 dark:text-gray-300 font-mono">
                        {log}
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Output */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Output
                </label>
                <pre className="p-3 bg-gray-50 dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded text-sm overflow-auto max-h-64 text-gray-800 dark:text-gray-200">
                  {JSON.stringify(selectedResult.output, null, 2)}
                </pre>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ToolTester;