'use client';

import { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Switch } from '@/components/ui/switch';
import { Trash, Plus, Play, TestTube } from 'lucide-react';
import { Tool, CreateToolRequest, UpdateToolRequest, ToolType, ToolStatus, HttpMethod, AuthType } from '@/types/tool';
import { toolsApi } from '@/lib/api/tools';
import { SchemaBuilder } from './schema-builder';
import { ToolTester } from './tool-tester';

interface ToolBuilderProps {
  tool?: Tool | null;
  onSave: () => void;
  onCancel: () => void;
}

const defaultTool = {
  name: '',
  description: '',
  type: ToolType.API,
  config: {
    endpoint: '',
    method: HttpMethod.GET,
    headers: {},
    authentication: {
      type: AuthType.NONE,
      credentials: {},
    },
    timeout: 30000,
    retries: 3,
  },
  schema: {
    input: {},
    output: {},
  },
  status: ToolStatus.DRAFT,
  tags: [],
  metadata: {
    version: '1.0.0',
    category: '',
    documentation: '',
  },
};

export function ToolBuilder({ tool, onSave, onCancel }: ToolBuilderProps) {
  const [formData, setFormData] = useState<any>(tool || defaultTool);
  const [saving, setSaving] = useState(false);
  const [testing, setTesting] = useState(false);
  const [testResult, setTestResult] = useState<any>(null);
  const [activeTab, setActiveTab] = useState('basic');

  useEffect(() => {
    if (tool) {
      setFormData(tool);
    }
  }, [tool]);

  const handleInputChange = (field: string, value: any) => {
    setFormData((prev: any) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleConfigChange = (field: string, value: any) => {
    setFormData((prev: any) => ({
      ...prev,
      config: {
        ...prev.config,
        [field]: value,
      },
    }));
  };

  const handleAuthChange = (field: string, value: any) => {
    setFormData((prev: any) => ({
      ...prev,
      config: {
        ...prev.config,
        authentication: {
          ...prev.config.authentication,
          [field]: value,
        },
      },
    }));
  };

  const handleSchemaChange = (schema: any) => {
    setFormData((prev: any) => ({
      ...prev,
      schema,
    }));
  };

  const handleMetadataChange = (field: string, value: any) => {
    setFormData((prev: any) => ({
      ...prev,
      metadata: {
        ...prev.metadata,
        [field]: value,
      },
    }));
  };

  const handleAddHeader = () => {
    const key = prompt('Header name:');
    const value = prompt('Header value:');
    if (key && value) {
      handleConfigChange('headers', {
        ...formData.config.headers,
        [key]: value,
      });
    }
  };

  const handleRemoveHeader = (key: string) => {
    const headers = { ...formData.config.headers };
    delete headers[key];
    handleConfigChange('headers', headers);
  };

  const handleTest = async () => {
    try {
      setTesting(true);
      setTestResult(null);

      if (!formData.schema.input || Object.keys(formData.schema.input).length === 0) {
        setTestResult({
          success: false,
          error: 'Please define input schema first',
        });
        return;
      }

      // Create sample input based on schema
      const sampleInput: any = {};
      Object.entries(formData.schema.input).forEach(([key, field]: [string, any]) => {
        switch (field.type) {
          case 'string':
            sampleInput[key] = field.example || 'test';
            break;
          case 'number':
            sampleInput[key] = field.example || 123;
            break;
          case 'boolean':
            sampleInput[key] = field.example || true;
            break;
          default:
            sampleInput[key] = field.example || 'test';
        }
      });

      // Test the tool configuration
      if (tool?.id) {
        const result = await toolsApi.test(tool.id, {
          input: sampleInput,
          dryRun: true,
        });
        setTestResult(result);
      } else {
        setTestResult({
          success: true,
          output: { message: 'Configuration looks valid', input: sampleInput },
          executionTime: 0,
        });
      }
    } catch (error: any) {
      setTestResult({
        success: false,
        error: error.message,
      });
    } finally {
      setTesting(false);
    }
  };

  const handleSave = async () => {
    try {
      setSaving(true);

      const payload = {
        name: formData.name,
        description: formData.description,
        type: formData.type,
        config: formData.config,
        schema: formData.schema,
        status: formData.status,
        tags: formData.tags,
        metadata: formData.metadata,
      };

      if (tool?.id) {
        await toolsApi.update(tool.id, payload as UpdateToolRequest);
      } else {
        await toolsApi.create(payload as CreateToolRequest);
      }

      onSave();
    } catch (error) {
      console.error('Failed to save tool:', error);
    } finally {
      setSaving(false);
    }
  };

  return (
    <div className="space-y-6">
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="basic">Basic</TabsTrigger>
          <TabsTrigger value="config">Configuration</TabsTrigger>
          <TabsTrigger value="schema">Schema</TabsTrigger>
          <TabsTrigger value="test">Test</TabsTrigger>
          <TabsTrigger value="metadata">Metadata</TabsTrigger>
        </TabsList>

        <TabsContent value="basic" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Basic Information</CardTitle>
              <CardDescription>
                Define the basic properties of your tool
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Name</Label>
                  <Input
                    id="name"
                    value={formData.name}
                    onChange={(e) => handleInputChange('name', e.target.value)}
                    placeholder="My API Tool"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="type">Type</Label>
                  <Select
                    value={formData.type}
                    onValueChange={(value) => handleInputChange('type', value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="api">API Tool</SelectItem>
                      <SelectItem value="function">Function</SelectItem>
                      <SelectItem value="workflow">Workflow</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => handleInputChange('description', e.target.value)}
                  placeholder="Describe what this tool does..."
                  rows={3}
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="status">Status</Label>
                  <Select
                    value={formData.status}
                    onValueChange={(value) => handleInputChange('status', value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="draft">Draft</SelectItem>
                      <SelectItem value="active">Active</SelectItem>
                      <SelectItem value="archived">Archived</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="tags">Tags (comma-separated)</Label>
                  <Input
                    id="tags"
                    value={formData.tags?.join(', ') || ''}
                    onChange={(e) => handleInputChange('tags', e.target.value.split(',').map((t: string) => t.trim()).filter(Boolean))}
                    placeholder="api, external, integration"
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="config" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Configuration</CardTitle>
              <CardDescription>
                Configure how your tool works
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {formData.type === 'api' && (
                <>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="endpoint">Endpoint URL</Label>
                      <Input
                        id="endpoint"
                        value={formData.config.endpoint || ''}
                        onChange={(e) => handleConfigChange('endpoint', e.target.value)}
                        placeholder="https://api.example.com/data"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="method">HTTP Method</Label>
                      <Select
                        value={formData.config.method}
                        onValueChange={(value) => handleConfigChange('method', value)}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="GET">GET</SelectItem>
                          <SelectItem value="POST">POST</SelectItem>
                          <SelectItem value="PUT">PUT</SelectItem>
                          <SelectItem value="DELETE">DELETE</SelectItem>
                          <SelectItem value="PATCH">PATCH</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="timeout">Timeout (ms)</Label>
                      <Input
                        id="timeout"
                        type="number"
                        value={formData.config.timeout || 30000}
                        onChange={(e) => handleConfigChange('timeout', parseInt(e.target.value))}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="retries">Retries</Label>
                      <Input
                        id="retries"
                        type="number"
                        value={formData.config.retries || 3}
                        onChange={(e) => handleConfigChange('retries', parseInt(e.target.value))}
                      />
                    </div>
                  </div>

                  <Separator />

                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <Label>Headers</Label>
                      <Button
                        type="button"
                        size="sm"
                        onClick={handleAddHeader}
                        className="gap-2"
                      >
                        <Plus className="h-3 w-3" />
                        Add Header
                      </Button>
                    </div>
                    {Object.entries(formData.config.headers || {}).map(([key, value]) => (
                      <div key={key} className="flex items-center gap-2">
                        <Input value={key} disabled className="flex-1" />
                        <span>:</span>
                        <Input value={value as string} disabled className="flex-1" />
                        <Button
                          type="button"
                          size="sm"
                          variant="ghost"
                          onClick={() => handleRemoveHeader(key)}
                        >
                          <Trash className="h-3 w-3" />
                        </Button>
                      </div>
                    ))}
                  </div>

                  <Separator />

                  <div className="space-y-4">
                    <Label>Authentication</Label>
                    <Select
                      value={formData.config.authentication?.type}
                      onValueChange={(value) => handleAuthChange('type', value)}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="none">None</SelectItem>
                        <SelectItem value="api_key">API Key</SelectItem>
                        <SelectItem value="bearer">Bearer Token</SelectItem>
                        <SelectItem value="basic">Basic Auth</SelectItem>
                      </SelectContent>
                    </Select>

                    {formData.config.authentication?.type === 'api_key' && (
                      <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label>Header Name</Label>
                          <Input
                            value={formData.config.authentication.credentials?.headerName || 'X-API-Key'}
                            onChange={(e) => handleAuthChange('credentials', {
                              ...formData.config.authentication.credentials,
                              headerName: e.target.value,
                            })}
                          />
                        </div>
                        <div className="space-y-2">
                          <Label>API Key</Label>
                          <Input
                            type="password"
                            value={formData.config.authentication.credentials?.apiKey || ''}
                            onChange={(e) => handleAuthChange('credentials', {
                              ...formData.config.authentication.credentials,
                              apiKey: e.target.value,
                            })}
                          />
                        </div>
                      </div>
                    )}

                    {formData.config.authentication?.type === 'bearer' && (
                      <div className="space-y-2">
                        <Label>Bearer Token</Label>
                        <Input
                          type="password"
                          value={formData.config.authentication.credentials?.token || ''}
                          onChange={(e) => handleAuthChange('credentials', {
                            ...formData.config.authentication.credentials,
                            token: e.target.value,
                          })}
                        />
                      </div>
                    )}

                    {formData.config.authentication?.type === 'basic' && (
                      <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label>Username</Label>
                          <Input
                            value={formData.config.authentication.credentials?.username || ''}
                            onChange={(e) => handleAuthChange('credentials', {
                              ...formData.config.authentication.credentials,
                              username: e.target.value,
                            })}
                          />
                        </div>
                        <div className="space-y-2">
                          <Label>Password</Label>
                          <Input
                            type="password"
                            value={formData.config.authentication.credentials?.password || ''}
                            onChange={(e) => handleAuthChange('credentials', {
                              ...formData.config.authentication.credentials,
                              password: e.target.value,
                            })}
                          />
                        </div>
                      </div>
                    )}
                  </div>
                </>
              )}

              {formData.type === 'function' && (
                <div className="space-y-2">
                  <Label htmlFor="functionCode">Function Code</Label>
                  <Textarea
                    id="functionCode"
                    value={formData.config.functionCode || ''}
                    onChange={(e) => handleConfigChange('functionCode', e.target.value)}
                    placeholder="// Your JavaScript function code here
return input.value * 2;"
                    rows={10}
                    className="font-mono"
                  />
                  <p className="text-sm text-muted-foreground">
                    Write JavaScript code that processes the input and returns a result.
                    The input object is available as 'input'.
                  </p>
                </div>
              )}

              {formData.type === 'workflow' && (
                <div className="space-y-4">
                  <Label>Workflow Steps</Label>
                  <p className="text-sm text-muted-foreground">
                    Workflow configuration will be available in the next version.
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="schema" className="space-y-4">
          <SchemaBuilder
            schema={formData.schema}
            onChange={handleSchemaChange}
          />
        </TabsContent>

        <TabsContent value="test" className="space-y-4">
          <ToolTester
            toolId={tool?.id}
            toolConfig={formData.config}
            schema={formData.schema}
          />
        </TabsContent>

        <TabsContent value="metadata" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Metadata</CardTitle>
              <CardDescription>
                Additional information about your tool
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="version">Version</Label>
                  <Input
                    id="version"
                    value={formData.metadata?.version || ''}
                    onChange={(e) => handleMetadataChange('version', e.target.value)}
                    placeholder="1.0.0"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="category">Category</Label>
                  <Input
                    id="category"
                    value={formData.metadata?.category || ''}
                    onChange={(e) => handleMetadataChange('category', e.target.value)}
                    placeholder="Data Processing"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="documentation">Documentation</Label>
                <Textarea
                  id="documentation"
                  value={formData.metadata?.documentation || ''}
                  onChange={(e) => handleMetadataChange('documentation', e.target.value)}
                  placeholder="Detailed documentation about how to use this tool..."
                  rows={5}
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      <div className="flex justify-end gap-2">
        <Button variant="outline" onClick={onCancel}>
          Cancel
        </Button>
        <Button onClick={handleSave} disabled={saving}>
          {saving ? 'Saving...' : tool ? 'Update Tool' : 'Create Tool'}
        </Button>
      </div>
    </div>
  );
}