import { apiClient } from './client';

export interface Template {
  id: string;
  name: string;
  description: string;
  category: TemplateCategory;
  template: TemplateConfig;
  isPublic: boolean;
  organizationId?: string;
  metadata: any;
  createdAt: string;
  updatedAt: string;
  organization?: {
    id: string;
    name: string;
    slug: string;
  };
}

export enum TemplateCategory {
  CUSTOMER_SERVICE = 'customer-service',
  CONTENT_CREATION = 'content-creation',
  DATA_ANALYSIS = 'data-analysis',
  AUTOMATION = 'automation',
  EDUCATION = 'education',
  HEALTHCARE = 'healthcare',
  FINANCE = 'finance',
  MARKETING = 'marketing',
  SALES = 'sales',
  RESEARCH = 'research',
  GENERAL = 'general',
}

export interface TemplateConfig {
  prompt: string;
  systemPrompt?: string;
  config?: {
    temperature?: number;
    maxTokens?: number;
    model?: string;
    memory?: {
      enabled: boolean;
      maxMessages: number;
      strategy: 'rolling' | 'summarize' | 'vector';
    };
  };
  tools?: string[];
  parameters?: Array<{
    name: string;
    type: 'string' | 'number' | 'boolean' | 'array' | 'object';
    description: string;
    required: boolean;
    defaultValue?: any;
  }>;
}

export interface CreateTemplateRequest {
  name: string;
  description: string;
  category: TemplateCategory;
  template: TemplateConfig;
  isPublic?: boolean;
  tags?: string[];
  metadata?: {
    version?: string;
    author?: string;
    license?: string;
    website?: string;
    examples?: Array<{
      input: string;
      output: string;
      description: string;
    }>;
  };
}

export interface UpdateTemplateRequest {
  name?: string;
  description?: string;
  category?: TemplateCategory;
  template?: Partial<TemplateConfig>;
  isPublic?: boolean;
  tags?: string[];
  metadata?: any;
}

export interface TemplateQueryParams {
  page?: number;
  limit?: number;
  category?: TemplateCategory;
  search?: string;
  isPublic?: boolean;
  tags?: string[];
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface TemplateStats {
  total: number;
  totalDownloads: number;
  byCategory: Record<string, number>;
  byVisibility: Record<string, number>;
}

export const templatesApi = {
  async getAll(params?: TemplateQueryParams): Promise<{ templates: Template[]; pagination: any }> {
    const queryParams = new URLSearchParams();
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) {
          if (Array.isArray(value)) {
            value.forEach(v => queryParams.append(key, v));
          } else {
            queryParams.set(key, value.toString());
          }
        }
      });
    }
    return apiClient.get(`/api/v1/templates?${queryParams.toString()}`);
  },

  async getById(id: string): Promise<Template> {
    return apiClient.get(`/api/v1/templates/${id}`);
  },

  async create(data: CreateTemplateRequest): Promise<Template> {
    return apiClient.post('/api/v1/templates', data);
  },

  async update(id: string, data: UpdateTemplateRequest): Promise<Template> {
    return apiClient.patch(`/api/v1/templates/${id}`, data);
  },

  async delete(id: string): Promise<{ message: string }> {
    return apiClient.delete(`/api/v1/templates/${id}`);
  },

  async getPopular(limit?: number): Promise<Template[]> {
    const params = limit ? `?limit=${limit}` : '';
    return apiClient.get(`/api/v1/templates/popular${params}`);
  },

  async getByCategory(category: string): Promise<Template[]> {
    return apiClient.get(`/api/v1/templates/category/${category}`);
  },

  async search(
    searchTerm: string,
    filters?: { category?: string; minRating?: number }
  ): Promise<Template[]> {
    const queryParams = new URLSearchParams({ q: searchTerm });
    if (filters?.category) queryParams.set('category', filters.category);
    if (filters?.minRating) queryParams.set('minRating', filters.minRating.toString());
    
    return apiClient.get(`/api/v1/templates/search?${queryParams.toString()}`);
  },

  async fork(id: string, data: {
    name: string;
    description?: string;
    isPublic?: boolean;
  }): Promise<Template> {
    return apiClient.post(`/api/v1/templates/${id}/fork`, data);
  },

  async rate(id: string, data: {
    rating: number;
    review?: string;
  }): Promise<Template> {
    return apiClient.post(`/api/v1/templates/${id}/rate`, data);
  },

  async getStats(): Promise<TemplateStats> {
    return apiClient.get('/api/v1/templates/stats');
  },
};