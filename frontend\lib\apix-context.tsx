'use client';

import { createContext, useContext, useEffect, useState, ReactNode, useCallback } from 'react';
import { useAuth } from '@/lib/auth-context';
import { io, Socket } from 'socket.io-client';
import toast from 'react-hot-toast';

interface APIMXEvent {
  id: string;
  type: string;
  organizationId: string;
  userId?: string;
  sessionId?: string;
  payload: any;
  timestamp: string;
}

interface APIMXContextType {
  socket: Socket | null;
  connected: boolean;
  joinSession: (sessionId: string) => void;
  leaveSession: (sessionId: string) => void;
  addEventListener: (eventType: string, handler: (event: APIMXEvent) => void) => () => void;
  sendEvent: (type: string, payload: any) => void;
  connectionStatus: 'connecting' | 'connected' | 'disconnected' | 'error';
}

const APIMXContext = createContext<APIMXContextType | undefined>(undefined);

export function APIMXProvider({ children }: { children: ReactNode }) {
  const { user } = useAuth();
  const [socket, setSocket] = useState<Socket | null>(null);
  const [connected, setConnected] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState<'connecting' | 'connected' | 'disconnected' | 'error'>('disconnected');
  const [eventHandlers, setEventHandlers] = useState<Map<string, Set<(event: APIMXEvent) => void>>>(new Map());
  const [reconnectAttempts, setReconnectAttempts] = useState(0);
  const maxReconnectAttempts = 5;

  useEffect(() => {
    if (user) {
      connect();
    } else {
      disconnect();
    }

    return () => {
      disconnect();
    };
  }, [user]);

  const connect = useCallback(() => {
    if (socket?.connected) return;

    const token = localStorage.getItem('auth_token');
    if (!token) return;

    setConnectionStatus('connecting');

    const newSocket = io(`${process.env.NEXT_PUBLIC_BACKEND_URL || 'http://localhost:3001'}/apix`, {
      auth: { token },
      transports: ['websocket', 'polling'],
      timeout: 20000,
      forceNew: true,
    });

    newSocket.on('connect', () => {
      setConnected(true);
      setConnectionStatus('connected');
      setReconnectAttempts(0);
      console.log('APIX connected');
    });

    newSocket.on('disconnect', (reason) => {
      setConnected(false);
      setConnectionStatus('disconnected');
      console.log('APIX disconnected:', reason);

      if (reason === 'io server disconnect') {
        setTimeout(() => {
          if (reconnectAttempts < maxReconnectAttempts) {
            setReconnectAttempts(prev => prev + 1);
            connect();
          }
        }, Math.pow(2, reconnectAttempts) * 1000);
      }
    });

    newSocket.on('connect_error', (error) => {
      setConnectionStatus('error');
      console.error('APIX connection error:', error);
      
      if (reconnectAttempts < maxReconnectAttempts) {
        setTimeout(() => {
          setReconnectAttempts(prev => prev + 1);
          connect();
        }, Math.pow(2, reconnectAttempts) * 1000);
      } else {
        toast.error('Failed to connect to real-time services');
      }
    });

    newSocket.on('connected', (data) => {
      console.log('APIX authentication successful:', data);
    });

    newSocket.on('apix_event', (event: APIMXEvent) => {
      const handlers = eventHandlers.get(event.type) || eventHandlers.get('*');
      if (handlers) {
        handlers.forEach(handler => {
          try {
            handler(event);
          } catch (error) {
            console.error('Error handling APIX event:', error);
          }
        });
      }

      handleSystemEvents(event);
    });

    newSocket.on('session_joined', (data) => {
      console.log('Joined session:', data.sessionId);
    });

    newSocket.on('session_left', (data) => {
      console.log('Left session:', data.sessionId);
    });

    newSocket.on('pong', () => {
      console.log('APIX ping/pong successful');
    });

    setSocket(newSocket);
  }, [eventHandlers, reconnectAttempts]);

  const disconnect = useCallback(() => {
    if (socket) {
      socket.disconnect();
      setSocket(null);
    }
    setConnected(false);
    setConnectionStatus('disconnected');
    setReconnectAttempts(0);
  }, [socket]);

  const joinSession = useCallback((sessionId: string) => {
    if (socket?.connected) {
      socket.emit('join_session', { sessionId });
    }
  }, [socket]);

  const leaveSession = useCallback((sessionId: string) => {
    if (socket?.connected) {
      socket.emit('leave_session', { sessionId });
    }
  }, [socket]);

  const addEventListener = useCallback((eventType: string, handler: (event: APIMXEvent) => void) => {
    setEventHandlers(prev => {
      const newHandlers = new Map(prev);
      if (!newHandlers.has(eventType)) {
        newHandlers.set(eventType, new Set());
      }
      newHandlers.get(eventType)!.add(handler);
      return newHandlers;
    });

    return () => {
      setEventHandlers(prev => {
        const newHandlers = new Map(prev);
        const handlers = newHandlers.get(eventType);
        if (handlers) {
          handlers.delete(handler);
          if (handlers.size === 0) {
            newHandlers.delete(eventType);
          }
        }
        return newHandlers;
      });
    };
  }, []);

  const sendEvent = useCallback((type: string, payload: any) => {
    if (socket?.connected) {
      socket.emit('custom_event', { type, payload });
    }
  }, [socket]);

  const handleSystemEvents = (event: APIMXEvent) => {
    switch (event.type) {
      case 'system.quota_exceeded':
        toast.error(`Quota exceeded: ${event.payload.quotaType}`);
        break;
      case 'system.quota_warning':
        toast.warning(`Quota warning: ${event.payload.quotaType} at ${event.payload.percentage}%`);
        break;
      case 'billing.payment_failed':
        toast.error('Payment failed. Please update your billing information.');
        break;
      case 'agent.execution_completed':
        toast.success('Agent execution completed');
        break;
      case 'agent.execution_failed':
        toast.error(`Agent execution failed: ${event.payload.error}`);
        break;
      case 'tool.execution_completed':
        toast.success('Tool execution completed');
        break;
      case 'tool.execution_failed':
        toast.error(`Tool execution failed: ${event.payload.error}`);
        break;
      default:
        break;
    }
  };

  useEffect(() => {
    const interval = setInterval(() => {
      if (socket?.connected) {
        socket.emit('ping');
      }
    }, 30000);

    return () => clearInterval(interval);
  }, [socket]);

  const value: APIMXContextType = {
    socket,
    connected,
    joinSession,
    leaveSession,
    addEventListener,
    sendEvent,
    connectionStatus,
  };

  return (
    <APIMXContext.Provider value={value}>
      {children}
    </APIMXContext.Provider>
  );
}

export function useAPIMX() {
  const context = useContext(APIMXContext);
  if (context === undefined) {
    throw new Error('useAPIMX must be used within an APIMXProvider');
  }
  return context;
}