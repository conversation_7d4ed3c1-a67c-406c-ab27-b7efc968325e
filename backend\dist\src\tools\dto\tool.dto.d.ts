export declare enum ToolType {
    API = "api",
    FUNCTION = "function",
    WORKFLOW = "workflow"
}
export declare enum ToolStatus {
    DRAFT = "draft",
    ACTIVE = "active",
    ARCHIVED = "archived"
}
export declare enum HttpMethod {
    GET = "GET",
    POST = "POST",
    PUT = "PUT",
    DELETE = "DELETE",
    PATCH = "PATCH"
}
export declare enum AuthType {
    NONE = "none",
    API_KEY = "api_key",
    BEARER = "bearer",
    BASIC = "basic",
    OAUTH2 = "oauth2"
}
export declare class ToolConfigDto {
    endpoint?: string;
    method?: HttpMethod;
    headers?: Record<string, string>;
    authentication?: {
        type: AuthType;
        credentials: Record<string, string>;
    };
    timeout?: number;
    retries?: number;
    parameters?: Record<string, any>;
    functionCode?: string;
    workflowSteps?: Array<{
        id: string;
        type: 'api' | 'function' | 'condition';
        config: any;
        nextSteps?: string[];
    }>;
}
export declare class ToolSchemaDto {
    input: Record<string, any>;
    output: Record<string, any>;
    examples?: Array<{
        name: string;
        description: string;
        input: any;
        output: any;
    }>;
}
export declare class CreateToolDto {
    name: string;
    description: string;
    type: ToolType;
    config: ToolConfigDto;
    schema: ToolSchemaDto;
    status?: ToolStatus;
    tags?: string[];
    metadata?: {
        version?: string;
        author?: string;
        category?: string;
        documentation?: string;
    };
}
export declare class UpdateToolDto {
    name?: string;
    description?: string;
    config?: Partial<ToolConfigDto>;
    schema?: Partial<ToolSchemaDto>;
    status?: ToolStatus;
    tags?: string[];
    metadata?: any;
}
export declare class ToolQueryDto {
    page?: number;
    limit?: number;
    type?: ToolType;
    status?: ToolStatus;
    search?: string;
    category?: string;
    tags?: string[];
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
}
export declare class ExecuteToolDto {
    input: Record<string, any>;
    sessionId?: string;
    context?: Record<string, any>;
    validateInput?: boolean;
    overrides?: {
        timeout?: number;
        retries?: number;
        headers?: Record<string, string>;
    };
}
export declare class TestToolDto {
    input: Record<string, any>;
    dryRun?: boolean;
}
export declare class ToolVersionDto {
    version: string;
    description: string;
    config: ToolConfigDto;
    schema: ToolSchemaDto;
    metadata?: Record<string, any>;
}
