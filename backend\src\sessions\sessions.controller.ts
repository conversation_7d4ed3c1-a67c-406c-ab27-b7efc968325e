import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  Request,
} from '@nestjs/common';
import { SessionsService } from './sessions.service';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { OrganizationGuard } from '../common/guards/organization.guard';
import { CurrentUser, CurrentOrganization } from '../common/decorators/current-user.decorator';
import {
  CreateSessionDto,
  UpdateSessionDto,
  SessionQueryDto,
  AddMessageDto,
  UpdateContextDto,
} from './dto/session.dto';

@Controller('sessions')
@UseGuards(JwtAuthGuard, OrganizationGuard)
export class SessionsController {
  constructor(private readonly sessionsService: SessionsService) {}

  @Post()
  create(
    @CurrentUser('id') userId: string,
    @CurrentOrganization('id') organizationId: string,
    @Body() createSessionDto: CreateSessionDto,
  ) {
    return this.sessionsService.create(userId, organizationId, createSessionDto);
  }

  @Get()
  findAll(
    @CurrentOrganization('id') organizationId: string,
    @Query() query: SessionQueryDto,
  ) {
    return this.sessionsService.findAll(organizationId, query);
  }

  @Get('stats')
  getStats(
    @CurrentOrganization('id') organizationId: string,
    @CurrentUser('id') userId: string,
  ) {
    return this.sessionsService.getSessionStats(organizationId, userId);
  }

  @Get('my')
  findMy(
    @CurrentUser('id') userId: string,
    @CurrentOrganization('id') organizationId: string,
    @Query() query: SessionQueryDto,
  ) {
    return this.sessionsService.findAll(organizationId, { ...query, userId });
  }

  @Get(':id')
  findOne(
    @Param('id') id: string,
    @CurrentOrganization('id') organizationId: string,
    @CurrentUser('id') userId: string,
  ) {
    return this.sessionsService.findOne(id, organizationId, userId);
  }

  @Patch(':id')
  update(
    @Param('id') id: string,
    @CurrentOrganization('id') organizationId: string,
    @CurrentUser('id') userId: string,
    @Body() updateSessionDto: UpdateSessionDto,
  ) {
    return this.sessionsService.update(id, organizationId, userId, updateSessionDto);
  }

  @Delete(':id')
  remove(
    @Param('id') id: string,
    @CurrentOrganization('id') organizationId: string,
    @CurrentUser('id') userId: string,
  ) {
    return this.sessionsService.remove(id, organizationId, userId);
  }

  @Post(':id/messages')
  addMessage(
    @Param('id') sessionId: string,
    @CurrentOrganization('id') organizationId: string,
    @CurrentUser('id') userId: string,
    @Body() addMessageDto: AddMessageDto,
  ) {
    return this.sessionsService.addMessage(sessionId, organizationId, userId, addMessageDto);
  }

  @Patch(':id/context')
  updateContext(
    @Param('id') sessionId: string,
    @CurrentOrganization('id') organizationId: string,
    @CurrentUser('id') userId: string,
    @Body() updateContextDto: UpdateContextDto,
  ) {
    return this.sessionsService.updateContext(sessionId, organizationId, userId, updateContextDto.context);
  }

  @Post(':id/clear-memory')
  clearMemory(
    @Param('id') sessionId: string,
    @CurrentOrganization('id') organizationId: string,
    @CurrentUser('id') userId: string,
  ) {
    return this.sessionsService.clearMemory(sessionId, organizationId, userId);
  }
}