"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AgentsController = void 0;
const common_1 = require("@nestjs/common");
const express_1 = require("express");
const passport_1 = require("@nestjs/passport");
const agents_service_1 = require("./agents.service");
const agent_executor_service_1 = require("./agent-executor.service");
const organization_guard_1 = require("../common/guards/organization.guard");
const current_user_decorator_1 = require("../common/decorators/current-user.decorator");
const agent_dto_1 = require("./dto/agent.dto");
let AgentsController = class AgentsController {
    constructor(agentsService, agentExecutorService) {
        this.agentsService = agentsService;
        this.agentExecutorService = agentExecutorService;
    }
    create(userId, organizationId, createAgentDto) {
        return this.agentsService.create(userId, organizationId, createAgentDto);
    }
    findAll(organizationId, query) {
        return this.agentsService.findAll(organizationId, query);
    }
    getStats(organizationId) {
        return this.agentsService.getAgentStats(organizationId);
    }
    getPopularModels(organizationId) {
        return this.agentsService.getPopularModels(organizationId);
    }
    searchByTags(organizationId, tags) {
        const tagArray = Array.isArray(tags) ? tags : [tags];
        return this.agentsService.searchAgentsByTags(organizationId, tagArray);
    }
    findOne(id, organizationId) {
        return this.agentsService.findOne(id, organizationId);
    }
    update(id, organizationId, updateAgentDto) {
        return this.agentsService.update(id, organizationId, updateAgentDto);
    }
    remove(id, organizationId) {
        return this.agentsService.remove(id, organizationId);
    }
    async executeAgent(id, userId, organizationId, executeDto, res) {
        if (executeDto.streaming) {
            res.setHeader('Content-Type', 'text/event-stream');
            res.setHeader('Cache-Control', 'no-cache');
            res.setHeader('Connection', 'keep-alive');
            res.setHeader('Access-Control-Allow-Origin', '*');
            const stream = this.agentExecutorService.executeAgentStreaming(id, organizationId, userId, executeDto);
            for await (const chunk of stream) {
                res.write(`data: ${JSON.stringify(chunk)}\n\n`);
            }
            res.end();
        }
        else {
            const result = await this.agentExecutorService.executeAgent(id, organizationId, userId, executeDto);
            res.json(result);
        }
    }
    createVersion(id, organizationId, versionDto) {
        return this.agentsService.createVersion(id, organizationId, versionDto);
    }
    rollbackToVersion(id, version, organizationId) {
        return this.agentsService.rollbackToVersion(id, organizationId, version);
    }
    clone(id, organizationId, cloneDto) {
        return this.agentsService.clone(id, organizationId, cloneDto);
    }
    getExecutionHistory(id, organizationId, limit) {
        return this.agentExecutorService.getExecutionHistory(id, organizationId, limit ? parseInt(limit.toString()) : 50);
    }
    getExecutionMetrics(id, organizationId) {
        return this.agentExecutorService.getExecutionMetrics(id, organizationId);
    }
    async testAgent(id, userId, organizationId, testDto) {
        return this.agentExecutorService.executeAgent(id, organizationId, userId, {
            ...testDto,
            streaming: false,
        });
    }
};
exports.AgentsController = AgentsController;
__decorate([
    (0, common_1.Post)(),
    __param(0, (0, current_user_decorator_1.CurrentUser)('id')),
    __param(1, (0, current_user_decorator_1.CurrentOrganization)('id')),
    __param(2, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, agent_dto_1.CreateAgentDto]),
    __metadata("design:returntype", void 0)
], AgentsController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    __param(0, (0, current_user_decorator_1.CurrentOrganization)('id')),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, agent_dto_1.AgentQueryDto]),
    __metadata("design:returntype", void 0)
], AgentsController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('stats'),
    __param(0, (0, current_user_decorator_1.CurrentOrganization)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], AgentsController.prototype, "getStats", null);
__decorate([
    (0, common_1.Get)('models/popular'),
    __param(0, (0, current_user_decorator_1.CurrentOrganization)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], AgentsController.prototype, "getPopularModels", null);
__decorate([
    (0, common_1.Get)('search/tags'),
    __param(0, (0, current_user_decorator_1.CurrentOrganization)('id')),
    __param(1, (0, common_1.Query)('tags')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Array]),
    __metadata("design:returntype", void 0)
], AgentsController.prototype, "searchByTags", null);
__decorate([
    (0, common_1.Get)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, current_user_decorator_1.CurrentOrganization)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", void 0)
], AgentsController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, current_user_decorator_1.CurrentOrganization)('id')),
    __param(2, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, agent_dto_1.UpdateAgentDto]),
    __metadata("design:returntype", void 0)
], AgentsController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, current_user_decorator_1.CurrentOrganization)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", void 0)
], AgentsController.prototype, "remove", null);
__decorate([
    (0, common_1.Post)(':id/execute'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, current_user_decorator_1.CurrentUser)('id')),
    __param(2, (0, current_user_decorator_1.CurrentOrganization)('id')),
    __param(3, (0, common_1.Body)()),
    __param(4, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String, agent_dto_1.ExecuteAgentDto, typeof (_a = typeof express_1.Response !== "undefined" && express_1.Response) === "function" ? _a : Object]),
    __metadata("design:returntype", Promise)
], AgentsController.prototype, "executeAgent", null);
__decorate([
    (0, common_1.Post)(':id/versions'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, current_user_decorator_1.CurrentOrganization)('id')),
    __param(2, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, agent_dto_1.AgentVersionDto]),
    __metadata("design:returntype", void 0)
], AgentsController.prototype, "createVersion", null);
__decorate([
    (0, common_1.Post)(':id/rollback/:version'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Param)('version')),
    __param(2, (0, current_user_decorator_1.CurrentOrganization)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String]),
    __metadata("design:returntype", void 0)
], AgentsController.prototype, "rollbackToVersion", null);
__decorate([
    (0, common_1.Post)(':id/clone'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, current_user_decorator_1.CurrentOrganization)('id')),
    __param(2, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, agent_dto_1.CloneAgentDto]),
    __metadata("design:returntype", void 0)
], AgentsController.prototype, "clone", null);
__decorate([
    (0, common_1.Get)(':id/executions'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, current_user_decorator_1.CurrentOrganization)('id')),
    __param(2, (0, common_1.Query)('limit')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, Number]),
    __metadata("design:returntype", void 0)
], AgentsController.prototype, "getExecutionHistory", null);
__decorate([
    (0, common_1.Get)(':id/metrics'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, current_user_decorator_1.CurrentOrganization)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", void 0)
], AgentsController.prototype, "getExecutionMetrics", null);
__decorate([
    (0, common_1.Post)(':id/test'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, current_user_decorator_1.CurrentUser)('id')),
    __param(2, (0, current_user_decorator_1.CurrentOrganization)('id')),
    __param(3, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String, Object]),
    __metadata("design:returntype", Promise)
], AgentsController.prototype, "testAgent", null);
exports.AgentsController = AgentsController = __decorate([
    (0, common_1.Controller)('agents'),
    (0, common_1.UseGuards)((0, passport_1.AuthGuard)('jwt'), organization_guard_1.OrganizationGuard),
    __metadata("design:paramtypes", [agents_service_1.AgentsService,
        agent_executor_service_1.AgentExecutorService])
], AgentsController);
//# sourceMappingURL=agents.controller.js.map