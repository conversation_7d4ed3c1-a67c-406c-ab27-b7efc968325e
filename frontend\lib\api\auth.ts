import { apiClient } from './client';

export interface LoginRequest {
  email: string;
  password: string;
}

export interface RegisterRequest {
  email: string;
  name: string;
  password: string;
  organizationName: string;
  organizationSlug: string;
}

export interface AuthResponse {
  access_token: string;
  user: {
    id: string;
    email: string;
    name: string;
    avatarUrl?: string;
    organizationId: string;
    organization: {
      id: string;
      name: string;
      slug: string;
      settings: any;
    };
    roles: Array<{
      id: string;
      name: string;
      permissions: string[];
    }>;
    settings: any;
  };
}

export interface RefreshResponse {
  access_token: string;
}

export const authApi = {
  async login(data: LoginRequest): Promise<AuthResponse> {
    return apiClient.post('/api/v1/auth/login', data);
  },

  async register(data: RegisterRequest): Promise<AuthResponse> {
    return apiClient.post('/api/v1/auth/register', data);
  },

  async me(): Promise<AuthResponse['user']> {
    return apiClient.get('/api/v1/auth/me');
  },

  async refresh(): Promise<RefreshResponse> {
    return apiClient.post('/api/v1/auth/refresh');
  },

  async logout(): Promise<void> {
    localStorage.removeItem('auth_token');
  },
};