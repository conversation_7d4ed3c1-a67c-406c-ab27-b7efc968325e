{"name": "@synapseai/backend", "version": "1.0.0", "description": "NESTJS backend service for SynapseAI platform", "main": "dist/main.js", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "type-check": "tsc --noEmit", "db:migrate": "prisma migrate dev", "db:migrate:prod": "prisma migrate deploy", "db:seed": "prisma db seed", "db:studio": "prisma studio", "db:generate": "prisma generate", "db:reset": "prisma migrate reset"}, "dependencies": {"@nestjs/common": "^10.2.8", "@nestjs/core": "^10.2.8", "@nestjs/platform-express": "^10.2.8", "@nestjs/platform-socket.io": "^10.2.8", "@nestjs/websockets": "^10.2.8", "@nestjs/jwt": "^10.2.0", "@nestjs/passport": "^10.0.2", "@nestjs/config": "^3.1.1", "@nestjs/throttler": "^5.0.1", "@nestjs/cache-manager": "^2.1.1", "@prisma/client": "^5.6.0", "@synapseai/shared": "workspace:*", "bcryptjs": "^2.4.3", "cache-manager": "^5.2.4", "cache-manager-redis-store": "^3.0.1", "class-transformer": "^0.5.1", "class-validator": "^0.14.0", "helmet": "^7.1.0", "ioredis": "^5.3.2", "passport": "^0.6.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.1", "socket.io": "^4.7.4", "winston": "^3.11.0", "stripe": "^14.8.0", "nodemailer": "^6.9.7", "twilio": "^4.19.0", "openai": "^4.20.1", "@anthropic-ai/sdk": "^0.9.1", "@google-ai/generativelanguage": "^2.5.0", "groq-sdk": "^0.3.3", "vm2": "^3.9.19"}, "devDependencies": {"@nestjs/cli": "^10.2.1", "@nestjs/schematics": "^10.0.3", "@nestjs/testing": "^10.2.8", "@types/express": "^4.17.21", "@types/jest": "^29.5.8", "@types/node": "^20.10.4", "@types/supertest": "^2.0.16", "@types/bcryptjs": "^2.4.6", "@types/passport-jwt": "^3.0.13", "@types/passport-local": "^1.0.38", "@types/nodemailer": "^6.4.14", "@types/vm2": "^1.3.1", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "eslint": "^8.54.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.1", "jest": "^29.7.0", "prettier": "^3.1.0", "prisma": "^5.6.0", "source-map-support": "^0.5.21", "supertest": "^6.3.3", "ts-jest": "^29.1.1", "ts-loader": "^9.5.1", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.3.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}, "prisma": {"seed": "ts-node prisma/seed.ts"}}