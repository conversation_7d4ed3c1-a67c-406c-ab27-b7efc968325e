import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
} from '@nestjs/common';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { TemplatesService } from './templates.service';
import { OrganizationGuard } from '../common/guards/organization.guard';
import { CurrentUser, CurrentOrganization } from '../common/decorators/current-user.decorator';
import {
  CreateTemplateDto,
  UpdateTemplateDto,
  TemplateQueryDto,
  RateTemplateDto,
  ForkTemplateDto,
} from './dto/template.dto';

@Controller('templates')
@UseGuards(JwtAuthGuard, OrganizationGuard)
export class TemplatesController {
  constructor(private readonly templatesService: TemplatesService) {}

  @Post()
  create(
    @CurrentUser('id') userId: string,
    @CurrentOrganization('id') organizationId: string,
    @Body() createTemplateDto: CreateTemplateDto,
  ) {
    return this.templatesService.create(userId, organizationId, createTemplateDto);
  }

  @Get()
  findAll(
    @CurrentOrganization('id') organizationId: string,
    @Query() query: TemplateQueryDto,
  ) {
    return this.templatesService.findAll(organizationId, query);
  }

  @Get('popular')
  getPopular(
    @CurrentOrganization('id') organizationId: string,
    @Query('limit') limit?: number,
  ) {
    return this.templatesService.getPopularTemplates(
      organizationId,
      limit ? parseInt(limit.toString()) : 10,
    );
  }

  @Get('stats')
  getStats(@CurrentOrganization('id') organizationId: string) {
    return this.templatesService.getTemplateStats(organizationId);
  }

  @Get('category/:category')
  getByCategory(
    @Param('category') category: string,
    @CurrentOrganization('id') organizationId: string,
  ) {
    return this.templatesService.getTemplatesByCategory(organizationId, category);
  }

  @Get('search')
  search(
    @CurrentOrganization('id') organizationId: string,
    @Query('q') searchTerm: string,
    @Query('category') category?: string,
    @Query('minRating') minRating?: number,
  ) {
    return this.templatesService.searchTemplates(organizationId, searchTerm, {
      category,
      minRating: minRating ? parseFloat(minRating.toString()) : undefined,
    });
  }

  @Get(':id')
  findOne(
    @Param('id') id: string,
    @CurrentOrganization('id') organizationId: string,
  ) {
    return this.templatesService.findOne(id, organizationId);
  }

  @Patch(':id')
  update(
    @Param('id') id: string,
    @CurrentOrganization('id') organizationId: string,
    @Body() updateTemplateDto: UpdateTemplateDto,
  ) {
    return this.templatesService.update(id, organizationId, updateTemplateDto);
  }

  @Delete(':id')
  remove(
    @Param('id') id: string,
    @CurrentOrganization('id') organizationId: string,
  ) {
    return this.templatesService.remove(id, organizationId);
  }

  @Post(':id/fork')
  fork(
    @Param('id') id: string,
    @CurrentOrganization('id') organizationId: string,
    @Body() forkDto: ForkTemplateDto,
  ) {
    return this.templatesService.fork(id, organizationId, forkDto);
  }

  @Post(':id/rate')
  rate(
    @Param('id') id: string,
    @CurrentOrganization('id') organizationId: string,
    @CurrentUser('id') userId: string,
    @Body() rateDto: RateTemplateDto,
  ) {
    return this.templatesService.rate(id, organizationId, userId, rateDto);
  }
}