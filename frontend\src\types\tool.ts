export enum ToolType {
  API = 'api',
  FUNCTION = 'function',
  WORKFLOW = 'workflow',
}

export enum ToolStatus {
  DRAFT = 'draft',
  ACTIVE = 'active',
  ARCHIVED = 'archived',
}

export enum HttpMethod {
  GET = 'GET',
  POST = 'POST',
  PUT = 'PUT',
  DELETE = 'DELETE',
  PATCH = 'PATCH',
}

export enum AuthType {
  NONE = 'none',
  API_KEY = 'api_key',
  BEARER = 'bearer',
  BASIC = 'basic',
  OAUTH2 = 'oauth2',
}

export interface ToolConfig {
  endpoint?: string;
  method?: HttpMethod;
  headers?: Record<string, string>;
  authentication?: {
    type: AuthType;
    credentials: Record<string, string>;
  };
  timeout?: number;
  retries?: number;
  parameters?: Record<string, any>;
  functionCode?: string;
  workflowSteps?: Array<{
    id: string;
    type: 'api' | 'function' | 'condition';
    config: any;
    nextSteps?: string[];
  }>;
}

export interface ToolSchema {
  input: Record<string, any>;
  output: Record<string, any>;
  examples?: Array<{
    name: string;
    description: string;
    input: any;
    output: any;
  }>;
}

export interface Tool {
  id: string;
  name: string;
  description: string;
  type: ToolType;
  config: ToolConfig;
  schema: ToolSchema;
  status: ToolStatus;
  organizationId: string;
  metadata: {
    version?: string;
    author?: string;
    category?: string;
    documentation?: string;
    tags?: string[];
    executions?: number;
    lastExecuted?: string;
    versions?: Array<{
      version: string;
      description: string;
      config: ToolConfig;
      schema: ToolSchema;
      createdAt: string;
      metadata?: Record<string, any>;
    }>;
    currentVersion?: string;
    clonedFrom?: string;
    clonedAt?: string;
  };
  createdAt: string;
  updatedAt: string;
}

export interface CreateToolRequest {
  name: string;
  description: string;
  type: ToolType;
  config: ToolConfig;
  schema: ToolSchema;
  status?: ToolStatus;
  tags?: string[];
  metadata?: {
    version?: string;
    author?: string;
    category?: string;
    documentation?: string;
  };
}

export interface UpdateToolRequest {
  name?: string;
  description?: string;
  config?: Partial<ToolConfig>;
  schema?: Partial<ToolSchema>;
  status?: ToolStatus;
  tags?: string[];
  metadata?: any;
}

export interface ExecuteToolRequest {
  input: Record<string, any>;
  sessionId?: string;
  context?: Record<string, any>;
  validateInput?: boolean;
  overrides?: {
    timeout?: number;
    retries?: number;
    headers?: Record<string, string>;
  };
}

export interface TestToolRequest {
  input: Record<string, any>;
  dryRun?: boolean;
}

export interface ToolExecutionResult {
  success: boolean;
  output: any;
  executionTime: number;
  sessionId?: string;
  error?: string;
  metadata?: {
    httpStatus?: number;
    headers?: Record<string, string>;
    retryCount?: number;
  };
}

export interface ToolVersionRequest {
  version: string;
  description: string;
  config: ToolConfig;
  schema: ToolSchema;
  metadata?: Record<string, any>;
}

export interface ToolsResponse {
  tools: Tool[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}

export interface ToolQueryParams {
  page?: number;
  limit?: number;
  type?: ToolType;
  status?: ToolStatus;
  search?: string;
  category?: string;
  tags?: string[];
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface ToolStats {
  total: number;
  executions: number;
  byType: Record<string, number>;
  byStatus: Record<string, number>;
}

export interface ToolMetrics {
  totalExecutions: number;
  avgExecutionTime: number;
  successRate: number;
  lastExecutedAt?: string;
}

export interface ToolExecutionHistory {
  id: string;
  organizationId: string;
  userId: string;
  user: {
    id: string;
    name: string;
    email: string;
  };
  type: string;
  status: string;
  metadata: any;
  createdAt: string;
  updatedAt: string;
}