import { Injectable, OnModuleInit, OnModuleDestroy, INestApplication } from '@nestjs/common';
import { PrismaClient } from '@prisma/client';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class PrismaService extends PrismaClient implements OnModuleInit, OnModuleDestroy {
  constructor(private configService: ConfigService) {
    super({
      datasources: {
        db: {
          url: configService.get('DATABASE_URL'),
        },
      },
      log: configService.get('NODE_ENV') === 'development' ? ['query', 'info', 'warn', 'error'] : ['error'],
    });
  }

  async onModuleInit() {
    await this.$connect();
  }

  async onModuleDestroy() {
    await this.$disconnect();
  }

  async enableShutdownHooks(app: INestApplication) {
    this.$on('beforeExit', async () => {
      await app.close();
    });
  }

  async withOrganization<T>(
    organizationId: string,
    callback: (prisma: PrismaClient) => Promise<T>,
  ): Promise<T> {
    return callback(this);
  }

  async getOrganizationContext(organizationId: string) {
    return this.organization.findUnique({
      where: { id: organizationId },
      include: {
        users: {
          include: {
            userRoles: {
              include: {
                role: true,
              },
            },
          },
        },
        quotas: true,
      },
    });
  }
}