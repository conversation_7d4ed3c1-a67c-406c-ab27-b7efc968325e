import { PrismaService } from '../prisma/prisma.service';
import { APIMXService } from '../apix/apix.service';
import { CreateToolDto, UpdateToolDto, ToolQueryDto, ToolVersionDto } from './dto/tool.dto';
export declare class ToolsService {
    private prisma;
    private apixService;
    constructor(prisma: PrismaService, apixService: APIMXService);
    create(userId: string, organizationId: string, createToolDto: CreateToolDto): Promise<any>;
    findAll(organizationId: string, query: ToolQueryDto): Promise<{
        tools: any;
        pagination: {
            page: number;
            limit: number;
            total: any;
            pages: number;
        };
    }>;
    findOne(id: string, organizationId: string): Promise<any>;
    update(id: string, organizationId: string, updateToolDto: UpdateToolDto): Promise<any>;
    remove(id: string, organizationId: string): Promise<{
        message: string;
    }>;
    createVersion(id: string, organizationId: string, versionDto: ToolVersionDto): Promise<any>;
    rollbackToVersion(id: string, organizationId: string, version: string): Promise<any>;
    clone(id: string, organizationId: string, cloneData: {
        name: string;
        description?: string;
    }): Promise<any>;
    getToolStats(organizationId: string): Promise<{
        total: any;
        executions: any;
        byType: any;
        byStatus: any;
    }>;
    getPopularTools(organizationId: string, limit?: number): Promise<any>;
    searchToolsByTags(organizationId: string, tags: string[]): Promise<any>;
    getToolsByCategory(organizationId: string, category: string): Promise<any>;
    private validateToolConfig;
    incrementExecutionCount(toolId: string): Promise<void>;
}
