import { TemplatesService } from './templates.service';
import { CreateTemplateDto, UpdateTemplateDto, TemplateQueryDto, RateTemplateDto, ForkTemplateDto } from './dto/template.dto';
export declare class TemplatesController {
    private readonly templatesService;
    constructor(templatesService: TemplatesService);
    create(userId: string, organizationId: string, createTemplateDto: CreateTemplateDto): Promise<any>;
    findAll(organizationId: string, query: TemplateQueryDto): Promise<{
        templates: any;
        pagination: {
            page: number;
            limit: number;
            total: any;
            pages: number;
        };
    }>;
    getPopular(organizationId: string, limit?: number): Promise<any>;
    getStats(organizationId: string): Promise<{
        total: any;
        totalDownloads: any;
        byCategory: any;
        byVisibility: any;
    }>;
    getByCategory(category: string, organizationId: string): Promise<any>;
    search(organizationId: string, searchTerm: string, category?: string, minRating?: number): Promise<any>;
    findOne(id: string, organizationId: string): Promise<any>;
    update(id: string, organizationId: string, updateTemplateDto: UpdateTemplateDto): Promise<any>;
    remove(id: string, organizationId: string): Promise<{
        message: string;
    }>;
    fork(id: string, organizationId: string, forkDto: ForkTemplateDto): Promise<any>;
    rate(id: string, organizationId: string, userId: string, rateDto: RateTemplateDto): Promise<any>;
}
