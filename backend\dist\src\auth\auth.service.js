"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthService = void 0;
const common_1 = require("@nestjs/common");
const jwt_1 = require("@nestjs/jwt");
const bcrypt = require("bcryptjs");
const prisma_service_1 = require("../prisma/prisma.service");
let AuthService = class AuthService {
    constructor(prisma, jwtService) {
        this.prisma = prisma;
        this.jwtService = jwtService;
    }
    async validateUser(email, password) {
        const user = await this.prisma.user.findUnique({
            where: { email },
            include: {
                organization: true,
                userRoles: {
                    include: {
                        role: true,
                    },
                },
            },
        });
        if (!user || !user.passwordHash) {
            throw new common_1.UnauthorizedException('Invalid credentials');
        }
        const isPasswordValid = await bcrypt.compare(password, user.passwordHash);
        if (!isPasswordValid) {
            throw new common_1.UnauthorizedException('Invalid credentials');
        }
        const { passwordHash, ...userWithoutPassword } = user;
        return userWithoutPassword;
    }
    async login(loginDto) {
        const user = await this.validateUser(loginDto.email, loginDto.password);
        await this.prisma.user.update({
            where: { id: user.id },
            data: { lastLoginAt: new Date() },
        });
        const payload = {
            sub: user.id,
            email: user.email,
            organizationId: user.organizationId,
            roles: user.userRoles.map(ur => ur.role.name),
        };
        return {
            access_token: this.jwtService.sign(payload),
            user: {
                id: user.id,
                email: user.email,
                name: user.name,
                avatarUrl: user.avatarUrl,
                organizationId: user.organizationId,
                organization: user.organization,
                roles: user.userRoles.map(ur => ur.role),
                settings: user.settings,
            },
        };
    }
    async register(registerDto) {
        const existingUser = await this.prisma.user.findUnique({
            where: { email: registerDto.email },
        });
        if (existingUser) {
            throw new common_1.BadRequestException('User already exists');
        }
        const existingOrg = await this.prisma.organization.findUnique({
            where: { slug: registerDto.organizationSlug },
        });
        if (existingOrg) {
            throw new common_1.BadRequestException('Organization slug already taken');
        }
        const hashedPassword = await bcrypt.hash(registerDto.password, 12);
        const result = await this.prisma.$transaction(async (prisma) => {
            const organization = await prisma.organization.create({
                data: {
                    name: registerDto.organizationName,
                    slug: registerDto.organizationSlug,
                    settings: {
                        branding: { theme: 'light' },
                        features: ['agents', 'tools'],
                    },
                },
            });
            const adminRole = await prisma.role.create({
                data: {
                    name: 'Admin',
                    description: 'Organization administrator',
                    organizationId: organization.id,
                    permissions: [
                        'agents:create', 'agents:read', 'agents:update', 'agents:delete',
                        'tools:create', 'tools:read', 'tools:update', 'tools:delete',
                        'sessions:create', 'sessions:read', 'sessions:update', 'sessions:delete',
                        'analytics:read', 'billing:read', 'admin:manage',
                    ],
                },
            });
            const user = await prisma.user.create({
                data: {
                    email: registerDto.email,
                    name: registerDto.name,
                    passwordHash: hashedPassword,
                    organizationId: organization.id,
                    settings: {
                        theme: 'light',
                        notifications: {
                            email: true,
                            sms: false,
                            webhook: false,
                            push: true,
                        },
                    },
                },
            });
            await prisma.userRole.create({
                data: {
                    userId: user.id,
                    roleId: adminRole.id,
                },
            });
            await prisma.quota.createMany({
                data: [
                    {
                        organizationId: organization.id,
                        type: 'agents',
                        limit: 5,
                        resetAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
                    },
                    {
                        organizationId: organization.id,
                        type: 'tools',
                        limit: 10,
                        resetAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
                    },
                    {
                        organizationId: organization.id,
                        type: 'api_calls',
                        limit: 100,
                        resetAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
                    },
                ],
            });
            return { user, organization, role: adminRole };
        });
        const payload = {
            sub: result.user.id,
            email: result.user.email,
            organizationId: result.user.organizationId,
            roles: [result.role.name],
        };
        return {
            access_token: this.jwtService.sign(payload),
            user: {
                id: result.user.id,
                email: result.user.email,
                name: result.user.name,
                organizationId: result.user.organizationId,
                organization: result.organization,
                roles: [result.role],
                settings: result.user.settings,
            },
        };
    }
    async verifyToken(token) {
        try {
            const payload = this.jwtService.verify(token);
            const user = await this.prisma.user.findUnique({
                where: { id: payload.sub },
                include: {
                    organization: true,
                    userRoles: {
                        include: {
                            role: true,
                        },
                    },
                },
            });
            if (!user) {
                throw new common_1.UnauthorizedException('User not found');
            }
            const { passwordHash, ...userWithoutPassword } = user;
            return userWithoutPassword;
        }
        catch (error) {
            throw new common_1.UnauthorizedException('Invalid token');
        }
    }
};
exports.AuthService = AuthService;
exports.AuthService = AuthService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService, typeof (_a = typeof jwt_1.JwtService !== "undefined" && jwt_1.JwtService) === "function" ? _a : Object])
], AuthService);
//# sourceMappingURL=auth.service.js.map