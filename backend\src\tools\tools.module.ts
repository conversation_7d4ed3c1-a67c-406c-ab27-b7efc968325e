import { Modu<PERSON> } from '@nestjs/common';
import { ToolsController } from './tools.controller';
import { ToolsService } from './tools.service';
import { ToolExecutorService } from './tool-executor.service';
import { SafeExecutorService } from './safe-executor.service';
import { AuthModule } from '../auth/auth.module';
import { APIMXModule } from '../apix/apix.module';
import { SessionsModule } from '../sessions/sessions.module';

@Module({
  imports: [AuthModule, APIMXModule, SessionsModule],
  controllers: [ToolsController],
  providers: [ToolsService, ToolExecutorService, SafeExecutorService],
  exports: [ToolsService, ToolExecutorService, SafeExecutorService],
})
export class ToolsModule {}