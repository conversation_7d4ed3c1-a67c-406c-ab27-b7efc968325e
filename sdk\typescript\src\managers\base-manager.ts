import { SynapseAIClient } from '../client';

export abstract class BaseManager {
  protected client: SynapseAIClient;

  constructor(client: SynapseAIClient) {
    this.client = client;
  }

  /**
   * Get the client instance
   */
  protected getClient(): SynapseAIClient {
    return this.client;
  }

  /**
   * Make a request with the client
   */
  protected async request<T = any>(config: any): Promise<T> {
    return this.client.request(config);
  }

  /**
   * Get current organization ID
   */
  protected getOrganizationId(): string {
    return this.client.getConfig().organizationId;
  }

  /**
   * Check if debug mode is enabled
   */
  protected isDebugEnabled(): boolean {
    return this.client.getConfig().debug;
  }

  /**
   * Log debug information
   */
  protected debug(message: string, data?: any): void {
    if (this.isDebugEnabled()) {
      console.log(`[${this.constructor.name}] ${message}`, data);
    }
  }

  /**
   * Subscribe to events related to this manager
   */
  protected subscribe(subscription: any): string {
    return this.client.subscribe(subscription);
  }

  /**
   * Unsubscribe from events
   */
  protected unsubscribe(subscriptionId: string): void {
    this.client.unsubscribe(subscriptionId);
  }

  /**
   * Generate a unique ID
   */
  protected generateId(prefix = 'id'): string {
    return `${prefix}_${Date.now()}_${Math.random().toString(36).substring(2)}`;
  }

  /**
   * Validate required parameters
   */
  protected validateRequired(params: Record<string, any>, required: string[]): void {
    const missing = required.filter(key => params[key] == null);
    if (missing.length > 0) {
      throw new Error(`Missing required parameters: ${missing.join(', ')}`);
    }
  }

  /**
   * Format date for API requests
   */
  protected formatDate(date: Date): string {
    return date.toISOString();
  }

  /**
   * Parse API date response
   */
  protected parseDate(dateString: string): Date {
    return new Date(dateString);
  }

  /**
   * Build query parameters
   */
  protected buildQueryParams(params: Record<string, any>): Record<string, string> {
    const query: Record<string, string> = {};
    
    Object.entries(params).forEach(([key, value]) => {
      if (value != null) {
        if (Array.isArray(value)) {
          query[key] = value.join(',');
        } else {
          query[key] = String(value);
        }
      }
    });
    
    return query;
  }

  /**
   * Handle pagination
   */
  protected buildPaginationParams(page?: number, limit?: number): Record<string, number> {
    const params: Record<string, number> = {};
    
    if (page != null) params.page = page;
    if (limit != null) params.limit = limit;
    
    return params;
  }

  /**
   * Wait for a specific condition
   */
  protected async waitFor<T>(
    condition: () => Promise<T | null>,
    options: {
      timeout?: number;
      interval?: number;
      timeoutMessage?: string;
    } = {}
  ): Promise<T> {
    const { timeout = 30000, interval = 1000, timeoutMessage = 'Operation timed out' } = options;
    const startTime = Date.now();

    while (Date.now() - startTime < timeout) {
      const result = await condition();
      if (result != null) {
        return result;
      }
      await new Promise(resolve => setTimeout(resolve, interval));
    }

    throw new Error(timeoutMessage);
  }

  /**
   * Retry an operation with exponential backoff
   */
  protected async retry<T>(
    operation: () => Promise<T>,
    options: {
      maxAttempts?: number;
      baseDelay?: number;
      maxDelay?: number;
      shouldRetry?: (error: any) => boolean;
    } = {}
  ): Promise<T> {
    const {
      maxAttempts = 3,
      baseDelay = 1000,
      maxDelay = 10000,
      shouldRetry = () => true,
    } = options;

    let lastError: any;
    
    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error;
        
        if (attempt === maxAttempts || !shouldRetry(error)) {
          throw error;
        }
        
        const delay = Math.min(baseDelay * Math.pow(2, attempt - 1), maxDelay);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
    
    throw lastError;
  }
}