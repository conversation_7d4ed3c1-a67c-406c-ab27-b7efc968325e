{"scenarios": [{"id": "customer-service-basics", "title": "Customer Service Chatbot", "description": "Build a chatbot that handles common customer questions", "difficulty": "beginner", "estimatedTime": "10 minutes", "category": "customer-support", "objectives": ["Create a customer service agent", "Configure response style and tone", "Test with common customer questions", "Handle escalation to human support"], "steps": [{"id": "step-1", "title": "Choose Agent Te<PERSON>late", "instruction": "Select the 'Customer Service' template to get started quickly", "type": "selection", "options": [{"id": "customer-service", "name": "Customer Service Agent", "description": "Handles customer inquiries, complaints, and support requests", "recommended": true}, {"id": "sales-assistant", "name": "Sales Assistant", "description": "Helps with lead qualification and product information"}, {"id": "content-creator", "name": "Content Creator", "description": "Generates marketing content and social media posts"}], "expectedResult": "Customer service template selected", "tips": ["Templates save time by providing pre-configured settings", "You can always customize the template after selection", "Customer service is the most popular use case for beginners"]}, {"id": "step-2", "title": "Configure Agent Personality", "instruction": "Set how your agent should respond to customers", "type": "form", "fields": [{"name": "<PERSON><PERSON><PERSON>", "label": "Agent Name", "type": "text", "placeholder": "e.g., <PERSON>, Customer Support Bot", "defaultValue": "Customer Support Assistant"}, {"name": "responseStyle", "label": "Response Style", "type": "select", "options": [{"value": "friendly", "label": "Friendly & Warm"}, {"value": "professional", "label": "Professional & Formal"}, {"value": "casual", "label": "Casual & Relaxed"}], "defaultValue": "friendly"}, {"name": "companyInfo", "label": "Company Information", "type": "textarea", "placeholder": "Tell the agent about your company, products, and common policies...", "defaultValue": "We are TechCorp, a software company that sells productivity tools. Our main products are ProjectFlow (project management) and DataSync (analytics platform). We offer 24/7 support and have a 30-day money-back guarantee."}], "expectedResult": "Agent personality configured", "tips": ["Friendly tone works well for most customer service scenarios", "Include key company information so the agent can answer specific questions", "You can always update these settings later"]}, {"id": "step-3", "title": "Test Your Agent", "instruction": "Try asking your agent some common customer questions", "type": "chat-test", "suggestedQuestions": ["What are your business hours?", "I need help with my order", "How do I cancel my subscription?", "Your software isn't working properly", "Do you offer student discounts?"], "expectedBehaviors": ["Responds in the configured tone", "Asks follow-up questions when needed", "Offers to escalate complex issues", "Provides helpful information based on company details"], "tips": ["Try both simple and complex questions", "Notice how the agent handles requests it can't answer", "Pay attention to the response time and quality"]}, {"id": "step-4", "title": "Set Up Escalation Rules", "instruction": "Define when the agent should transfer to a human", "type": "configuration", "settings": [{"name": "escalationTriggers", "label": "Escalate When", "type": "checklist", "options": [{"value": "complaint", "label": "Customer expresses frustration or anger", "recommended": true}, {"value": "refund", "label": "Customer requests refund or cancellation", "recommended": true}, {"value": "technical", "label": "Complex technical issues", "recommended": true}, {"value": "billing", "label": "Billing or payment problems", "recommended": false}, {"value": "unknown", "label": "Agent doesn't understand the request", "recommended": true}]}, {"name": "escalationMessage", "label": "Escalation Message", "type": "textarea", "placeholder": "What should the agent say when escalating?", "defaultValue": "I understand this requires special attention. Let me connect you with one of our specialists who can better assist you. Please hold for just a moment."}], "expectedResult": "Escalation rules configured", "tips": ["Start with the recommended escalation triggers", "A smooth escalation message maintains customer confidence", "You can adjust these rules based on your support team's capacity"]}, {"id": "step-5", "title": "Deploy and Monitor", "instruction": "Launch your agent and see how it performs", "type": "deployment", "actions": [{"name": "deploy", "label": "Deploy Agent", "description": "Make your agent available to handle real customer inquiries"}, {"name": "getEmbedCode", "label": "Get Embed Code", "description": "Copy code to add the chat widget to your website"}, {"name": "setupMonitoring", "label": "Set Up Monitoring", "description": "Configure alerts and analytics to track performance"}], "monitoringMetrics": [{"name": "Response Time", "description": "Average time to respond to customer messages", "target": "< 2 seconds"}, {"name": "Resolution Rate", "description": "Percentage of issues resolved without escalation", "target": "> 70%"}, {"name": "Customer Satisfaction", "description": "Thumbs up/down ratings from customers", "target": "> 4.0/5.0"}], "tips": ["Start with a small group of customers for testing", "Monitor closely in the first few days", "Collect feedback and iterate on the agent's responses"]}], "completionCriteria": ["Agent successfully responds to test questions", "Escalation rules are properly configured", "Agent maintains consistent tone and personality", "Performance metrics are being tracked"], "nextSteps": [{"title": "Advanced Customer Service", "description": "Add order lookup, product recommendations, and multi-language support"}, {"title": "Integration with CRM", "description": "Connect your agent to Salesforce, HubSpot, or other customer management tools"}, {"title": "Analytics and Optimization", "description": "Use conversation data to improve your agent's performance"}]}, {"id": "lead-qualification-workflow", "title": "Lead Qualification Automation", "description": "Automatically score and route incoming leads to your sales team", "difficulty": "intermediate", "estimatedTime": "15 minutes", "category": "sales", "objectives": ["Create a lead scoring algorithm", "Build an automated qualification workflow", "Set up automatic lead routing", "Configure follow-up sequences"], "steps": [{"id": "step-1", "title": "Define Lead Scoring Criteria", "instruction": "Set up the factors that determine lead quality", "type": "scoring-setup", "scoringFactors": [{"category": "Company Information", "factors": [{"name": "Company Size", "weight": 20, "values": {"1-10": 10, "11-50": 30, "51-200": 50, "200+": 100}}, {"name": "Industry", "weight": 15, "values": {"technology": 80, "finance": 70, "healthcare": 60, "other": 30}}, {"name": "Revenue", "weight": 25, "values": {"<1M": 20, "1M-10M": 50, "10M-100M": 80, "100M+": 100}}]}, {"category": "Engagement Level", "factors": [{"name": "Website Activity", "weight": 15, "values": {"low": 20, "medium": 50, "high": 80}}, {"name": "Content Downloads", "weight": 10, "values": {"0": 0, "1-2": 30, "3-5": 60, "5+": 100}}, {"name": "Event Attendance", "weight": 15, "values": {"none": 0, "webinar": 40, "conference": 70, "demo": 100}}]}], "tips": ["Weight factors based on what predicts success for your business", "Company size and revenue are often strong predictors", "Engagement shows genuine interest in your solution"]}, {"id": "step-2", "title": "Build the Qualification Workflow", "instruction": "Create a visual workflow that processes incoming leads", "type": "workflow-builder", "workflowSteps": [{"id": "trigger", "type": "trigger", "name": "New Lead Received", "description": "Triggered when a lead fills out a form or downloads content"}, {"id": "enrich", "type": "tool", "name": "<PERSON>rich Lead Data", "description": "Use Clearbit or similar service to get company information"}, {"id": "score", "type": "agent", "name": "Calculate Lead Score", "description": "AI agent applies scoring criteria to determine lead quality"}, {"id": "route", "type": "condition", "name": "Route Based on Score", "description": "Direct leads to appropriate sales rep or nurture sequence"}, {"id": "notify", "type": "tool", "name": "Notify Sales Team", "description": "Send alert to assigned sales rep for high-quality leads"}], "tips": ["Data enrichment improves scoring accuracy", "Conditional routing ensures leads go to the right person", "Immediate notifications help sales teams respond quickly"]}, {"id": "step-3", "title": "Configure Lead Routing Rules", "instruction": "Set up how leads are assigned to your sales team", "type": "routing-configuration", "routingRules": [{"condition": "Score >= 80", "action": "Assign to Senior Sales Rep", "priority": "High", "notification": "Immediate Slack + Email"}, {"condition": "Score 60-79", "action": "Assign to Standard Sales Rep", "priority": "Medium", "notification": "Email within 1 hour"}, {"condition": "Score 40-59", "action": "Add to Nurture Campaign", "priority": "Low", "notification": "Daily digest email"}, {"condition": "Score < 40", "action": "Marketing Qualified Lead (MQL)", "priority": "Low", "notification": "Weekly report"}], "salesReps": [{"name": "<PERSON>", "level": "Senior", "capacity": 15, "specialties": ["Enterprise", "Technology"]}, {"name": "<PERSON>", "level": "Standard", "capacity": 25, "specialties": ["SMB", "Finance"]}, {"name": "<PERSON>", "level": "Standard", "capacity": 20, "specialties": ["Healthcare", "Government"]}], "tips": ["High-scoring leads should get immediate attention", "Balance workload across your sales team", "Consider rep specialties when routing leads"]}, {"id": "step-4", "title": "Test the Complete Workflow", "instruction": "Run sample leads through your qualification system", "type": "workflow-testing", "testScenarios": [{"name": "High-Value Enterprise Lead", "data": {"company": "TechCorp Inc", "size": "500 employees", "revenue": "$50M", "industry": "Technology", "engagement": "Downloaded 3 whitepapers, attended webinar"}, "expectedScore": 85, "expectedRoute": "Senior Sales Rep", "expectedAction": "Immediate notification"}, {"name": "Small Business Lead", "data": {"company": "Local Marketing Agency", "size": "8 employees", "revenue": "$500K", "industry": "Marketing", "engagement": "Visited pricing page"}, "expectedScore": 45, "expectedRoute": "Nurture Campaign", "expectedAction": "Add to email sequence"}], "tips": ["Test with realistic data from your target market", "Verify that scores match your expectations", "Check that notifications are sent to the right people"]}]}, {"id": "content-moderation-system", "title": "AI Content Moderation", "description": "Automatically review and moderate user-generated content", "difficulty": "intermediate", "estimatedTime": "20 minutes", "category": "content-management", "objectives": ["Set up automated content scanning", "Configure moderation rules and policies", "Build human review workflows", "Create reporting and analytics"]}], "templates": [{"id": "ecommerce-support", "name": "E-commerce Customer Support", "description": "Complete customer service solution for online stores", "includes": ["Order status lookup", "Return/refund processing", "Product recommendations", "Shipping inquiries"], "difficulty": "beginner", "industry": "retail"}, {"id": "saas-onboarding", "name": "SaaS User Onboarding", "description": "Guide new users through your software setup process", "includes": ["Account setup", "Feature tutorials", "Integration help", "Best practices"], "difficulty": "intermediate", "industry": "software"}, {"id": "real-estate-assistant", "name": "Real Estate Lead Assistant", "description": "Qualify and nurture real estate leads automatically", "includes": ["Property matching", "Appointment scheduling", "Market insights", "Document collection"], "difficulty": "intermediate", "industry": "real-estate"}], "tips": {"general": ["Start with simple scenarios before building complex workflows", "Test thoroughly with realistic data before going live", "Monitor performance and iterate based on user feedback", "Keep your AI instructions clear and specific"], "troubleshooting": ["If responses seem off-topic, review your agent instructions", "For slow responses, check your complexity settings", "If escalations aren't working, verify your trigger conditions", "For integration issues, test API connections independently"], "optimization": ["Use A/B testing to compare different approaches", "Analyze conversation logs to identify improvement opportunities", "Collect user feedback through ratings and surveys", "Regular training updates improve AI performance over time"]}, "faqs": [{"question": "How long does it take to set up my first AI agent?", "answer": "Most users can create and test their first agent in under 10 minutes using our templates. Custom configurations may take 15-30 minutes depending on complexity."}, {"question": "Can I test everything without affecting my live systems?", "answer": "Absolutely! Our sandbox environment lets you test all features safely. You can use sample data and simulated integrations before connecting to real systems."}, {"question": "What if my AI agent gives wrong answers?", "answer": "This is normal during initial setup. Use our training tools to provide correct examples, adjust instructions, and set up escalation rules for complex questions."}, {"question": "How do I know if my AI is performing well?", "answer": "We provide real-time metrics including response accuracy, user satisfaction ratings, escalation rates, and response times. Set up alerts for any issues."}, {"question": "Can I integrate with my existing tools?", "answer": "Yes! We support hundreds of integrations including CRM systems, help desks, email platforms, and custom APIs. Most popular tools have pre-built connectors."}]}