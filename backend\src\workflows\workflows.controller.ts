import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  Request,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { OrganizationGuard } from '../common/guards/organization.guard';
import { CurrentUser, CurrentOrganization } from '../common/decorators/current-user.decorator';
import { WorkflowsService } from './workflows.service';
import { WorkflowExecutorService } from './workflow-executor.service';
import {
  CreateWorkflowDto,
  UpdateWorkflowDto,
  ExecuteWorkflowDto,
  WorkflowQueryDto,
  WorkflowExecutionQueryDto,
  CloneWorkflowDto,
} from './dto/workflow.dto';

@Controller('workflows')
@UseGuards(JwtAuthGuard, OrganizationGuard)
export class WorkflowsController {
  constructor(
    private readonly workflowsService: WorkflowsService,
    private readonly workflowExecutorService: WorkflowExecutorService,
  ) {}

  @Post()
  create(
    @CurrentOrganization('id') organizationId: string,
    @CurrentUser('id') userId: string,
    @Body() createWorkflowDto: CreateWorkflowDto,
  ) {
    return this.workflowsService.create(organizationId, userId, createWorkflowDto);
  }

  @Get()
  findAll(
    @CurrentOrganization('id') organizationId: string,
    @Query() query: WorkflowQueryDto,
  ) {
    return this.workflowsService.findAll(organizationId, query);
  }

  @Get('stats')
  getStats(@CurrentOrganization('id') organizationId: string) {
    return this.workflowsService.getStats(organizationId);
  }

  @Get(':id')
  findOne(
    @Param('id') id: string,
    @CurrentOrganization('id') organizationId: string,
  ) {
    return this.workflowsService.findOne(id, organizationId);
  }

  @Patch(':id')
  update(
    @Param('id') id: string,
    @CurrentOrganization('id') organizationId: string,
    @CurrentUser('id') userId: string,
    @Body() updateWorkflowDto: UpdateWorkflowDto,
  ) {
    return this.workflowsService.update(id, organizationId, userId, updateWorkflowDto);
  }

  @Delete(':id')
  @HttpCode(HttpStatus.NO_CONTENT)
  remove(
    @Param('id') id: string,
    @CurrentOrganization('id') organizationId: string,
    @CurrentUser('id') userId: string,
  ) {
    return this.workflowsService.remove(id, organizationId, userId);
  }

  @Post(':id/clone')
  clone(
    @Param('id') id: string,
    @CurrentOrganization('id') organizationId: string,
    @CurrentUser('id') userId: string,
    @Body() cloneDto: CloneWorkflowDto,
  ) {
    return this.workflowsService.clone(id, organizationId, userId, cloneDto);
  }

  @Post(':id/execute')
  executeWorkflow(
    @Param('id') id: string,
    @CurrentOrganization('id') organizationId: string,
    @CurrentUser('id') userId: string,
    @Body() executeDto: ExecuteWorkflowDto,
  ) {
    return this.workflowExecutorService.executeWorkflow(id, organizationId, userId, executeDto);
  }

  @Get(':id/executions')
  getWorkflowExecutions(
    @Param('id') workflowId: string,
    @CurrentOrganization('id') organizationId: string,
    @Query() query: WorkflowExecutionQueryDto,
  ) {
    return this.workflowExecutorService.getExecutions(organizationId, {
      ...query,
      workflowId,
    });
  }

  @Get('executions/all')
  getAllExecutions(
    @CurrentOrganization('id') organizationId: string,
    @Query() query: WorkflowExecutionQueryDto,
  ) {
    return this.workflowExecutorService.getExecutions(organizationId, query);
  }

  @Get('executions/:executionId')
  getExecution(
    @Param('executionId') executionId: string,
    @CurrentOrganization('id') organizationId: string,
  ) {
    return this.workflowExecutorService.getExecution(executionId, organizationId);
  }

  @Post('executions/:executionId/cancel')
  @HttpCode(HttpStatus.OK)
  cancelExecution(
    @Param('executionId') executionId: string,
    @CurrentOrganization('id') organizationId: string,
    @CurrentUser('id') userId: string,
  ) {
    return this.workflowExecutorService.cancelExecution(executionId, organizationId, userId);
  }
}