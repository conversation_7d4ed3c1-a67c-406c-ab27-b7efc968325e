'use client';

import { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { useRouter } from 'next/navigation';
import toast from 'react-hot-toast';
import { authApi } from '@/lib/api/auth';

interface User {
  id: string;
  email: string;
  name: string;
  avatarUrl?: string;
  organizationId: string;
  organization: {
    id: string;
    name: string;
    slug: string;
    settings: any;
  };
  roles: Array<{
    id: string;
    name: string;
    permissions: string[];
  }>;
  settings: any;
}

interface AuthContextType {
  user: User | null;
  loading: boolean;
  login: (email: string, password: string) => Promise<void>;
  register: (data: RegisterData) => Promise<void>;
  logout: () => void;
  refreshToken: () => Promise<void>;
  hasPermission: (permission: string) => boolean;
  hasRole: (roleName: string) => boolean;
}

interface RegisterData {
  email: string;
  name: string;
  password: string;
  organizationName: string;
  organizationSlug: string;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const router = useRouter();

  useEffect(() => {
    checkAuth();
  }, []);

  const checkAuth = async () => {
    try {
      const token = localStorage.getItem('auth_token');
      if (!token) {
        setLoading(false);
        return;
      }

      const userData = await authApi.me();
      setUser(userData);
    } catch (error) {
      localStorage.removeItem('auth_token');
      console.error('Auth check failed:', error);
    } finally {
      setLoading(false);
    }
  };

  const login = async (email: string, password: string) => {
    try {
      const response = await authApi.login({ email, password });
      
      localStorage.setItem('auth_token', response.access_token);
      setUser(response.user);
      
      toast.success('Welcome back!');
      router.push('/dashboard');
    } catch (error: any) {
      const message = error?.response?.data?.message || 'Login failed';
      toast.error(message);
      throw error;
    }
  };

  const register = async (data: RegisterData) => {
    try {
      const response = await authApi.register(data);
      
      localStorage.setItem('auth_token', response.access_token);
      setUser(response.user);
      
      toast.success(`Welcome to SynapseAI, ${response.user.name}!`);
      router.push('/dashboard');
    } catch (error: any) {
      const message = error?.response?.data?.message || 'Registration failed';
      toast.error(message);
      throw error;
    }
  };

  const logout = () => {
    localStorage.removeItem('auth_token');
    setUser(null);
    router.push('/login');
    toast.success('Logged out successfully');
  };

  const refreshToken = async () => {
    try {
      const response = await authApi.refresh();
      localStorage.setItem('auth_token', response.access_token);
    } catch (error) {
      logout();
    }
  };

  const hasPermission = (permission: string): boolean => {
    if (!user?.roles) return false;
    
    return user.roles.some(role => 
      role.permissions.includes(permission) || 
      role.permissions.includes('*') ||
      role.name === 'Admin'
    );
  };

  const hasRole = (roleName: string): boolean => {
    if (!user?.roles) return false;
    return user.roles.some(role => role.name === roleName);
  };

  const value: AuthContextType = {
    user,
    loading,
    login,
    register,
    logout,
    refreshToken,
    hasPermission,
    hasRole,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}