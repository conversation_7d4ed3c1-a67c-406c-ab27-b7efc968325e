import { Injectable, NotFoundException, ForbiddenException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';

@Injectable()
export class OrganizationsService {
  constructor(private prisma: PrismaService) {}

  async findCurrent(organizationId: string) {
    const organization = await this.prisma.organization.findUnique({
      where: { id: organizationId },
    });

    if (!organization) {
      throw new NotFoundException('Organization not found');
    }

    return organization;
  }

  async updateSettings(organizationId: string, settings: any) {
    const organization = await this.findCurrent(organizationId);

    const updatedOrganization = await this.prisma.organization.update({
      where: { id: organizationId },
      data: {
        settings: {
          ...organization.settings,
          ...settings,
        },
        updatedAt: new Date(),
      },
    });

    return updatedOrganization;
  }

  async getQuotas(organizationId: string) {
    return this.prisma.quota.findMany({
      where: { organizationId },
      orderBy: { type: 'asc' },
    });
  }

  async updateQuota(organizationId: string, type: string, limit: number) {
    const quota = await this.prisma.quota.upsert({
      where: {
        organizationId_type: {
          organizationId,
          type,
        },
      },
      create: {
        organizationId,
        type,
        limit,
        used: 0,
        resetAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days
      },
      update: {
        limit,
        updatedAt: new Date(),
      },
    });

    return quota;
  }

  async getUsage(organizationId: string, type?: string, from?: Date, to?: Date) {
    const where: any = { organizationId };
    
    if (type) {
      where.type = type;
    }
    
    if (from || to) {
      where.timestamp = {};
      if (from) where.timestamp.gte = from;
      if (to) where.timestamp.lte = to;
    }

    return this.prisma.billingUsage.findMany({
      where,
      orderBy: { timestamp: 'desc' },
      take: 1000,
    });
  }
}