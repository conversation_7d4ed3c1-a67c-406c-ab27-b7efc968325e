import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  Request,
  HttpStatus,
  HttpCode,
} from '@nestjs/common';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { ToolsService } from './tools.service';
import { ToolExecutorService } from './tool-executor.service';
import {
  CreateToolDto,
  UpdateToolDto,
  ToolQueryDto,
  ExecuteToolDto,
  TestToolDto,
  ToolVersionDto,
} from './dto/tool.dto';

@Controller('tools')
@UseGuards(JwtAuthGuard)
export class ToolsController {
  constructor(
    private readonly toolsService: ToolsService,
    private readonly toolExecutorService: ToolExecutorService,
  ) {}

  @Post()
  @HttpCode(HttpStatus.CREATED)
  async create(@Request() req, @Body() createToolDto: CreateToolDto) {
    return this.toolsService.create(
      req.user.sub,
      req.user.organizationId,
      createToolDto,
    );
  }

  @Get()
  async findAll(@Request() req, @Query() query: ToolQueryDto) {
    return this.toolsService.findAll(req.user.organizationId, query);
  }

  @Get('stats')
  async getStats(@Request() req) {
    return this.toolsService.getToolStats(req.user.organizationId);
  }

  @Get('popular')
  async getPopular(@Request() req, @Query('limit') limit?: number) {
    return this.toolsService.getPopularTools(req.user.organizationId, limit);
  }

  @Get('search/tags')
  async searchByTags(@Request() req, @Query('tags') tags: string[]) {
    const tagArray = Array.isArray(tags) ? tags : [tags];
    return this.toolsService.searchToolsByTags(req.user.organizationId, tagArray);
  }

  @Get('category/:category')
  async getByCategory(@Request() req, @Param('category') category: string) {
    return this.toolsService.getToolsByCategory(req.user.organizationId, category);
  }

  @Get(':id')
  async findOne(@Request() req, @Param('id') id: string) {
    return this.toolsService.findOne(id, req.user.organizationId);
  }

  @Put(':id')
  async update(
    @Request() req,
    @Param('id') id: string,
    @Body() updateToolDto: UpdateToolDto,
  ) {
    return this.toolsService.update(id, req.user.organizationId, updateToolDto);
  }

  @Delete(':id')
  @HttpCode(HttpStatus.NO_CONTENT)
  async remove(@Request() req, @Param('id') id: string) {
    return this.toolsService.remove(id, req.user.organizationId);
  }

  @Post(':id/execute')
  async execute(
    @Request() req,
    @Param('id') id: string,
    @Body() executeDto: ExecuteToolDto,
  ) {
    return this.toolExecutorService.executeTool(
      id,
      req.user.organizationId,
      req.user.sub,
      executeDto,
    );
  }

  @Post(':id/test')
  async test(
    @Request() req,
    @Param('id') id: string,
    @Body() testDto: TestToolDto,
  ) {
    return this.toolExecutorService.testTool(
      id,
      req.user.organizationId,
      testDto,
    );
  }

  @Post(':id/version')
  async createVersion(
    @Request() req,
    @Param('id') id: string,
    @Body() versionDto: ToolVersionDto,
  ) {
    return this.toolsService.createVersion(id, req.user.organizationId, versionDto);
  }

  @Put(':id/version/:version/rollback')
  async rollbackToVersion(
    @Request() req,
    @Param('id') id: string,
    @Param('version') version: string,
  ) {
    return this.toolsService.rollbackToVersion(id, req.user.organizationId, version);
  }

  @Post(':id/clone')
  async clone(
    @Request() req,
    @Param('id') id: string,
    @Body() cloneData: { name: string; description?: string },
  ) {
    return this.toolsService.clone(id, req.user.organizationId, cloneData);
  }

  @Get(':id/execution/history')
  async getExecutionHistory(
    @Request() req,
    @Param('id') id: string,
    @Query('limit') limit?: number,
  ) {
    return this.toolExecutorService.getExecutionHistory(
      id,
      req.user.organizationId,
      limit,
    );
  }

  @Get(':id/execution/metrics')
  async getExecutionMetrics(@Request() req, @Param('id') id: string) {
    return this.toolExecutorService.getExecutionMetrics(
      id,
      req.user.organizationId,
    );
  }
}