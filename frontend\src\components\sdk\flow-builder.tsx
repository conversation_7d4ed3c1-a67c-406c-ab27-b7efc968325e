'use client';

import React, { useState, useCallback } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Plus, 
  Play, 
  Save, 
  Download, 
  Upload,
  MessageSquare,
  Cog,
  GitBranch,
  Users,
  Clock,
  CheckCircle,
  AlertTriangle,
  Zap,
  Eye,
  Settings,
  Trash2
} from 'lucide-react';

interface FlowNode {
  id: string;
  type: 'agent' | 'tool' | 'condition' | 'human-approval' | 'trigger';
  name: string;
  position: { x: number; y: number };
  config: any;
  status?: 'idle' | 'running' | 'completed' | 'error';
}

interface FlowConnection {
  id: string;
  sourceId: string;
  targetId: string;
  condition?: string;
}

export function SDKFlowBuilder() {
  const [nodes, setNodes] = useState<FlowNode[]>([
    {
      id: 'start',
      type: 'trigger',
      name: 'Webhook Trigger',
      position: { x: 100, y: 100 },
      config: { url: '/webhook/customer-inquiry' }
    }
  ]);
  
  const [connections, setConnections] = useState<FlowConnection[]>([]);
  const [selectedNode, setSelectedNode] = useState<string | null>(null);
  const [isRunning, setIsRunning] = useState(false);
  const [draggedNodeType, setDraggedNodeType] = useState<string | null>(null);

  const nodeTypes = [
    {
      type: 'agent',
      name: 'AI Agent',
      icon: <MessageSquare className="w-5 h-5" />,
      color: 'bg-blue-500',
      description: 'Execute an AI agent'
    },
    {
      type: 'tool',
      name: 'API Tool',
      icon: <Cog className="w-5 h-5" />,
      color: 'bg-green-500',
      description: 'Call external APIs'
    },
    {
      type: 'condition',
      name: 'Condition',
      icon: <GitBranch className="w-5 h-5" />,
      color: 'bg-yellow-500',
      description: 'Branch based on logic'
    },
    {
      type: 'human-approval',
      name: 'Human Approval',
      icon: <Users className="w-5 h-5" />,
      color: 'bg-purple-500',
      description: 'Require human review'
    }
  ];

  const templates = [
    {
      id: 'customer-service',
      name: 'Customer Service Flow',
      description: 'Auto-respond to customer inquiries with escalation',
      nodes: 4,
      category: 'Customer Support'
    },
    {
      id: 'lead-qualification',
      name: 'Lead Qualification',
      description: 'Score and route leads automatically',
      nodes: 6,
      category: 'Sales'
    },
    {
      id: 'document-processing',
      name: 'Document Processing',
      description: 'Extract data from documents and update systems',
      nodes: 5,
      category: 'Data Processing'
    }
  ];

  const addNode = useCallback((type: string, position: { x: number; y: number }) => {
    const newNode: FlowNode = {
      id: `node_${Date.now()}`,
      type: type as FlowNode['type'],
      name: `New ${type.charAt(0).toUpperCase() + type.slice(1)}`,
      position,
      config: {}
    };
    setNodes(prev => [...prev, newNode]);
  }, []);

  const runWorkflow = async () => {
    setIsRunning(true);
    
    // Simulate workflow execution
    for (let i = 0; i < nodes.length; i++) {
      await new Promise(resolve => setTimeout(resolve, 1000));
      setNodes(prev => prev.map(node => 
        node.id === nodes[i].id 
          ? { ...node, status: i === nodes.length - 1 ? 'completed' : 'running' }
          : node.status === 'running' ? { ...node, status: 'completed' } : node
      ));
    }
    
    setIsRunning(false);
  };

  const getNodeIcon = (type: string) => {
    const nodeType = nodeTypes.find(nt => nt.type === type);
    return nodeType?.icon || <Cog className="w-5 h-5" />;
  };

  const getNodeColor = (type: string) => {
    const nodeType = nodeTypes.find(nt => nt.type === type);
    return nodeType?.color || 'bg-gray-500';
  };

  const getStatusColor = (status?: string) => {
    switch (status) {
      case 'running': return 'border-blue-500 bg-blue-50';
      case 'completed': return 'border-green-500 bg-green-50';
      case 'error': return 'border-red-500 bg-red-50';
      default: return 'border-gray-300 bg-white';
    }
  };

  return (
    <div className="space-y-6">
      <Card className="bg-gradient-to-r from-purple-600 to-pink-600 text-white border-0">
        <CardContent className="p-8 text-center">
          <Zap className="w-12 h-12 mx-auto mb-4 opacity-90" />
          <h2 className="text-2xl font-bold mb-2">Visual Flow Builder</h2>
          <p className="text-purple-100 max-w-2xl mx-auto">
            Create complex AI workflows by dragging and dropping components. 
            No coding required - just visual connections.
          </p>
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Toolbar */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Components</CardTitle>
            <CardDescription>Drag components to build your workflow</CardDescription>
          </CardHeader>
          <CardContent className="space-y-3">
            {nodeTypes.map((nodeType) => (
              <div
                key={nodeType.type}
                draggable
                onDragStart={() => setDraggedNodeType(nodeType.type)}
                className="cursor-move"
              >
                <Card className="hover:shadow-md transition-all border-2 border-dashed border-gray-200 hover:border-blue-300">
                  <CardContent className="p-3">
                    <div className="flex items-center gap-3">
                      <div className={`${nodeType.color} text-white rounded p-2`}>
                        {nodeType.icon}
                      </div>
                      <div>
                        <h4 className="font-semibold text-sm">{nodeType.name}</h4>
                        <p className="text-xs text-gray-600">{nodeType.description}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            ))}

            <div className="pt-4 border-t">
              <h4 className="font-semibold text-sm mb-3">Quick Templates</h4>
              <div className="space-y-2">
                {templates.map((template) => (
                  <Card key={template.id} className="cursor-pointer hover:shadow-md transition-all">
                    <CardContent className="p-3">
                      <h5 className="font-semibold text-sm mb-1">{template.name}</h5>
                      <p className="text-xs text-gray-600 mb-2">{template.description}</p>
                      <div className="flex justify-between items-center">
                        <Badge variant="outline" className="text-xs">
                          {template.nodes} nodes
                        </Badge>
                        <Button size="sm" variant="outline" className="text-xs">
                          Use Template
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Canvas */}
        <div className="lg:col-span-2">
          <Card className="h-[600px]">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="text-lg">Workflow Canvas</CardTitle>
                <div className="flex gap-2">
                  <Button variant="outline" size="sm">
                    <Upload className="w-4 h-4 mr-1" />
                    Import
                  </Button>
                  <Button variant="outline" size="sm">
                    <Download className="w-4 h-4 mr-1" />
                    Export
                  </Button>
                  <Button variant="outline" size="sm">
                    <Save className="w-4 h-4 mr-1" />
                    Save
                  </Button>
                  <Button 
                    onClick={runWorkflow}
                    disabled={isRunning}
                    className="bg-green-600 hover:bg-green-700"
                    size="sm"
                  >
                    {isRunning ? (
                      <>
                        <Clock className="w-4 h-4 mr-1 animate-spin" />
                        Running...
                      </>
                    ) : (
                      <>
                        <Play className="w-4 h-4 mr-1" />
                        Run
                      </>
                    )}
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent className="p-0">
              <div 
                className="h-full bg-gray-50 relative overflow-hidden"
                onDrop={(e) => {
                  e.preventDefault();
                  if (draggedNodeType) {
                    const rect = e.currentTarget.getBoundingClientRect();
                    addNode(draggedNodeType, {
                      x: e.clientX - rect.left,
                      y: e.clientY - rect.top
                    });
                    setDraggedNodeType(null);
                  }
                }}
                onDragOver={(e) => e.preventDefault()}
              >
                {/* Grid Pattern */}
                <div 
                  className="absolute inset-0 opacity-20"
                  style={{
                    backgroundImage: 'radial-gradient(circle, #000 1px, transparent 1px)',
                    backgroundSize: '20px 20px'
                  }}
                />

                {/* Nodes */}
                {nodes.map((node) => (
                  <div
                    key={node.id}
                    className={`absolute cursor-pointer transition-all ${
                      selectedNode === node.id ? 'z-10' : ''
                    }`}
                    style={{
                      left: node.position.x,
                      top: node.position.y,
                      transform: selectedNode === node.id ? 'scale(1.05)' : 'scale(1)'
                    }}
                    onClick={() => setSelectedNode(node.id)}
                  >
                    <Card className={`w-48 ${getStatusColor(node.status)} border-2 shadow-lg`}>
                      <CardContent className="p-3">
                        <div className="flex items-center gap-3">
                          <div className={`${getNodeColor(node.type)} text-white rounded p-2`}>
                            {getNodeIcon(node.type)}
                          </div>
                          <div className="flex-1">
                            <h4 className="font-semibold text-sm">{node.name}</h4>
                            <p className="text-xs text-gray-600">{node.type}</p>
                          </div>
                          {node.status && (
                            <div className="flex-shrink-0">
                              {node.status === 'running' && <Clock className="w-4 h-4 text-blue-600 animate-spin" />}
                              {node.status === 'completed' && <CheckCircle className="w-4 h-4 text-green-600" />}
                              {node.status === 'error' && <AlertTriangle className="w-4 h-4 text-red-600" />}
                            </div>
                          )}
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                ))}

                {/* Empty State */}
                {nodes.length === 1 && (
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="text-center text-gray-500">
                      <Zap className="w-12 h-12 mx-auto mb-4 opacity-50" />
                      <h3 className="text-lg font-semibold mb-2">Start Building Your Workflow</h3>
                      <p className="text-sm mb-4">Drag components from the left panel to create your flow</p>
                      <Button variant="outline">
                        <Plus className="w-4 h-4 mr-2" />
                        Add First Component
                      </Button>
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Properties Panel */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Properties</CardTitle>
            <CardDescription>
              {selectedNode ? 'Configure the selected component' : 'Select a component to edit'}
            </CardDescription>
          </CardHeader>
          <CardContent>
            {selectedNode ? (
              <div className="space-y-4">
                {(() => {
                  const node = nodes.find(n => n.id === selectedNode);
                  if (!node) return null;

                  return (
                    <>
                      <div>
                        <label className="block text-sm font-medium mb-2">Name</label>
                        <input 
                          type="text" 
                          className="w-full p-2 border rounded-lg text-sm"
                          value={node.name}
                          onChange={(e) => {
                            setNodes(prev => prev.map(n => 
                              n.id === selectedNode ? { ...n, name: e.target.value } : n
                            ));
                          }}
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium mb-2">Type</label>
                        <div className="flex items-center gap-2">
                          <div className={`${getNodeColor(node.type)} text-white rounded p-1`}>
                            {getNodeIcon(node.type)}
                          </div>
                          <span className="text-sm font-medium">{node.type}</span>
                        </div>
                      </div>

                      {node.type === 'agent' && (
                        <div className="space-y-3">
                          <div>
                            <label className="block text-sm font-medium mb-2">Agent Model</label>
                            <select className="w-full p-2 border rounded-lg text-sm">
                              <option>GPT-4</option>
                              <option>GPT-3.5</option>
                              <option>Claude</option>
                            </select>
                          </div>
                          <div>
                            <label className="block text-sm font-medium mb-2">Instructions</label>
                            <textarea 
                              className="w-full p-2 border rounded-lg text-sm resize-none"
                              rows={3}
                              placeholder="Tell the agent what to do..."
                            />
                          </div>
                        </div>
                      )}

                      {node.type === 'tool' && (
                        <div className="space-y-3">
                          <div>
                            <label className="block text-sm font-medium mb-2">API Endpoint</label>
                            <input 
                              type="url" 
                              className="w-full p-2 border rounded-lg text-sm"
                              placeholder="https://api.example.com/endpoint"
                            />
                          </div>
                          <div>
                            <label className="block text-sm font-medium mb-2">Method</label>
                            <select className="w-full p-2 border rounded-lg text-sm">
                              <option>POST</option>
                              <option>GET</option>
                              <option>PUT</option>
                              <option>DELETE</option>
                            </select>
                          </div>
                        </div>
                      )}

                      {node.type === 'condition' && (
                        <div className="space-y-3">
                          <div>
                            <label className="block text-sm font-medium mb-2">Condition Logic</label>
                            <textarea 
                              className="w-full p-2 border rounded-lg text-sm resize-none"
                              rows={3}
                              placeholder="if (response.confidence > 0.8) { ... }"
                            />
                          </div>
                        </div>
                      )}

                      <div className="flex gap-2 pt-4">
                        <Button variant="outline" size="sm" className="flex-1">
                          <Eye className="w-4 h-4 mr-1" />
                          Test
                        </Button>
                        <Button variant="outline" size="sm">
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </div>
                    </>
                  );
                })()}
              </div>
            ) : (
              <div className="text-center text-gray-500 py-8">
                <Settings className="w-8 h-8 mx-auto mb-3 opacity-50" />
                <p className="text-sm">Select a component to view its properties</p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Workflow Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="bg-blue-100 text-blue-600 rounded-lg p-2">
                <Zap className="w-5 h-5" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Total Nodes</p>
                <p className="text-xl font-semibold">{nodes.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="bg-green-100 text-green-600 rounded-lg p-2">
                <CheckCircle className="w-5 h-5" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Connections</p>
                <p className="text-xl font-semibold">{connections.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="bg-yellow-100 text-yellow-600 rounded-lg p-2">
                <Clock className="w-5 h-5" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Est. Runtime</p>
                <p className="text-xl font-semibold">~2.5s</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="bg-purple-100 text-purple-600 rounded-lg p-2">
                <Users className="w-5 h-5" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Human Steps</p>
                <p className="text-xl font-semibold">{nodes.filter(n => n.type === 'human-approval').length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}