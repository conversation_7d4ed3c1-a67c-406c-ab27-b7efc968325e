'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  PlayCircle, 
  BookOpen, 
  Code, 
  Lightbulb, 
  CheckCircle, 
  ArrowRight,
  Zap,
  Users,
  Cog,
  Database
} from 'lucide-react';
import { SDKTutorialGuide } from '@/components/sdk/tutorial-guide';
import { SDKTestingEnvironment } from '@/components/sdk/testing-environment';
import { SDKFlowBuilder } from '@/components/sdk/flow-builder';
import { SDKDocumentation } from '@/components/sdk/documentation';

export default function SDKPlaygroundPage() {
  const [activeTab, setActiveTab] = useState('tutorial');
  const [completedSteps, setCompletedSteps] = useState<string[]>([]);

  const features = [
    {
      icon: <BookOpen className="w-6 h-6" />,
      title: "Interactive Tutorials",
      description: "Step-by-step guides for non-technical users",
      color: "bg-blue-500"
    },
    {
      icon: <PlayCircle className="w-6 h-6" />,
      title: "Visual Testing",
      description: "Click-based testing without coding",
      color: "bg-green-500"
    },
    {
      icon: <Cog className="w-6 h-6" />,
      title: "Flow Builder",
      description: "Drag & drop workflow creation",
      color: "bg-purple-500"
    },
    {
      icon: <Code className="w-6 h-6" />,
      title: "Live Examples",
      description: "Real-time SDK demonstrations",
      color: "bg-orange-500"
    }
  ];

  const quickStart = [
    {
      step: 1,
      title: "Choose Your Path",
      description: "Select tutorials based on your role and goals",
      icon: <Users className="w-5 h-5" />,
      completed: completedSteps.includes('step-1')
    },
    {
      step: 2,
      title: "Follow Visual Guides",
      description: "Interactive tutorials with real examples",
      icon: <BookOpen className="w-5 h-5" />,
      completed: completedSteps.includes('step-2')
    },
    {
      step: 3,
      title: "Test in Browser",
      description: "Try SDK features without any setup",
      icon: <PlayCircle className="w-5 h-5" />,
      completed: completedSteps.includes('step-3')
    },
    {
      step: 4,
      title: "Build Workflows",
      description: "Create complex automations visually",
      icon: <Zap className="w-5 h-5" />,
      completed: completedSteps.includes('step-4')
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      {/* Hero Section */}
      <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white">
        <div className="container mx-auto px-4 py-16">
          <div className="max-w-4xl mx-auto text-center">
            <div className="inline-flex items-center gap-2 bg-white/20 backdrop-blur-sm rounded-full px-4 py-2 mb-6">
              <Lightbulb className="w-4 h-4" />
              <span className="text-sm font-medium">No Coding Required</span>
            </div>
            
            <h1 className="text-4xl md:text-6xl font-bold mb-6 bg-gradient-to-r from-white to-blue-100 bg-clip-text text-transparent">
              SynapseAI SDK Playground
            </h1>
            
            <p className="text-xl md:text-2xl mb-8 text-blue-100 max-w-3xl mx-auto">
              Learn, test, and build with our AI platform through interactive tutorials 
              and visual tools designed for everyone
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button 
                size="lg" 
                className="bg-white text-blue-600 hover:bg-blue-50"
                onClick={() => setActiveTab('tutorial')}
              >
                <PlayCircle className="w-5 h-5 mr-2" />
                Start Learning
              </Button>
              <Button 
                size="lg" 
                variant="outline" 
                className="border-white text-white hover:bg-white/10"
                onClick={() => setActiveTab('testing')}
              >
                <Code className="w-5 h-5 mr-2" />
                Try SDK Now
              </Button>
            </div>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-8">
        {/* Features Overview */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
          {features.map((feature, index) => (
            <Card key={index} className="group hover:shadow-lg transition-all duration-300 hover:-translate-y-1">
              <CardContent className="p-6">
                <div className={`${feature.color} text-white rounded-lg p-3 w-fit mb-4 group-hover:scale-110 transition-transform`}>
                  {feature.icon}
                </div>
                <h3 className="font-semibold text-lg mb-2">{feature.title}</h3>
                <p className="text-gray-600 text-sm">{feature.description}</p>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Quick Start Guide */}
        <Card className="mb-8 bg-gradient-to-r from-green-50 to-blue-50 border-0">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CheckCircle className="w-6 h-6 text-green-600" />
              Quick Start Guide
            </CardTitle>
            <CardDescription>
              Get started in 4 simple steps - no technical knowledge required
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {quickStart.map((step, index) => (
                <div key={index} className="flex items-start gap-3">
                  <div className={`rounded-full p-2 ${step.completed ? 'bg-green-500 text-white' : 'bg-gray-200 text-gray-600'}`}>
                    {step.completed ? <CheckCircle className="w-4 h-4" /> : step.icon}
                  </div>
                  <div>
                    <h4 className="font-medium text-sm mb-1">Step {step.step}: {step.title}</h4>
                    <p className="text-xs text-gray-600">{step.description}</p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Main Tabs Interface */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-4 mb-8">
            <TabsTrigger value="tutorial" className="flex items-center gap-2">
              <BookOpen className="w-4 h-4" />
              <span className="hidden sm:inline">Tutorials</span>
            </TabsTrigger>
            <TabsTrigger value="testing" className="flex items-center gap-2">
              <PlayCircle className="w-4 h-4" />
              <span className="hidden sm:inline">Testing</span>
            </TabsTrigger>
            <TabsTrigger value="builder" className="flex items-center gap-2">
              <Cog className="w-4 h-4" />
              <span className="hidden sm:inline">Flow Builder</span>
            </TabsTrigger>
            <TabsTrigger value="docs" className="flex items-center gap-2">
              <Database className="w-4 h-4" />
              <span className="hidden sm:inline">Documentation</span>
            </TabsTrigger>
          </TabsList>

          <TabsContent value="tutorial" className="space-y-6">
            <SDKTutorialGuide 
              onStepComplete={(stepId) => setCompletedSteps(prev => [...prev, stepId])}
              completedSteps={completedSteps}
            />
          </TabsContent>

          <TabsContent value="testing" className="space-y-6">
            <SDKTestingEnvironment />
          </TabsContent>

          <TabsContent value="builder" className="space-y-6">
            <SDKFlowBuilder />
          </TabsContent>

          <TabsContent value="docs" className="space-y-6">
            <SDKDocumentation />
          </TabsContent>
        </Tabs>

        {/* Call to Action */}
        <Card className="mt-12 bg-gradient-to-r from-purple-600 to-blue-600 text-white border-0">
          <CardContent className="p-8 text-center">
            <h3 className="text-2xl font-bold mb-4">Ready to Build Something Amazing?</h3>
            <p className="text-purple-100 mb-6 max-w-2xl mx-auto">
              Take your learning to the next level. Integrate SynapseAI into your projects 
              and unlock the power of AI automation.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" className="bg-white text-purple-600 hover:bg-gray-100">
                <ArrowRight className="w-5 h-5 mr-2" />
                Get API Key
              </Button>
              <Button size="lg" variant="outline" className="border-white text-white hover:bg-white/10">
                View Full Documentation
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}