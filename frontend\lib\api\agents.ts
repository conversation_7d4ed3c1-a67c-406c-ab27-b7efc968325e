import { apiClient } from './client';

export interface Agent {
  id: string;
  name: string;
  description: string;
  organizationId: string;
  templateId?: string;
  config: AgentConfig;
  status: 'draft' | 'active' | 'archived';
  metadata: any;
  createdAt: string;
  updatedAt: string;
  template?: {
    id: string;
    name: string;
    category: string;
  };
}

export interface AgentConfig {
  prompt: string;
  model: string;
  temperature: number;
  maxTokens: number;
  systemPrompt?: string;
  tools?: string[];
  memory?: {
    enabled: boolean;
    maxMessages: number;
    strategy: 'rolling' | 'summarize' | 'vector';
  };
  parameters?: Record<string, any>;
}

export interface CreateAgentRequest {
  name: string;
  description: string;
  templateId?: string;
  config: AgentConfig;
  status?: 'draft' | 'active' | 'archived';
  tags?: string[];
}

export interface UpdateAgentRequest {
  name?: string;
  description?: string;
  config?: Partial<AgentConfig>;
  status?: 'draft' | 'active' | 'archived';
  tags?: string[];
}

export interface ExecuteAgentRequest {
  input: string;
  sessionId?: string;
  context?: Record<string, any>;
  streaming?: boolean;
  overrides?: {
    temperature?: number;
    maxTokens?: number;
    model?: string;
  };
}

export interface ExecutionResult {
  success: boolean;
  output: string;
  usage?: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  };
  executionTime: number;
  sessionId: string;
  error?: string;
}

export interface AgentQueryParams {
  page?: number;
  limit?: number;
  status?: 'draft' | 'active' | 'archived';
  search?: string;
  model?: string;
  tags?: string[];
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface AgentStats {
  total: number;
  executions: number;
  byStatus: Record<string, number>;
  byModel: Record<string, number>;
}

export interface ExecutionMetrics {
  totalExecutions: number;
  avgTokensPerExecution: number;
  totalTokensUsed: number;
  lastExecutedAt?: string;
}

export const agentsApi = {
  async getAll(params?: AgentQueryParams): Promise<{ agents: Agent[]; pagination: any }> {
    const queryParams = new URLSearchParams();
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) {
          if (Array.isArray(value)) {
            value.forEach(v => queryParams.append(key, v));
          } else {
            queryParams.set(key, value.toString());
          }
        }
      });
    }
    return apiClient.get(`/api/v1/agents?${queryParams.toString()}`);
  },

  async getById(id: string): Promise<Agent> {
    return apiClient.get(`/api/v1/agents/${id}`);
  },

  async create(data: CreateAgentRequest): Promise<Agent> {
    return apiClient.post('/api/v1/agents', data);
  },

  async update(id: string, data: UpdateAgentRequest): Promise<Agent> {
    return apiClient.patch(`/api/v1/agents/${id}`, data);
  },

  async delete(id: string): Promise<{ message: string }> {
    return apiClient.delete(`/api/v1/agents/${id}`);
  },

  async execute(id: string, data: ExecuteAgentRequest): Promise<ExecutionResult> {
    return apiClient.post(`/api/v1/agents/${id}/execute`, data);
  },

  async executeStreaming(
    id: string,
    data: ExecuteAgentRequest,
    onChunk: (chunk: any) => void,
  ): Promise<void> {
    const response = await fetch(`${process.env.NEXT_PUBLIC_BACKEND_URL || 'http://localhost:3001'}/api/v1/agents/${id}/execute`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
      },
      body: JSON.stringify({ ...data, streaming: true }),
    });

    if (!response.ok) {
      throw new Error('Execution failed');
    }

    const reader = response.body?.getReader();
    if (!reader) return;

    const decoder = new TextDecoder();

    try {
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        const chunk = decoder.decode(value);
        const lines = chunk.split('\n');

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const data = JSON.parse(line.slice(6));
              onChunk(data);
            } catch (e) {
              // Ignore parsing errors
            }
          }
        }
      }
    } finally {
      reader.releaseLock();
    }
  },

  async test(id: string, data: { input: string; context?: any }): Promise<ExecutionResult> {
    return apiClient.post(`/api/v1/agents/${id}/test`, data);
  },

  async getStats(): Promise<AgentStats> {
    return apiClient.get('/api/v1/agents/stats');
  },

  async getPopularModels(): Promise<Array<{ model: string; count: number }>> {
    return apiClient.get('/api/v1/agents/models/popular');
  },

  async searchByTags(tags: string[]): Promise<Agent[]> {
    const queryParams = new URLSearchParams();
    tags.forEach(tag => queryParams.append('tags', tag));
    return apiClient.get(`/api/v1/agents/search/tags?${queryParams.toString()}`);
  },

  async getExecutionHistory(id: string, limit?: number): Promise<any[]> {
    const params = limit ? `?limit=${limit}` : '';
    return apiClient.get(`/api/v1/agents/${id}/executions${params}`);
  },

  async getExecutionMetrics(id: string): Promise<ExecutionMetrics> {
    return apiClient.get(`/api/v1/agents/${id}/metrics`);
  },

  async createVersion(id: string, data: {
    version: string;
    description: string;
    config: AgentConfig;
    metadata?: Record<string, any>;
  }): Promise<Agent> {
    return apiClient.post(`/api/v1/agents/${id}/versions`, data);
  },

  async rollbackToVersion(id: string, version: string): Promise<Agent> {
    return apiClient.post(`/api/v1/agents/${id}/rollback/${version}`);
  },

  async clone(id: string, data: {
    name: string;
    description?: string;
    includeVersionHistory?: boolean;
  }): Promise<Agent> {
    return apiClient.post(`/api/v1/agents/${id}/clone`, data);
  },
};