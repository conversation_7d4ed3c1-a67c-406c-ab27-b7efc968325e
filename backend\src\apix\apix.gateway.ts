import {
  WebSocketGateway,
  WebSocketServer,
  SubscribeMessage,
  OnGatewayConnection,
  OnGatewayDisconnect,
  ConnectedSocket,
  MessageBody,
} from '@nestjs/websockets';
import { Server, Socket } from 'socket.io';
import { Injectable, Logger, UseGuards } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { APIMXEvent } from './apix.service';

interface AuthenticatedSocket extends Socket {
  userId?: string;
  organizationId?: string;
  userRoles?: string[];
}

@Injectable()
@WebSocketGateway({
  cors: {
    origin: process.env.FRONTEND_URL || 'http://localhost:3000',
    credentials: true,
  },
  namespace: '/apix',
})
export class APIMXGateway implements OnGatewayConnection, OnGatewayDisconnect {
  @WebSocketServer()
  server: Server;

  private readonly logger = new Logger(APIMXGateway.name);
  private connectedClients = new Map<string, AuthenticatedSocket>();
  private userSockets = new Map<string, Set<string>>();
  private organizationSockets = new Map<string, Set<string>>();
  private sessionSockets = new Map<string, Set<string>>();

  constructor(private jwtService: JwtService) {}

  async handleConnection(client: AuthenticatedSocket) {
    try {
      const token = client.handshake.auth.token || client.handshake.headers.authorization?.replace('Bearer ', '');
      
      if (!token) {
        client.disconnect();
        return;
      }

      const payload = this.jwtService.verify(token);
      client.userId = payload.sub;
      client.organizationId = payload.organizationId;
      client.userRoles = payload.roles;

      this.connectedClients.set(client.id, client);

      if (!this.userSockets.has(client.userId)) {
        this.userSockets.set(client.userId, new Set());
      }
      this.userSockets.get(client.userId).add(client.id);

      if (!this.organizationSockets.has(client.organizationId)) {
        this.organizationSockets.set(client.organizationId, new Set());
      }
      this.organizationSockets.get(client.organizationId).add(client.id);

      await client.join(`org:${client.organizationId}`);
      await client.join(`user:${client.userId}`);

      this.logger.log(`Client connected: ${client.id} (User: ${client.userId}, Org: ${client.organizationId})`);

      client.emit('connected', {
        clientId: client.id,
        userId: client.userId,
        organizationId: client.organizationId,
        timestamp: new Date(),
      });

    } catch (error) {
      this.logger.error(`Authentication failed for client ${client.id}:`, error);
      client.disconnect();
    }
  }

  async handleDisconnect(client: AuthenticatedSocket) {
    this.connectedClients.delete(client.id);

    if (client.userId && this.userSockets.has(client.userId)) {
      this.userSockets.get(client.userId).delete(client.id);
      if (this.userSockets.get(client.userId).size === 0) {
        this.userSockets.delete(client.userId);
      }
    }

    if (client.organizationId && this.organizationSockets.has(client.organizationId)) {
      this.organizationSockets.get(client.organizationId).delete(client.id);
      if (this.organizationSockets.get(client.organizationId).size === 0) {
        this.organizationSockets.delete(client.organizationId);
      }
    }

    this.logger.log(`Client disconnected: ${client.id}`);
  }

  @SubscribeMessage('join_session')
  async handleJoinSession(
    @ConnectedSocket() client: AuthenticatedSocket,
    @MessageBody() data: { sessionId: string },
  ) {
    const { sessionId } = data;
    
    if (!this.sessionSockets.has(sessionId)) {
      this.sessionSockets.set(sessionId, new Set());
    }
    this.sessionSockets.get(sessionId).add(client.id);

    await client.join(`session:${sessionId}`);
    
    client.emit('session_joined', { sessionId, timestamp: new Date() });
    this.logger.log(`Client ${client.id} joined session: ${sessionId}`);
  }

  @SubscribeMessage('leave_session')
  async handleLeaveSession(
    @ConnectedSocket() client: AuthenticatedSocket,
    @MessageBody() data: { sessionId: string },
  ) {
    const { sessionId } = data;
    
    if (this.sessionSockets.has(sessionId)) {
      this.sessionSockets.get(sessionId).delete(client.id);
      if (this.sessionSockets.get(sessionId).size === 0) {
        this.sessionSockets.delete(sessionId);
      }
    }

    await client.leave(`session:${sessionId}`);
    
    client.emit('session_left', { sessionId, timestamp: new Date() });
    this.logger.log(`Client ${client.id} left session: ${sessionId}`);
  }

  @SubscribeMessage('ping')
  handlePing(@ConnectedSocket() client: AuthenticatedSocket) {
    client.emit('pong', { timestamp: new Date() });
  }

  broadcastToOrganization(organizationId: string, event: APIMXEvent) {
    this.server.to(`org:${organizationId}`).emit('apix_event', event);
    this.logger.debug(`Broadcasted ${event.type} to organization: ${organizationId}`);
  }

  broadcastToUser(userId: string, event: APIMXEvent) {
    this.server.to(`user:${userId}`).emit('apix_event', event);
    this.logger.debug(`Broadcasted ${event.type} to user: ${userId}`);
  }

  broadcastToSession(sessionId: string, event: APIMXEvent) {
    this.server.to(`session:${sessionId}`).emit('apix_event', event);
    this.logger.debug(`Broadcasted ${event.type} to session: ${sessionId}`);
  }

  broadcastToAll(event: APIMXEvent) {
    this.server.emit('apix_event', event);
    this.logger.debug(`Broadcasted ${event.type} to all clients`);
  }

  getConnectedClientsCount(): number {
    return this.connectedClients.size;
  }

  getOrganizationClientsCount(organizationId: string): number {
    return this.organizationSockets.get(organizationId)?.size || 0;
  }

  getUserClientsCount(userId: string): number {
    return this.userSockets.get(userId)?.size || 0;
  }

  isUserOnline(userId: string): boolean {
    return this.userSockets.has(userId) && this.userSockets.get(userId).size > 0;
  }
}