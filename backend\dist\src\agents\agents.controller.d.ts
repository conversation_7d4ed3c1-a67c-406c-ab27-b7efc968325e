import { Response } from 'express';
import { AgentsService } from './agents.service';
import { AgentExecutorService } from './agent-executor.service';
import { CreateAgentDto, UpdateAgentDto, AgentQueryDto, ExecuteAgentDto, AgentVersionDto, CloneAgentDto } from './dto/agent.dto';
export declare class AgentsController {
    private readonly agentsService;
    private readonly agentExecutorService;
    constructor(agentsService: AgentsService, agentExecutorService: AgentExecutorService);
    create(userId: string, organizationId: string, createAgentDto: CreateAgentDto): Promise<any>;
    findAll(organizationId: string, query: AgentQueryDto): Promise<{
        agents: any;
        pagination: {
            page: number;
            limit: number;
            total: any;
            pages: number;
        };
    }>;
    getStats(organizationId: string): Promise<{
        total: any;
        executions: any;
        byStatus: any;
        byModel: {};
    }>;
    getPopularModels(organizationId: string): Promise<{
        model: string;
        count: unknown;
    }[]>;
    searchByTags(organizationId: string, tags: string[]): Promise<any>;
    findOne(id: string, organizationId: string): Promise<any>;
    update(id: string, organizationId: string, updateAgentDto: UpdateAgentDto): Promise<any>;
    remove(id: string, organizationId: string): Promise<{
        message: string;
    }>;
    executeAgent(id: string, userId: string, organizationId: string, executeDto: ExecuteAgentDto, res: Response): Promise<void>;
    createVersion(id: string, organizationId: string, versionDto: AgentVersionDto): Promise<any>;
    rollbackToVersion(id: string, version: string, organizationId: string): Promise<any>;
    clone(id: string, organizationId: string, cloneDto: CloneAgentDto): Promise<any>;
    getExecutionHistory(id: string, organizationId: string, limit?: number): Promise<any>;
    getExecutionMetrics(id: string, organizationId: string): Promise<{
        totalExecutions: any;
        avgTokensPerExecution: any;
        totalTokensUsed: any;
        lastExecutedAt: any;
    }>;
    testAgent(id: string, userId: string, organizationId: string, testDto: {
        input: string;
        context?: any;
    }): Promise<import("./agent-executor.service").ExecutionResult>;
}
