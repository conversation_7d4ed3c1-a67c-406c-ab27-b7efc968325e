import { PaginatedResponse, QueryParams } from '../types';
import { BaseManager } from './base-manager';

export interface BillingUsage {
  id: string;
  organizationId: string;
  type: string;
  quantity: number;
  cost: number;
  metadata: Record<string, any>;
  createdAt: string;
}

export interface Quota {
  id: string;
  organizationId: string;
  type: string;
  limit: number;
  used: number;
  resetAt: string;
  createdAt: string;
  updatedAt: string;
}

export interface BillingQueryParams extends QueryParams {
  type?: string;
  startDate?: string;
  endDate?: string;
  minCost?: number;
  maxCost?: number;
}

export interface UsageSummary {
  totalCost: number;
  totalUsage: number;
  byType: Record<string, {
    cost: number;
    quantity: number;
    percentage: number;
  }>;
  byDay: Array<{
    date: string;
    cost: number;
    quantity: number;
  }>;
  topConsumers: Array<{
    type: string;
    cost: number;
    quantity: number;
  }>;
}

export interface Invoice {
  id: string;
  organizationId: string;
  amount: number;
  currency: string;
  status: 'draft' | 'pending' | 'paid' | 'failed' | 'cancelled';
  billingPeriod: {
    start: string;
    end: string;
  };
  lineItems: Array<{
    description: string;
    quantity: number;
    unitPrice: number;
    amount: number;
    metadata: Record<string, any>;
  }>;
  dueDate: string;
  paidAt?: string;
  createdAt: string;
  updatedAt: string;
}

export interface PaymentMethod {
  id: string;
  type: 'card' | 'bank_account' | 'paypal';
  last4?: string;
  brand?: string;
  expiryMonth?: number;
  expiryYear?: number;
  isDefault: boolean;
  metadata: Record<string, any>;
  createdAt: string;
}

export interface Subscription {
  id: string;
  organizationId: string;
  planId: string;
  status: 'active' | 'cancelled' | 'past_due' | 'unpaid';
  currentPeriodStart: string;
  currentPeriodEnd: string;
  cancelAtPeriodEnd: boolean;
  metadata: Record<string, any>;
  createdAt: string;
  updatedAt: string;
}

export class BillingManager extends BaseManager {
  /**
   * Get billing usage
   */
  async getUsage(params?: BillingQueryParams): Promise<PaginatedResponse<BillingUsage>> {
    return this.request({
      method: 'GET',
      url: '/billing/usage',
      params: this.buildQueryParams(params || {}),
    });
  }

  /**
   * Get usage summary
   */
  async getUsageSummary(options?: {
    startDate?: string;
    endDate?: string;
    groupBy?: 'day' | 'week' | 'month';
  }): Promise<UsageSummary> {
    return this.request({
      method: 'GET',
      url: '/billing/usage/summary',
      params: this.buildQueryParams(options || {}),
    });
  }

  /**
   * Get current usage for a specific type
   */
  async getCurrentUsage(type: string): Promise<{
    type: string;
    current: number;
    limit: number;
    percentage: number;
    resetAt: string;
  }> {
    this.validateRequired({ type }, ['type']);
    
    return this.request({
      method: 'GET',
      url: `/billing/usage/current/${type}`,
    });
  }

  /**
   * Get all quotas
   */
  async getQuotas(): Promise<Quota[]> {
    return this.request({
      method: 'GET',
      url: '/billing/quotas',
    });
  }

  /**
   * Get specific quota
   */
  async getQuota(type: string): Promise<Quota> {
    this.validateRequired({ type }, ['type']);
    
    return this.request({
      method: 'GET',
      url: `/billing/quotas/${type}`,
    });
  }

  /**
   * Update quota limit
   */
  async updateQuota(type: string, limit: number): Promise<Quota> {
    this.validateRequired({ type, limit }, ['type', 'limit']);
    
    return this.request({
      method: 'PUT',
      url: `/billing/quotas/${type}`,
      data: { limit },
    });
  }

  /**
   * Reset quota usage
   */
  async resetQuota(type: string): Promise<Quota> {
    this.validateRequired({ type }, ['type']);
    
    return this.request({
      method: 'POST',
      url: `/billing/quotas/${type}/reset`,
    });
  }

  /**
   * Get invoices
   */
  async getInvoices(params?: {
    status?: 'draft' | 'pending' | 'paid' | 'failed' | 'cancelled';
    startDate?: string;
    endDate?: string;
    page?: number;
    limit?: number;
  }): Promise<PaginatedResponse<Invoice>> {
    return this.request({
      method: 'GET',
      url: '/billing/invoices',
      params: this.buildQueryParams(params || {}),
    });
  }

  /**
   * Get specific invoice
   */
  async getInvoice(id: string): Promise<Invoice> {
    this.validateRequired({ id }, ['id']);
    
    return this.request({
      method: 'GET',
      url: `/billing/invoices/${id}`,
    });
  }

  /**
   * Download invoice PDF
   */
  async downloadInvoice(id: string): Promise<{
    invoiceId: string;
    downloadUrl: string;
    expiresAt: string;
  }> {
    this.validateRequired({ id }, ['id']);
    
    return this.request({
      method: 'GET',
      url: `/billing/invoices/${id}/download`,
    });
  }

  /**
   * Pay invoice
   */
  async payInvoice(id: string, paymentMethodId?: string): Promise<{
    invoiceId: string;
    status: string;
    paidAt: string;
    transactionId: string;
  }> {
    this.validateRequired({ id }, ['id']);
    
    return this.request({
      method: 'POST',
      url: `/billing/invoices/${id}/pay`,
      data: paymentMethodId ? { paymentMethodId } : {},
    });
  }

  /**
   * Get payment methods
   */
  async getPaymentMethods(): Promise<PaymentMethod[]> {
    return this.request({
      method: 'GET',
      url: '/billing/payment-methods',
    });
  }

  /**
   * Add payment method
   */
  async addPaymentMethod(paymentMethod: {
    type: 'card' | 'bank_account' | 'paypal';
    token: string;
    isDefault?: boolean;
    metadata?: Record<string, any>;
  }): Promise<PaymentMethod> {
    this.validateRequired(paymentMethod, ['type', 'token']);
    
    return this.request({
      method: 'POST',
      url: '/billing/payment-methods',
      data: paymentMethod,
    });
  }

  /**
   * Update payment method
   */
  async updatePaymentMethod(
    id: string, 
    updates: {
      isDefault?: boolean;
      metadata?: Record<string, any>;
    }
  ): Promise<PaymentMethod> {
    this.validateRequired({ id }, ['id']);
    
    return this.request({
      method: 'PUT',
      url: `/billing/payment-methods/${id}`,
      data: updates,
    });
  }

  /**
   * Delete payment method
   */
  async deletePaymentMethod(id: string): Promise<void> {
    this.validateRequired({ id }, ['id']);
    
    await this.request({
      method: 'DELETE',
      url: `/billing/payment-methods/${id}`,
    });
  }

  /**
   * Set default payment method
   */
  async setDefaultPaymentMethod(id: string): Promise<PaymentMethod> {
    this.validateRequired({ id }, ['id']);
    
    return this.request({
      method: 'POST',
      url: `/billing/payment-methods/${id}/set-default`,
    });
  }

  /**
   * Get current subscription
   */
  async getSubscription(): Promise<Subscription | null> {
    return this.request({
      method: 'GET',
      url: '/billing/subscription',
    });
  }

  /**
   * Update subscription
   */
  async updateSubscription(planId: string): Promise<Subscription> {
    this.validateRequired({ planId }, ['planId']);
    
    return this.request({
      method: 'PUT',
      url: '/billing/subscription',
      data: { planId },
    });
  }

  /**
   * Cancel subscription
   */
  async cancelSubscription(cancelAtPeriodEnd = true): Promise<Subscription> {
    return this.request({
      method: 'POST',
      url: '/billing/subscription/cancel',
      data: { cancelAtPeriodEnd },
    });
  }

  /**
   * Resume subscription
   */
  async resumeSubscription(): Promise<Subscription> {
    return this.request({
      method: 'POST',
      url: '/billing/subscription/resume',
    });
  }

  /**
   * Get available plans
   */
  async getPlans(): Promise<Array<{
    id: string;
    name: string;
    description: string;
    price: number;
    currency: string;
    interval: 'month' | 'year';
    features: string[];
    limits: Record<string, number>;
    metadata: Record<string, any>;
  }>> {
    return this.request({
      method: 'GET',
      url: '/billing/plans',
    });
  }

  /**
   * Get billing address
   */
  async getBillingAddress(): Promise<{
    name: string;
    company?: string;
    line1: string;
    line2?: string;
    city: string;
    state?: string;
    postalCode: string;
    country: string;
  } | null> {
    return this.request({
      method: 'GET',
      url: '/billing/address',
    });
  }

  /**
   * Update billing address
   */
  async updateBillingAddress(address: {
    name: string;
    company?: string;
    line1: string;
    line2?: string;
    city: string;
    state?: string;
    postalCode: string;
    country: string;
  }): Promise<void> {
    this.validateRequired(address, ['name', 'line1', 'city', 'postalCode', 'country']);
    
    await this.request({
      method: 'PUT',
      url: '/billing/address',
      data: address,
    });
  }

  /**
   * Get billing portal session URL
   */
  async getBillingPortalUrl(returnUrl?: string): Promise<{
    url: string;
    expiresAt: string;
  }> {
    return this.request({
      method: 'POST',
      url: '/billing/portal',
      data: returnUrl ? { returnUrl } : {},
    });
  }

  /**
   * Get cost forecast
   */
  async getCostForecast(days = 30): Promise<{
    forecastDays: number;
    currentTrend: number;
    projectedCost: number;
    confidence: number;
    breakdown: Record<string, number>;
    recommendations: string[];
  }> {
    return this.request({
      method: 'GET',
      url: '/billing/forecast',
      params: { days },
    });
  }

  /**
   * Set spending alerts
   */
  async setSpendingAlert(alert: {
    threshold: number;
    type: 'total' | 'daily' | 'monthly';
    enabled: boolean;
    notificationMethods: ('email' | 'sms' | 'webhook')[];
    metadata?: Record<string, any>;
  }): Promise<{
    id: string;
    threshold: number;
    type: string;
    enabled: boolean;
    notificationMethods: string[];
    createdAt: string;
  }> {
    this.validateRequired(alert, ['threshold', 'type', 'enabled', 'notificationMethods']);
    
    return this.request({
      method: 'POST',
      url: '/billing/alerts',
      data: alert,
    });
  }

  /**
   * Get spending alerts
   */
  async getSpendingAlerts(): Promise<Array<{
    id: string;
    threshold: number;
    type: string;
    enabled: boolean;
    notificationMethods: string[];
    triggered: boolean;
    lastTriggeredAt?: string;
    createdAt: string;
  }>> {
    return this.request({
      method: 'GET',
      url: '/billing/alerts',
    });
  }

  /**
   * Update spending alert
   */
  async updateSpendingAlert(
    id: string, 
    updates: {
      threshold?: number;
      enabled?: boolean;
      notificationMethods?: ('email' | 'sms' | 'webhook')[];
    }
  ): Promise<void> {
    this.validateRequired({ id }, ['id']);
    
    await this.request({
      method: 'PUT',
      url: `/billing/alerts/${id}`,
      data: updates,
    });
  }

  /**
   * Delete spending alert
   */
  async deleteSpendingAlert(id: string): Promise<void> {
    this.validateRequired({ id }, ['id']);
    
    await this.request({
      method: 'DELETE',
      url: `/billing/alerts/${id}`,
    });
  }

  /**
   * Export billing data
   */
  async exportBillingData(options: {
    startDate: string;
    endDate: string;
    format: 'csv' | 'pdf' | 'excel';
    includeUsage?: boolean;
    includeInvoices?: boolean;
  }): Promise<{
    exportId: string;
    downloadUrl: string;
    expiresAt: string;
  }> {
    this.validateRequired(options, ['startDate', 'endDate', 'format']);
    
    return this.request({
      method: 'POST',
      url: '/billing/export',
      data: options,
    });
  }

  /**
   * Subscribe to billing events
   */
  onBillingEvent(callback: (event: any) => void): string {
    return this.subscribe({
      eventTypes: [
        'billing.usage_updated',
        'billing.quota_exceeded',
        'billing.invoice_generated',
        'billing.payment_processed',
        'billing.payment_failed',
        'billing.subscription_updated'
      ],
      organizationId: this.getOrganizationId(),
      callback,
    });
  }

  /**
   * Monitor quota usage in real-time
   */
  async monitorQuotas(callback: (quota: {
    type: string;
    used: number;
    limit: number;
    percentage: number;
    status: 'normal' | 'warning' | 'exceeded';
  }) => void): Promise<string> {
    return this.subscribe({
      eventTypes: ['billing.usage_updated', 'billing.quota_exceeded'],
      organizationId: this.getOrganizationId(),
      callback: (event) => {
        if (event.type === 'billing.usage_updated') {
          const { type, used, limit } = event.payload;
          const percentage = (used / limit) * 100;
          let status: 'normal' | 'warning' | 'exceeded' = 'normal';
          
          if (percentage >= 100) status = 'exceeded';
          else if (percentage >= 80) status = 'warning';
          
          callback({ type, used, limit, percentage, status });
        }
      },
    });
  }
}