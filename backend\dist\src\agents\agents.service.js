"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AgentsService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
const apix_service_1 = require("../apix/apix.service");
let AgentsService = class AgentsService {
    constructor(prisma, apixService) {
        this.prisma = prisma;
        this.apixService = apixService;
    }
    async create(userId, organizationId, createAgentDto) {
        const { templateId, config, tags = [], ...agentData } = createAgentDto;
        if (templateId) {
            const template = await this.prisma.template.findFirst({
                where: {
                    id: templateId,
                    OR: [
                        { organizationId },
                        { isPublic: true },
                    ],
                },
            });
            if (!template) {
                throw new common_1.NotFoundException('Template not found');
            }
            const templateConfig = template.template;
            config.prompt = config.prompt || templateConfig.prompt;
            config.systemPrompt = config.systemPrompt || templateConfig.systemPrompt;
            config.temperature = config.temperature ?? templateConfig.config?.temperature ?? 0.7;
            config.maxTokens = config.maxTokens ?? templateConfig.config?.maxTokens ?? 4000;
            config.memory = config.memory || templateConfig.config?.memory;
        }
        const agent = await this.prisma.agent.create({
            data: {
                ...agentData,
                organizationId,
                templateId,
                config: {
                    ...config,
                    memory: config.memory || {
                        enabled: true,
                        maxMessages: 50,
                        strategy: 'rolling',
                    },
                },
                metadata: {
                    tags,
                    createdBy: userId,
                    version: '1.0.0',
                },
            },
        });
        await this.apixService.publishAgentEvent('created', agent.id, organizationId, {
            name: agent.name,
            status: agent.status,
            createdBy: userId,
        });
        return agent;
    }
    async findAll(organizationId, query) {
        const { page = 1, limit = 20, status, search, model, tags, sortBy = 'createdAt', sortOrder = 'desc', } = query;
        const skip = (page - 1) * limit;
        const where = {
            organizationId,
            ...(status && { status }),
        };
        if (search) {
            where.OR = [
                { name: { contains: search, mode: 'insensitive' } },
                { description: { contains: search, mode: 'insensitive' } },
            ];
        }
        if (model) {
            where.config = {
                path: ['model'],
                equals: model,
            };
        }
        if (tags && tags.length > 0) {
            where.metadata = {
                path: ['tags'],
                array_contains: tags,
            };
        }
        const [agents, total] = await Promise.all([
            this.prisma.agent.findMany({
                where,
                include: {
                    template: {
                        select: {
                            id: true,
                            name: true,
                            category: true,
                        },
                    },
                },
                orderBy: { [sortBy]: sortOrder },
                skip,
                take: limit,
            }),
            this.prisma.agent.count({ where }),
        ]);
        return {
            agents,
            pagination: {
                page,
                limit,
                total,
                pages: Math.ceil(total / limit),
            },
        };
    }
    async findOne(id, organizationId) {
        const agent = await this.prisma.agent.findUnique({
            where: { id },
            include: {
                template: {
                    select: {
                        id: true,
                        name: true,
                        description: true,
                        category: true,
                    },
                },
            },
        });
        if (!agent) {
            throw new common_1.NotFoundException('Agent not found');
        }
        if (agent.organizationId !== organizationId) {
            throw new common_1.ForbiddenException('Access denied');
        }
        return agent;
    }
    async update(id, organizationId, updateAgentDto) {
        const agent = await this.findOne(id, organizationId);
        const updatedAgent = await this.prisma.agent.update({
            where: { id },
            data: {
                ...updateAgentDto,
                config: updateAgentDto.config ? {
                    ...agent.config,
                    ...updateAgentDto.config,
                } : agent.config,
                metadata: {
                    ...agent.metadata,
                    ...(updateAgentDto.tags && { tags: updateAgentDto.tags }),
                    updatedAt: new Date(),
                },
                updatedAt: new Date(),
            },
        });
        await this.apixService.publishAgentEvent('updated', agent.id, organizationId, {
            changes: updateAgentDto,
        });
        return updatedAgent;
    }
    async remove(id, organizationId) {
        const agent = await this.findOne(id, organizationId);
        const activeSessions = await this.prisma.session.count({
            where: {
                organizationId,
                type: 'agent',
                metadata: {
                    path: ['targetId'],
                    equals: id,
                },
                status: 'active',
            },
        });
        if (activeSessions > 0) {
            throw new common_1.BadRequestException('Cannot delete agent with active sessions');
        }
        await this.prisma.agent.delete({
            where: { id },
        });
        await this.apixService.publishAgentEvent('deleted', agent.id, organizationId, {});
        return { message: 'Agent deleted successfully' };
    }
    async createVersion(id, organizationId, versionDto) {
        const agent = await this.findOne(id, organizationId);
        const currentMetadata = agent.metadata;
        const versions = currentMetadata.versions || [];
        versions.push({
            version: versionDto.version,
            description: versionDto.description,
            config: versionDto.config,
            createdAt: new Date(),
            metadata: versionDto.metadata,
        });
        const updatedAgent = await this.prisma.agent.update({
            where: { id },
            data: {
                config: versionDto.config,
                metadata: {
                    ...currentMetadata,
                    versions,
                    currentVersion: versionDto.version,
                },
                updatedAt: new Date(),
            },
        });
        await this.apixService.publishAgentEvent('version_created', agent.id, organizationId, {
            version: versionDto.version,
        });
        return updatedAgent;
    }
    async rollbackToVersion(id, organizationId, version) {
        const agent = await this.findOne(id, organizationId);
        const metadata = agent.metadata;
        const versions = metadata.versions || [];
        const targetVersion = versions.find((v) => v.version === version);
        if (!targetVersion) {
            throw new common_1.NotFoundException('Version not found');
        }
        const updatedAgent = await this.prisma.agent.update({
            where: { id },
            data: {
                config: targetVersion.config,
                metadata: {
                    ...metadata,
                    currentVersion: version,
                    rolledBackAt: new Date(),
                },
                updatedAt: new Date(),
            },
        });
        await this.apixService.publishAgentEvent('version_rollback', agent.id, organizationId, {
            version,
            previousVersion: metadata.currentVersion,
        });
        return updatedAgent;
    }
    async clone(id, organizationId, cloneDto) {
        const originalAgent = await this.findOne(id, organizationId);
        const clonedAgent = await this.prisma.agent.create({
            data: {
                name: cloneDto.name,
                description: cloneDto.description || `Clone of ${originalAgent.name}`,
                organizationId,
                templateId: originalAgent.templateId,
                config: originalAgent.config,
                status: 'draft',
                metadata: {
                    ...originalAgent.metadata,
                    clonedFrom: originalAgent.id,
                    clonedAt: new Date(),
                    versions: cloneDto.includeVersionHistory
                        ? originalAgent.metadata.versions
                        : undefined,
                },
            },
        });
        await this.apixService.publishAgentEvent('cloned', clonedAgent.id, organizationId, {
            originalId: originalAgent.id,
            originalName: originalAgent.name,
        });
        return clonedAgent;
    }
    async getAgentStats(organizationId) {
        const [total, byStatus, byModel, executions] = await Promise.all([
            this.prisma.agent.count({ where: { organizationId } }),
            this.prisma.agent.groupBy({
                by: ['status'],
                where: { organizationId },
                _count: true,
            }),
            this.prisma.agent.groupBy({
                by: ['config'],
                where: { organizationId },
                _count: true,
            }),
            this.prisma.session.count({
                where: {
                    organizationId,
                    type: 'agent',
                },
            }),
        ]);
        const modelStats = {};
        const modelData = await this.prisma.agent.findMany({
            where: { organizationId },
            select: { config: true },
        });
        modelData.forEach((agent) => {
            const config = agent.config;
            const model = config.model;
            modelStats[model] = (modelStats[model] || 0) + 1;
        });
        return {
            total,
            executions,
            byStatus: byStatus.reduce((acc, item) => {
                acc[item.status] = item._count;
                return acc;
            }, {}),
            byModel: modelStats,
        };
    }
    async getPopularModels(organizationId) {
        const agents = await this.prisma.agent.findMany({
            where: { organizationId },
            select: { config: true },
        });
        const modelCounts = {};
        agents.forEach((agent) => {
            const config = agent.config;
            const model = config.model;
            modelCounts[model] = (modelCounts[model] || 0) + 1;
        });
        return Object.entries(modelCounts)
            .map(([model, count]) => ({ model, count }))
            .sort((a, b) => b.count - a.count)
            .slice(0, 10);
    }
    async searchAgentsByTags(organizationId, tags) {
        return this.prisma.agent.findMany({
            where: {
                organizationId,
                metadata: {
                    path: ['tags'],
                    array_contains: tags,
                },
            },
            select: {
                id: true,
                name: true,
                description: true,
                status: true,
                metadata: true,
            },
        });
    }
};
exports.AgentsService = AgentsService;
exports.AgentsService = AgentsService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService,
        apix_service_1.APIMXService])
], AgentsService);
//# sourceMappingURL=agents.service.js.map