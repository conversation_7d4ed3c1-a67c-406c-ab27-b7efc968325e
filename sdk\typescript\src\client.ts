import axios, { AxiosInstance, AxiosRequestConfig } from 'axios';
import EventEmitter from 'eventemitter3';
import WebSocket from 'ws';
import { 
  SynapseAIConfig, 
  SynapseAIClientError, 
  WebSocketEvent, 
  EventSubscription,
  EventType
} from './types';
import { AgentManager } from './managers/agent-manager';
import { ToolManager } from './managers/tool-manager';
import { ProviderManager } from './managers/provider-manager';
import { SessionManager } from './managers/session-manager';
import { BillingManager } from './managers/billing-manager';
import { AnalyticsManager } from './managers/analytics-manager';
import { WorkflowManager } from './managers/workflow-manager';

export class SynapseAIClient extends EventEmitter {
  private config: Required<SynapseAIConfig>;
  private httpClient: AxiosInstance;
  private wsClient?: WebSocket;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectInterval = 5000;
  private subscriptions: Map<string, EventSubscription> = new Map();

  // Manager instances
  public readonly agents: AgentManager;
  public readonly tools: ToolManager;
  public readonly workflows: WorkflowManager;
  public readonly providers: ProviderManager;
  public readonly sessions: SessionManager;
  public readonly billing: BillingManager;
  public readonly analytics: AnalyticsManager;

  constructor(config: SynapseAIConfig) {
    super();

    // Set default configuration
    this.config = {
      apiKey: config.apiKey,
      baseUrl: config.baseUrl || 'https://api.synapseai.com',
      organizationId: config.organizationId || '',
      timeout: config.timeout || 30000,
      retries: config.retries || 3,
      enableWebSocket: config.enableWebSocket ?? true,
      debug: config.debug || false,
    };

    // Initialize HTTP client
    this.httpClient = this.createHttpClient();

    // Initialize managers
    this.agents = new AgentManager(this);
    this.tools = new ToolManager(this);
    this.workflows = new WorkflowManager(this);
    this.providers = new ProviderManager(this);
    this.sessions = new SessionManager(this);
    this.billing = new BillingManager(this);
    this.analytics = new AnalyticsManager(this);

    // Initialize WebSocket if enabled
    if (this.config.enableWebSocket) {
      this.initializeWebSocket();
    }
  }

  /**
   * Get the current configuration
   */
  public getConfig(): Required<SynapseAIConfig> {
    return { ...this.config };
  }

  /**
   * Update configuration
   */
  public updateConfig(updates: Partial<SynapseAIConfig>): void {
    this.config = { ...this.config, ...updates };
    this.httpClient = this.createHttpClient();
    
    if (updates.enableWebSocket !== undefined) {
      if (updates.enableWebSocket && !this.wsClient) {
        this.initializeWebSocket();
      } else if (!updates.enableWebSocket && this.wsClient) {
        this.disconnectWebSocket();
      }
    }
  }

  /**
   * Set organization ID for all requests
   */
  public setOrganization(organizationId: string): void {
    this.config.organizationId = organizationId;
    this.httpClient.defaults.headers.common['X-Organization-ID'] = organizationId;
  }

  /**
   * Get the HTTP client instance for advanced usage
   */
  public getHttpClient(): AxiosInstance {
    return this.httpClient;
  }

  /**
   * Make a raw HTTP request
   */
  public async request<T = any>(config: AxiosRequestConfig): Promise<T> {
    try {
      const response = await this.httpClient.request(config);
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  /**
   * Test the connection to the API
   */
  public async testConnection(): Promise<boolean> {
    try {
      await this.request({ method: 'GET', url: '/health' });
      return true;
    } catch (error) {
      if (this.config.debug) {
        console.error('Connection test failed:', error);
      }
      return false;
    }
  }

  /**
   * Subscribe to real-time events
   */
  public subscribe(subscription: EventSubscription): string {
    const subscriptionId = this.generateSubscriptionId();
    this.subscriptions.set(subscriptionId, subscription);

    if (this.wsClient && this.wsClient.readyState === WebSocket.OPEN) {
      this.sendWebSocketMessage({
        type: 'subscribe',
        subscriptionId,
        eventTypes: subscription.eventTypes,
        organizationId: subscription.organizationId,
        userId: subscription.userId,
        sessionId: subscription.sessionId,
      });
    }

    return subscriptionId;
  }

  /**
   * Unsubscribe from real-time events
   */
  public unsubscribe(subscriptionId: string): void {
    this.subscriptions.delete(subscriptionId);

    if (this.wsClient && this.wsClient.readyState === WebSocket.OPEN) {
      this.sendWebSocketMessage({
        type: 'unsubscribe',
        subscriptionId,
      });
    }
  }

  /**
   * Subscribe to specific event types
   */
  public on(eventType: EventType, callback: (event: WebSocketEvent) => void): string {
    return this.subscribe({
      eventTypes: [eventType],
      organizationId: this.config.organizationId,
      callback,
    });
  }

  /**
   * Subscribe to session-specific events
   */
  public onSession(sessionId: string, callback: (event: WebSocketEvent) => void): string {
    return this.subscribe({
      eventTypes: ['session.updated', 'session.message_added', 'session.completed'],
      sessionId,
      organizationId: this.config.organizationId,
      callback,
    });
  }

  /**
   * Close all connections and clean up
   */
  public async close(): Promise<void> {
    this.subscriptions.clear();
    this.disconnectWebSocket();
    this.removeAllListeners();
  }

  /**
   * Enable or disable debug mode
   */
  public setDebug(enabled: boolean): void {
    this.config.debug = enabled;
  }

  // Private methods

  private createHttpClient(): AxiosInstance {
    const client = axios.create({
      baseURL: `${this.config.baseUrl}/api/v1`,
      timeout: this.config.timeout,
      headers: {
        'Authorization': `Bearer ${this.config.apiKey}`,
        'Content-Type': 'application/json',
        'User-Agent': '@synapseai/sdk-typescript/1.0.0',
        ...(this.config.organizationId && {
          'X-Organization-ID': this.config.organizationId
        }),
      },
    });

    // Request interceptor for debugging
    if (this.config.debug) {
      client.interceptors.request.use((config) => {
        console.log('SDK Request:', {
          method: config.method?.toUpperCase(),
          url: config.url,
          data: config.data,
        });
        return config;
      });
    }

    // Response interceptor for debugging and error handling
    client.interceptors.response.use(
      (response) => {
        if (this.config.debug) {
          console.log('SDK Response:', {
            status: response.status,
            data: response.data,
          });
        }
        return response;
      },
      (error) => {
        if (this.config.debug) {
          console.error('SDK Error:', {
            status: error.response?.status,
            message: error.message,
            data: error.response?.data,
          });
        }
        return Promise.reject(this.handleError(error));
      }
    );

    // Retry interceptor
    this.addRetryInterceptor(client);

    return client;
  }

  private addRetryInterceptor(client: AxiosInstance): void {
    let retryCount = 0;

    client.interceptors.response.use(
      (response) => {
        retryCount = 0;
        return response;
      },
      async (error) => {
        const shouldRetry = retryCount < this.config.retries &&
          error.response?.status >= 500 &&
          error.config &&
          !error.config.__isRetryRequest;

        if (shouldRetry) {
          retryCount++;
          error.config.__isRetryRequest = true;

          // Exponential backoff
          const delay = Math.pow(2, retryCount) * 1000;
          await new Promise(resolve => setTimeout(resolve, delay));

          return client.request(error.config);
        }

        retryCount = 0;
        return Promise.reject(error);
      }
    );
  }

  private handleError(error: any): SynapseAIClientError {
    if (error.response) {
      // Server responded with error status
      const { status, data } = error.response;
      return new SynapseAIClientError({
        code: data?.code || `HTTP_${status}`,
        message: data?.message || error.message || 'Request failed',
        details: data?.details || data,
        statusCode: status,
      });
    } else if (error.request) {
      // Network error
      return new SynapseAIClientError({
        code: 'NETWORK_ERROR',
        message: 'Network request failed',
        details: error.message,
      });
    } else {
      // Other error
      return new SynapseAIClientError({
        code: 'UNKNOWN_ERROR',
        message: error.message || 'Unknown error occurred',
        details: error,
      });
    }
  }

  private initializeWebSocket(): void {
    if (!this.config.apiKey) {
      console.warn('WebSocket cannot be initialized without an API key');
      return;
    }

    const wsUrl = this.config.baseUrl
      .replace('http://', 'ws://')
      .replace('https://', 'wss://');

    const url = `${wsUrl}/ws?token=${this.config.apiKey}&organizationId=${this.config.organizationId}`;

    try {
      this.wsClient = new WebSocket(url);

      this.wsClient.on('open', () => {
        this.reconnectAttempts = 0;
        this.emit('connected');
        
        if (this.config.debug) {
          console.log('WebSocket connected');
        }

        // Resubscribe to all existing subscriptions
        this.subscriptions.forEach((subscription, subscriptionId) => {
          this.sendWebSocketMessage({
            type: 'subscribe',
            subscriptionId,
            eventTypes: subscription.eventTypes,
            organizationId: subscription.organizationId,
            userId: subscription.userId,
            sessionId: subscription.sessionId,
          });
        });
      });

      this.wsClient.on('message', (data: string) => {
        try {
          const event: WebSocketEvent = JSON.parse(data);
          this.handleWebSocketEvent(event);
        } catch (error) {
          if (this.config.debug) {
            console.error('Failed to parse WebSocket message:', error);
          }
        }
      });

      this.wsClient.on('close', () => {
        this.emit('disconnected');
        
        if (this.config.debug) {
          console.log('WebSocket disconnected');
        }

        // Attempt to reconnect
        if (this.reconnectAttempts < this.maxReconnectAttempts) {
          setTimeout(() => {
            this.reconnectAttempts++;
            if (this.config.debug) {
              console.log(`Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
            }
            this.initializeWebSocket();
          }, this.reconnectInterval);
        } else {
          this.emit('reconnect_failed');
        }
      });

      this.wsClient.on('error', (error: Error) => {
        this.emit('error', error);
        if (this.config.debug) {
          console.error('WebSocket error:', error);
        }
      });

    } catch (error) {
      if (this.config.debug) {
        console.error('Failed to initialize WebSocket:', error);
      }
    }
  }

  private disconnectWebSocket(): void {
    if (this.wsClient) {
      this.wsClient.close();
      this.wsClient = undefined;
    }
  }

  private sendWebSocketMessage(message: any): void {
    if (this.wsClient && this.wsClient.readyState === WebSocket.OPEN) {
      this.wsClient.send(JSON.stringify(message));
    }
  }

  private handleWebSocketEvent(event: WebSocketEvent): void {
    // Emit the event for global listeners
    this.emit('event', event);
    this.emit(event.type, event);

    // Call subscription callbacks
    this.subscriptions.forEach((subscription) => {
      const matchesEventType = subscription.eventTypes.includes(event.type as EventType);
      const matchesOrganization = !subscription.organizationId || 
        subscription.organizationId === event.organizationId;
      const matchesUser = !subscription.userId || 
        subscription.userId === event.userId;
      const matchesSession = !subscription.sessionId || 
        subscription.sessionId === event.sessionId;

      if (matchesEventType && matchesOrganization && matchesUser && matchesSession) {
        try {
          subscription.callback(event);
        } catch (error) {
          if (this.config.debug) {
            console.error('Error in subscription callback:', error);
          }
        }
      }
    });
  }

  private generateSubscriptionId(): string {
    return `sub_${Date.now()}_${Math.random().toString(36).substring(2)}`;
  }

  /**
   * Get WebSocket connection status
   */
  public getConnectionStatus(): 'connecting' | 'connected' | 'disconnected' | 'error' {
    if (!this.wsClient) return 'disconnected';
    
    switch (this.wsClient.readyState) {
      case WebSocket.CONNECTING: return 'connecting';
      case WebSocket.OPEN: return 'connected';
      case WebSocket.CLOSING:
      case WebSocket.CLOSED: return 'disconnected';
      default: return 'error';
    }
  }

  /**
   * Force reconnect WebSocket
   */
  public reconnect(): void {
    this.disconnectWebSocket();
    this.reconnectAttempts = 0;
    if (this.config.enableWebSocket) {
      this.initializeWebSocket();
    }
  }
}