export declare enum AgentStatus {
    DRAFT = "draft",
    ACTIVE = "active",
    ARCHIVED = "archived"
}
export declare enum ExecutionStrategy {
    ROLLING = "rolling",
    SUMMARIZE = "summarize",
    VECTOR = "vector"
}
export declare class AgentConfigDto {
    prompt: string;
    model: string;
    temperature: number;
    maxTokens: number;
    systemPrompt?: string;
    tools?: string[];
    memory?: {
        enabled: boolean;
        maxMessages: number;
        strategy: ExecutionStrategy;
    };
    parameters?: Record<string, any>;
}
export declare class CreateAgentDto {
    name: string;
    description: string;
    templateId?: string;
    config: AgentConfigDto;
    status?: AgentStatus;
    tags?: string[];
}
export declare class UpdateAgentDto {
    name?: string;
    description?: string;
    config?: Partial<AgentConfigDto>;
    status?: AgentStatus;
    tags?: string[];
}
export declare class AgentQueryDto {
    page?: number;
    limit?: number;
    status?: AgentStatus;
    search?: string;
    model?: string;
    tags?: string[];
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
}
export declare class ExecuteAgentDto {
    input: string;
    sessionId?: string;
    context?: Record<string, any>;
    streaming?: boolean;
    overrides?: {
        temperature?: number;
        maxTokens?: number;
        model?: string;
    };
}
export declare class AgentVersionDto {
    version: string;
    description: string;
    config: AgentConfigDto;
    metadata?: Record<string, any>;
}
export declare class CloneAgentDto {
    name: string;
    description?: string;
    includeVersionHistory?: boolean;
}
