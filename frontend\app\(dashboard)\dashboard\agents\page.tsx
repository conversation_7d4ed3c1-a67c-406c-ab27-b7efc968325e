'use client';

import { useState, useEffect } from 'react';
import { useQuery } from 'react-query';
import { agentsA<PERSON>, Agent } from '@/lib/api/agents';
import { useAuth } from '@/lib/auth-context';
import Link from 'next/link';
import {
  Bot,
  Plus,
  Search,
  Filter,
  MoreVertical,
  Play,
  Pause,
  Edit,
  Copy,
  Trash2,
  Activity,
  Clock,
  Zap,
} from 'lucide-react';
import toast from 'react-hot-toast';

export default function AgentsPage() {
  const { hasPermission } = useAuth();
  const [search, setSearch] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('');
  const [page, setPage] = useState(1);

  const { data, isLoading, error, refetch } = useQuery(
    ['agents', { search, status: statusFilter, page }],
    () => agentsApi.getAll({
      search: search || undefined,
      status: statusFilter as any || undefined,
      page,
      limit: 12,
    }),
    {
      keepPreviousData: true,
    }
  );

  const { data: stats } = useQuery(['agents-stats'], agentsApi.getStats);

  const handleStatusToggle = async (agent: Agent) => {
    try {
      const newStatus = agent.status === 'active' ? 'draft' : 'active';
      await agentsApi.update(agent.id, { status: newStatus });
      toast.success(`Agent ${newStatus === 'active' ? 'activated' : 'deactivated'}`);
      refetch();
    } catch (error) {
      toast.error('Failed to update agent status');
    }
  };

  const handleDelete = async (agent: Agent) => {
    if (!confirm(`Are you sure you want to delete "${agent.name}"?`)) return;

    try {
      await agentsApi.delete(agent.id);
      toast.success('Agent deleted successfully');
      refetch();
    } catch (error) {
      toast.error('Failed to delete agent');
    }
  };

  const handleClone = async (agent: Agent) => {
    try {
      await agentsApi.clone(agent.id, {
        name: `${agent.name} (Copy)`,
        description: `Clone of ${agent.name}`,
      });
      toast.success('Agent cloned successfully');
      refetch();
    } catch (error) {
      toast.error('Failed to clone agent');
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-success-100 text-success-800 dark:bg-success-900 dark:text-success-200';
      case 'draft':
        return 'bg-warning-100 text-warning-800 dark:bg-warning-900 dark:text-warning-200';
      case 'archived':
        return 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200';
    }
  };

  if (error) {
    return (
      <div className="text-center py-12">
        <p className="text-error-600 dark:text-error-400">Failed to load agents</p>
        <button onClick={() => refetch()} className="btn-primary mt-4">
          Retry
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Agents</h1>
          <p className="text-gray-500 dark:text-gray-400">
            Create and manage AI agents for your workflows
          </p>
        </div>
        {hasPermission('agents:create') && (
          <Link href="/dashboard/agents/new" className="btn-primary">
            <Plus className="h-4 w-4 mr-2" />
            Create Agent
          </Link>
        )}
      </div>

      {/* Stats Cards */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="card p-4">
            <div className="flex items-center">
              <Bot className="h-8 w-8 text-primary-600 dark:text-primary-400" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Total Agents</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">{stats.total}</p>
              </div>
            </div>
          </div>
          <div className="card p-4">
            <div className="flex items-center">
              <Activity className="h-8 w-8 text-success-600 dark:text-success-400" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Active</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                  {stats.byStatus.active || 0}
                </p>
              </div>
            </div>
          </div>
          <div className="card p-4">
            <div className="flex items-center">
              <Clock className="h-8 w-8 text-warning-600 dark:text-warning-400" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Draft</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                  {stats.byStatus.draft || 0}
                </p>
              </div>
            </div>
          </div>
          <div className="card p-4">
            <div className="flex items-center">
              <Zap className="h-8 w-8 text-primary-600 dark:text-primary-400" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Executions</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">{stats.executions}</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <input
            type="text"
            placeholder="Search agents..."
            value={search}
            onChange={(e) => setSearch(e.target.value)}
            className="input pl-10"
          />
        </div>
        <select
          value={statusFilter}
          onChange={(e) => setStatusFilter(e.target.value)}
          className="input w-auto"
        >
          <option value="">All Status</option>
          <option value="active">Active</option>
          <option value="draft">Draft</option>
          <option value="archived">Archived</option>
        </select>
      </div>

      {/* Agents Grid */}
      {isLoading ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[...Array(6)].map((_, i) => (
            <div key={i} className="card p-6 animate-pulse">
              <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4 mb-4"></div>
              <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-full mb-2"></div>
              <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-2/3"></div>
            </div>
          ))}
        </div>
      ) : data?.agents.length === 0 ? (
        <div className="text-center py-12">
          <Bot className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">No agents found</h3>
          <p className="text-gray-500 dark:text-gray-400 mb-6">
            {search || statusFilter
              ? 'Try adjusting your search criteria'
              : 'Get started by creating your first agent'}
          </p>
          {hasPermission('agents:create') && !search && !statusFilter && (
            <Link href="/dashboard/agents/new" className="btn-primary">
              <Plus className="h-4 w-4 mr-2" />
              Create Agent
            </Link>
          )}
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {data?.agents.map((agent) => (
            <div key={agent.id} className="card p-6 hover:shadow-md transition-shadow">
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <div className="h-10 w-10 rounded-lg bg-primary-100 dark:bg-primary-900 flex items-center justify-center">
                    <Bot className="h-5 w-5 text-primary-600 dark:text-primary-400" />
                  </div>
                  <div>
                    <h3 className="font-medium text-gray-900 dark:text-white">{agent.name}</h3>
                    <span className={`inline-block px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(agent.status)}`}>
                      {agent.status}
                    </span>
                  </div>
                </div>
                <div className="relative">
                  <button className="p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                    <MoreVertical className="h-4 w-4" />
                  </button>
                </div>
              </div>

              <p className="text-sm text-gray-600 dark:text-gray-400 mb-4 line-clamp-2">
                {agent.description}
              </p>

              <div className="space-y-2 mb-4">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-500 dark:text-gray-400">Model:</span>
                  <span className="text-gray-900 dark:text-white font-mono text-xs">
                    {agent.config.model}
                  </span>
                </div>
                {agent.template && (
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-500 dark:text-gray-400">Template:</span>
                    <span className="text-gray-900 dark:text-white">{agent.template.name}</span>
                  </div>
                )}
              </div>

              <div className="flex items-center justify-between pt-4 border-t border-gray-200 dark:border-gray-700">
                <div className="flex space-x-2">
                  {hasPermission('agents:update') && (
                    <button
                      onClick={() => handleStatusToggle(agent)}
                      className="p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                      title={agent.status === 'active' ? 'Deactivate' : 'Activate'}
                    >
                      {agent.status === 'active' ? (
                        <Pause className="h-4 w-4" />
                      ) : (
                        <Play className="h-4 w-4" />
                      )}
                    </button>
                  )}
                  {hasPermission('agents:update') && (
                    <Link
                      href={`/dashboard/agents/${agent.id}/edit`}
                      className="p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                      title="Edit"
                    >
                      <Edit className="h-4 w-4" />
                    </Link>
                  )}
                  {hasPermission('agents:create') && (
                    <button
                      onClick={() => handleClone(agent)}
                      className="p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                      title="Clone"
                    >
                      <Copy className="h-4 w-4" />
                    </button>
                  )}
                  {hasPermission('agents:delete') && (
                    <button
                      onClick={() => handleDelete(agent)}
                      className="p-1 text-gray-400 hover:text-error-600 dark:hover:text-error-400"
                      title="Delete"
                    >
                      <Trash2 className="h-4 w-4" />
                    </button>
                  )}
                </div>
                <Link
                  href={`/dashboard/agents/${agent.id}`}
                  className="text-sm text-primary-600 hover:text-primary-500 dark:text-primary-400 font-medium"
                >
                  View Details
                </Link>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Pagination */}
      {data && data.pagination.pages > 1 && (
        <div className="flex items-center justify-between">
          <p className="text-sm text-gray-700 dark:text-gray-300">
            Showing {((page - 1) * 12) + 1} to {Math.min(page * 12, data.pagination.total)} of{' '}
            {data.pagination.total} results
          </p>
          <div className="flex space-x-2">
            <button
              onClick={() => setPage(p => Math.max(1, p - 1))}
              disabled={page === 1}
              className="btn-secondary disabled:opacity-50"
            >
              Previous
            </button>
            <button
              onClick={() => setPage(p => Math.min(data.pagination.pages, p + 1))}
              disabled={page === data.pagination.pages}
              className="btn-secondary disabled:opacity-50"
            >
              Next
            </button>
          </div>
        </div>
      )}
    </div>
  );
}