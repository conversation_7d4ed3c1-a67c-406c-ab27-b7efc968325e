import { z } from 'zod';

export const OrganizationSchema = z.object({
  id: z.string().uuid(),
  name: z.string().min(1).max(100),
  slug: z.string().min(1).max(50).regex(/^[a-z0-9-]+$/),
  settings: z.object({
    branding: z.object({
      logo: z.string().url().optional(),
      primaryColor: z.string().regex(/^#[0-9A-F]{6}$/i).optional(),
      theme: z.enum(['light', 'dark']).optional(),
    }).optional(),
    billing: z.object({
      planId: z.string(),
      quotas: z.record(z.string(), z.number()),
    }).optional(),
    features: z.array(z.string()).optional(),
  }),
  createdAt: z.date(),
  updatedAt: z.date(),
});

export const UserSchema = z.object({
  id: z.string().uuid(),
  email: z.string().email(),
  name: z.string().min(1).max(100),
  avatarUrl: z.string().url().optional(),
  organizationId: z.string().uuid(),
  settings: z.object({
    theme: z.enum(['light', 'dark']).optional(),
    notifications: z.object({
      email: z.boolean(),
      sms: z.boolean(),
      webhook: z.boolean(),
      push: z.boolean(),
    }).optional(),
    language: z.string().optional(),
  }),
  createdAt: z.date(),
  updatedAt: z.date(),
});

export const AgentSchema = z.object({
  id: z.string().uuid(),
  name: z.string().min(1).max(100),
  description: z.string().max(500),
  organizationId: z.string().uuid(),
  templateId: z.string().uuid().optional(),
  config: z.object({
    prompt: z.string().min(1),
    model: z.string(),
    temperature: z.number().min(0).max(2),
    maxTokens: z.number().min(1).max(32000),
    systemPrompt: z.string().optional(),
    tools: z.array(z.string()).optional(),
    memory: z.object({
      enabled: z.boolean(),
      maxMessages: z.number().min(1),
      strategy: z.enum(['rolling', 'summarize', 'vector']),
    }).optional(),
  }),
  status: z.enum(['draft', 'active', 'archived']),
  createdAt: z.date(),
  updatedAt: z.date(),
});

export const ToolValidationSchema = z.object({
  id: z.string().uuid(),
  name: z.string().min(1).max(100),
  description: z.string().max(500),
  organizationId: z.string().uuid(),
  type: z.enum(['api', 'function', 'workflow']),
  config: z.object({
    endpoint: z.string().url().optional(),
    method: z.enum(['GET', 'POST', 'PUT', 'DELETE']).optional(),
    headers: z.record(z.string(), z.string()).optional(),
    authentication: z.object({
      type: z.enum(['none', 'api_key', 'bearer', 'basic', 'oauth2']),
      credentials: z.record(z.string(), z.string()),
    }).optional(),
    timeout: z.number().optional(),
    retries: z.number().optional(),
  }),
  schema: z.object({
    input: z.record(z.string(), z.any()),
    output: z.record(z.string(), z.any()),
  }),
  status: z.enum(['draft', 'active', 'archived']),
  createdAt: z.date(),
  updatedAt: z.date(),
});

export const SessionSchema = z.object({
  id: z.string().uuid(),
  userId: z.string().uuid(),
  organizationId: z.string().uuid(),
  type: z.enum(['agent', 'tool', 'hybrid', 'widget']),
  metadata: z.record(z.string(), z.any()),
  memory: z.object({
    messages: z.array(z.object({
      id: z.string().uuid(),
      role: z.enum(['user', 'assistant', 'system']),
      content: z.string(),
      metadata: z.record(z.string(), z.any()).optional(),
      timestamp: z.date(),
    })),
    context: z.record(z.string(), z.any()),
    tokenCount: z.number(),
    maxTokens: z.number(),
  }),
  status: z.enum(['active', 'completed', 'failed', 'paused']),
  createdAt: z.date(),
  updatedAt: z.date(),
});

export const HITLRequestSchema = z.object({
  id: z.string().uuid(),
  organizationId: z.string().uuid(),
  sessionId: z.string().uuid(),
  type: z.enum(['approval', 'review', 'escalation']),
  title: z.string().min(1).max(200),
  description: z.string().max(1000),
  payload: z.record(z.string(), z.any()),
  assigneeId: z.string().uuid().optional(),
  status: z.enum(['pending', 'approved', 'rejected', 'escalated']),
  priority: z.enum(['low', 'medium', 'high', 'urgent']),
  createdAt: z.date(),
  resolvedAt: z.date().optional(),
});

export const ProviderSchema = z.object({
  id: z.string().uuid(),
  name: z.string().min(1).max(100),
  type: z.enum(['openai', 'anthropic', 'google', 'mistral', 'groq', 'custom']),
  organizationId: z.string().uuid(),
  config: z.object({
    apiKey: z.string(),
    baseUrl: z.string().url().optional(),
    models: z.array(z.string()),
    rateLimit: z.number().optional(),
    timeout: z.number().optional(),
  }),
  status: z.enum(['active', 'inactive', 'error']),
  createdAt: z.date(),
  updatedAt: z.date(),
});

export const CreateAgentSchema = z.object({
  name: z.string().min(1).max(100),
  description: z.string().max(500),
  templateId: z.string().uuid().optional(),
  config: z.object({
    prompt: z.string().min(1),
    model: z.string(),
    temperature: z.number().min(0).max(2).default(0.7),
    maxTokens: z.number().min(1).max(32000).default(4000),
    systemPrompt: z.string().optional(),
    tools: z.array(z.string()).optional(),
    memory: z.object({
      enabled: z.boolean().default(true),
      maxMessages: z.number().min(1).default(50),
      strategy: z.enum(['rolling', 'summarize', 'vector']).default('rolling'),
    }).optional(),
  }),
});

export const CreateToolSchema = z.object({
  name: z.string().min(1).max(100),
  description: z.string().max(500),
  type: z.enum(['api', 'function', 'workflow']),
  config: z.object({
    endpoint: z.string().url().optional(),
    method: z.enum(['GET', 'POST', 'PUT', 'DELETE']).optional(),
    headers: z.record(z.string(), z.string()).optional(),
    authentication: z.object({
      type: z.enum(['none', 'api_key', 'bearer', 'basic', 'oauth2']),
      credentials: z.record(z.string(), z.string()),
    }).optional(),
    timeout: z.number().default(30000),
    retries: z.number().default(3),
  }),
  schema: z.object({
    input: z.record(z.string(), z.any()),
    output: z.record(z.string(), z.any()),
  }),
});

export const CreateSessionSchema = z.object({
  type: z.enum(['agent', 'tool', 'hybrid', 'widget']),
  targetId: z.string().uuid(),
  metadata: z.record(z.string(), z.any()).optional(),
});

export const APIXEventSchema = z.object({
  type: z.string(),
  payload: z.record(z.string(), z.any()),
});

export type CreateAgentRequest = z.infer<typeof CreateAgentSchema>;
export type CreateToolRequest = z.infer<typeof CreateToolSchema>;
export type CreateSessionRequest = z.infer<typeof CreateSessionSchema>;
export type APIXEventPayload = z.infer<typeof APIXEventSchema>;