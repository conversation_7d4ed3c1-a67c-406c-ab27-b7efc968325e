import { Injectable, NotFoundException, ForbiddenException, BadRequestException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { APIMXService } from '../apix/apix.service';
import { CreateTemplateDto, UpdateTemplateDto, TemplateQueryDto, RateTemplateDto, ForkTemplateDto } from './dto/template.dto';

@Injectable()
export class TemplatesService {
  constructor(
    private prisma: PrismaService,
    private apixService: APIMXService,
  ) {}

  async create(userId: string, organizationId: string, createTemplateDto: CreateTemplateDto) {
    const { tags = [], metadata = {}, ...templateData } = createTemplateDto;

    const template = await this.prisma.template.create({
      data: {
        ...templateData,
        organizationId: createTemplateDto.isPublic ? null : organizationId,
        metadata: {
          ...metadata,
          tags,
          createdBy: userId,
          version: metadata.version || '1.0.0',
          downloads: 0,
          forks: 0,
          ratings: [],
          avgRating: 0,
        },
      },
    });

    await this.apixService.publishEvent({
      type: 'template.created',
      organizationId,
      payload: {
        templateId: template.id,
        name: template.name,
        category: template.category,
        isPublic: template.isPublic,
      },
    });

    return template;
  }

  async findAll(organizationId: string, query: TemplateQueryDto) {
    const {
      page = 1,
      limit = 20,
      category,
      search,
      isPublic,
      tags,
      sortBy = 'createdAt',
      sortOrder = 'desc',
    } = query;

    const skip = (page - 1) * limit;

    const where: any = {
      OR: [
        { organizationId },
        { isPublic: true },
      ],
    };

    if (category) {
      where.category = category;
    }

    if (isPublic !== undefined) {
      where.isPublic = isPublic;
    }

    if (search) {
      where.AND = [
        where.AND || {},
        {
          OR: [
            { name: { contains: search, mode: 'insensitive' } },
            { description: { contains: search, mode: 'insensitive' } },
          ],
        },
      ];
    }

    if (tags && tags.length > 0) {
      where.metadata = {
        path: ['tags'],
        array_contains: tags,
      };
    }

    const [templates, total] = await Promise.all([
      this.prisma.template.findMany({
        where,
        include: {
          organization: {
            select: {
              id: true,
              name: true,
              slug: true,
            },
          },
        },
        orderBy: { [sortBy]: sortOrder },
        skip,
        take: limit,
      }),
      this.prisma.template.count({ where }),
    ]);

    return {
      templates,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    };
  }

  async findOne(id: string, organizationId: string) {
    const template = await this.prisma.template.findUnique({
      where: { id },
      include: {
        organization: {
          select: {
            id: true,
            name: true,
            slug: true,
          },
        },
      },
    });

    if (!template) {
      throw new NotFoundException('Template not found');
    }

    // Check access permissions
    if (!template.isPublic && template.organizationId !== organizationId) {
      throw new ForbiddenException('Access denied');
    }

    // Increment download count if accessing from different organization
    if (template.organizationId !== organizationId) {
      await this.incrementDownloadCount(template.id);
    }

    return template;
  }

  async update(id: string, organizationId: string, updateTemplateDto: UpdateTemplateDto) {
    const template = await this.prisma.template.findUnique({
      where: { id },
    });

    if (!template) {
      throw new NotFoundException('Template not found');
    }

    // Only the creator organization can update
    if (template.organizationId !== organizationId) {
      throw new ForbiddenException('Only the creator can update this template');
    }

    const updatedTemplate = await this.prisma.template.update({
      where: { id },
      data: {
        ...updateTemplateDto,
        template: updateTemplateDto.template ? {
          ...template.template,
          ...updateTemplateDto.template,
        } : template.template,
        metadata: updateTemplateDto.metadata ? {
          ...template.metadata,
          ...updateTemplateDto.metadata,
          updatedAt: new Date(),
        } : template.metadata,
        updatedAt: new Date(),
      },
    });

    await this.apixService.publishEvent({
      type: 'template.updated',
      organizationId,
      payload: {
        templateId: template.id,
        changes: updateTemplateDto,
      },
    });

    return updatedTemplate;
  }

  async remove(id: string, organizationId: string) {
    const template = await this.prisma.template.findUnique({
      where: { id },
    });

    if (!template) {
      throw new NotFoundException('Template not found');
    }

    // Only the creator organization can delete
    if (template.organizationId !== organizationId) {
      throw new ForbiddenException('Only the creator can delete this template');
    }

    // Check if template is being used by agents
    const agentsUsingTemplate = await this.prisma.agent.count({
      where: { templateId: id },
    });

    if (agentsUsingTemplate > 0) {
      throw new BadRequestException('Cannot delete template that is being used by agents');
    }

    await this.prisma.template.delete({
      where: { id },
    });

    await this.apixService.publishEvent({
      type: 'template.deleted',
      organizationId,
      payload: {
        templateId: id,
        name: template.name,
      },
    });

    return { message: 'Template deleted successfully' };
  }

  async fork(id: string, organizationId: string, forkDto: ForkTemplateDto) {
    const originalTemplate = await this.findOne(id, organizationId);

    const forkedTemplate = await this.prisma.template.create({
      data: {
        name: forkDto.name,
        description: forkDto.description || `Fork of ${originalTemplate.name}`,
        category: originalTemplate.category,
        template: originalTemplate.template,
        isPublic: forkDto.isPublic,
        organizationId: forkDto.isPublic ? null : organizationId,
        metadata: {
          ...originalTemplate.metadata,
          forkedFrom: originalTemplate.id,
          forkedAt: new Date(),
          downloads: 0,
          forks: 0,
          ratings: [],
          avgRating: 0,
        },
      },
    });

    // Increment fork count on original
    await this.incrementForkCount(originalTemplate.id);

    await this.apixService.publishEvent({
      type: 'template.forked',
      organizationId,
      payload: {
        originalId: originalTemplate.id,
        forkedId: forkedTemplate.id,
        originalName: originalTemplate.name,
        forkedName: forkedTemplate.name,
      },
    });

    return forkedTemplate;
  }

  async rate(id: string, organizationId: string, userId: string, rateDto: RateTemplateDto) {
    const template = await this.findOne(id, organizationId);

    const metadata = template.metadata as any;
    const ratings = metadata.ratings || [];

    // Check if user already rated
    const existingRatingIndex = ratings.findIndex((r: any) => r.userId === userId);

    if (existingRatingIndex >= 0) {
      // Update existing rating
      ratings[existingRatingIndex] = {
        userId,
        organizationId,
        rating: rateDto.rating,
        review: rateDto.review,
        updatedAt: new Date(),
      };
    } else {
      // Add new rating
      ratings.push({
        userId,
        organizationId,
        rating: rateDto.rating,
        review: rateDto.review,
        createdAt: new Date(),
      });
    }

    // Calculate average rating
    const avgRating = ratings.reduce((sum: number, r: any) => sum + r.rating, 0) / ratings.length;

    const updatedTemplate = await this.prisma.template.update({
      where: { id },
      data: {
        metadata: {
          ...metadata,
          ratings,
          avgRating: parseFloat(avgRating.toFixed(1)),
        },
      },
    });

    await this.apixService.publishEvent({
      type: 'template.rated',
      organizationId,
      payload: {
        templateId: id,
        rating: rateDto.rating,
        avgRating: updatedTemplate.metadata['avgRating'],
      },
    });

    return updatedTemplate;
  }

  async getPopularTemplates(organizationId: string, limit = 10) {
    return this.prisma.template.findMany({
      where: {
        OR: [
          { organizationId },
          { isPublic: true },
        ],
      },
      orderBy: [
        { metadata: { path: ['downloads'], sort: 'desc' } },
        { metadata: { path: ['avgRating'], sort: 'desc' } },
      ],
      take: limit,
      include: {
        organization: {
          select: {
            id: true,
            name: true,
            slug: true,
          },
        },
      },
    });
  }

  async getTemplatesByCategory(organizationId: string, category: string) {
    return this.prisma.template.findMany({
      where: {
        category,
        OR: [
          { organizationId },
          { isPublic: true },
        ],
      },
      orderBy: { metadata: { path: ['avgRating'], sort: 'desc' } },
      include: {
        organization: {
          select: {
            id: true,
            name: true,
            slug: true,
          },
        },
      },
    });
  }

  async getTemplateStats(organizationId: string) {
    const [total, byCategory, byVisibility, totalDownloads] = await Promise.all([
      this.prisma.template.count({
        where: { organizationId },
      }),
      this.prisma.template.groupBy({
        by: ['category'],
        where: { organizationId },
        _count: true,
      }),
      this.prisma.template.groupBy({
        by: ['isPublic'],
        where: { organizationId },
        _count: true,
      }),
      this.prisma.template.aggregate({
        where: { organizationId },
        _sum: {
          metadata: { path: ['downloads'] },
        },
      }),
    ]);

    return {
      total,
      totalDownloads: totalDownloads._sum || 0,
      byCategory: byCategory.reduce((acc, item) => {
        acc[item.category] = item._count;
        return acc;
      }, {}),
      byVisibility: byVisibility.reduce((acc, item) => {
        acc[item.isPublic ? 'public' : 'private'] = item._count;
        return acc;
      }, {}),
    };
  }

  private async incrementDownloadCount(templateId: string) {
    const template = await this.prisma.template.findUnique({
      where: { id: templateId },
    });

    if (template) {
      const metadata = template.metadata as any;
      await this.prisma.template.update({
        where: { id: templateId },
        data: {
          metadata: {
            ...metadata,
            downloads: (metadata.downloads || 0) + 1,
          },
        },
      });
    }
  }

  private async incrementForkCount(templateId: string) {
    const template = await this.prisma.template.findUnique({
      where: { id: templateId },
    });

    if (template) {
      const metadata = template.metadata as any;
      await this.prisma.template.update({
        where: { id: templateId },
        data: {
          metadata: {
            ...metadata,
            forks: (metadata.forks || 0) + 1,
          },
        },
      });
    }
  }

  async searchTemplates(organizationId: string, searchTerm: string, filters?: any) {
    const where: any = {
      OR: [
        { organizationId },
        { isPublic: true },
      ],
      AND: {
        OR: [
          { name: { contains: searchTerm, mode: 'insensitive' } },
          { description: { contains: searchTerm, mode: 'insensitive' } },
          { metadata: { path: ['tags'], array_contains: [searchTerm] } },
        ],
      },
    };

    if (filters?.category) {
      where.category = filters.category;
    }

    if (filters?.minRating) {
      where.metadata = {
        ...where.metadata,
        path: ['avgRating'],
        gte: filters.minRating,
      };
    }

    return this.prisma.template.findMany({
      where,
      include: {
        organization: {
          select: {
            id: true,
            name: true,
            slug: true,
          },
        },
      },
      orderBy: [
        { metadata: { path: ['avgRating'], sort: 'desc' } },
        { metadata: { path: ['downloads'], sort: 'desc' } },
      ],
    });
  }
}