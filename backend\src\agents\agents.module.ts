import { Modu<PERSON> } from '@nestjs/common';
import { AgentsController } from './agents.controller';
import { AgentsService } from './agents.service';
import { AgentExecutorService } from './agent-executor.service';
import { AuthModule } from '../auth/auth.module';
import { APIMXModule } from '../apix/apix.module';
import { SessionsModule } from '../sessions/sessions.module';
import { ProvidersModule } from '../providers/providers.module';

@Module({
  imports: [AuthModule, APIMXModule, SessionsModule, ProvidersModule],
  controllers: [AgentsController],
  providers: [AgentsService, AgentExecutorService],
  exports: [AgentsService, AgentExecutorService],
})
export class AgentsModule {}