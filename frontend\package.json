{"name": "@synapseai/frontend", "version": "1.0.0", "description": "Next.js frontend for SynapseAI platform", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:e2e": "playwright test"}, "dependencies": {"next": "^14.0.3", "react": "^18.2.0", "react-dom": "^18.2.0", "next-auth": "^4.24.5", "@synapseai/shared": "workspace:*", "@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.10", "clsx": "^2.0.0", "framer-motion": "^10.16.5", "react-hook-form": "^7.47.0", "react-hot-toast": "^2.4.1", "react-query": "^3.39.3", "socket.io-client": "^4.7.4", "zustand": "^4.4.7", "zod": "^3.22.4", "recharts": "^2.8.0", "react-markdown": "^9.0.1", "react-syntax-highlighter": "^15.5.0", "date-fns": "^2.30.0", "lucide-react": "^0.294.0"}, "devDependencies": {"@types/node": "^20.10.4", "@types/react": "^18.2.42", "@types/react-dom": "^18.2.17", "@types/react-syntax-highlighter": "^15.5.11", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "autoprefixer": "^10.4.16", "eslint": "^8.54.0", "eslint-config-next": "^14.0.3", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "postcss": "^8.4.32", "tailwindcss": "^3.3.6", "typescript": "^5.3.3", "@playwright/test": "^1.40.1"}}