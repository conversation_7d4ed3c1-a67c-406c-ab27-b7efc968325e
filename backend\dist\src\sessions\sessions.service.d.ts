import { PrismaService } from '../prisma/prisma.service';
import { APIMXService } from '../apix/apix.service';
import { CreateSessionDto, UpdateSessionDto, SessionQueryDto } from './dto/session.dto';
export declare class SessionsService {
    private prisma;
    private apixService;
    constructor(prisma: PrismaService, apixService: APIMXService);
    create(userId: string, organizationId: string, createSessionDto: CreateSessionDto): Promise<any>;
    findAll(organizationId: string, query: SessionQueryDto): Promise<{
        sessions: any;
        pagination: {
            page: number;
            limit: number;
            total: any;
            pages: number;
        };
    }>;
    findOne(id: string, organizationId: string, userId?: string): Promise<any>;
    update(id: string, organizationId: string, userId: string, updateSessionDto: UpdateSessionDto): Promise<any>;
    remove(id: string, organizationId: string, userId: string): Promise<{
        message: string;
    }>;
    addMessage(sessionId: string, organizationId: string, userId: string, message: any): Promise<any>;
    updateContext(sessionId: string, organizationId: string, userId: string, context: any): Promise<any>;
    clearMemory(sessionId: string, organizationId: string, userId: string): Promise<any>;
    getSessionStats(organizationId: string, userId?: string): Promise<{
        total: any;
        active: any;
        byType: any;
        byStatus: any;
    }>;
    private estimateTokenCount;
    private calculateTotalTokens;
    private truncateMessages;
}
