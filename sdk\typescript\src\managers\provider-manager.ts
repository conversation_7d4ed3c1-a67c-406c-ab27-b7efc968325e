import { Provider, PaginatedResponse, QueryParams } from '../types';
import { BaseManager } from './base-manager';

export interface ProviderQueryParams extends QueryParams {
  type?: 'openai' | 'claude' | 'gemini' | 'mistral' | 'groq' | 'custom';
  status?: 'active' | 'inactive' | 'maintenance' | 'error';
  capabilities?: string[];
  tags?: string[];
}

export interface CreateProviderRequest {
  name: string;
  description: string;
  type: 'openai' | 'claude' | 'gemini' | 'mistral' | 'groq' | 'custom';
  config: {
    apiKey?: string;
    baseUrl?: string;
    headers?: Record<string, string>;
    timeout?: number;
    maxRetries?: number;
    maxConcurrentRequests?: number;
    maxRequestsPerMinute?: number;
    rateLimits?: {
      requestsPerMinute: number;
      tokensPerMinute: number;
      requestsPerDay: number;
      tokensPerDay: number;
    };
  };
  models: Array<{
    id: string;
    name: string;
    description?: string;
    size: 'small' | 'medium' | 'large' | 'xlarge';
    capabilities: string[];
    inputCostPer1KTokens: number;
    outputCostPer1KTokens: number;
    maxTokens?: number;
    contextWindow?: number;
    parameters?: Record<string, any>;
  }>;
  status?: 'active' | 'inactive';
  priority?: number;
  tags?: string[];
  metadata?: Record<string, any>;
}

export interface ProviderSelectionCriteria {
  requiredCapabilities?: string[];
  preferredModelSize?: 'small' | 'medium' | 'large' | 'xlarge';
  maxCostPer1KTokens?: number;
  minTokens?: number;
  region?: string;
  complianceRequirements?: string[];
  preferLowLatency?: boolean;
  preferLowCost?: boolean;
  organizationPreferences?: {
    preferredProviders?: string[];
    excludedProviders?: string[];
    maxCostThreshold?: number;
  };
}

export interface ProviderExecutionRequest {
  providerId: string;
  modelId: string;
  prompt: string;
  parameters?: {
    temperature?: number;
    maxTokens?: number;
    topP?: number;
    frequencyPenalty?: number;
    presencePenalty?: number;
    stopSequences?: string[];
  };
  sessionId?: string;
  context?: Record<string, any>;
  stream?: boolean;
  overrides?: {
    timeout?: number;
    maxRetries?: number;
  };
}

export interface ProviderExecutionResult {
  success: boolean;
  response: string;
  usage: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  };
  cost: number;
  executionTime: number;
  modelId: string;
  providerId: string;
  sessionId?: string;
  metadata: {
    finishReason?: string;
    model?: string;
    version?: string;
    latency?: number;
    retryCount?: number;
  };
}

export class ProviderManager extends BaseManager {
  /**
   * Create a new provider
   */
  async create(provider: CreateProviderRequest): Promise<Provider> {
    this.validateRequired(provider, ['name', 'description', 'type', 'config', 'models']);
    
    return this.request({
      method: 'POST',
      url: '/providers',
      data: provider,
    });
  }

  /**
   * Get all providers
   */
  async list(params?: ProviderQueryParams): Promise<PaginatedResponse<Provider>> {
    return this.request({
      method: 'GET',
      url: '/providers',
      params: this.buildQueryParams(params || {}),
    });
  }

  /**
   * Get a specific provider by ID
   */
  async get(id: string): Promise<Provider> {
    this.validateRequired({ id }, ['id']);
    
    return this.request({
      method: 'GET',
      url: `/providers/${id}`,
    });
  }

  /**
   * Update a provider
   */
  async update(id: string, updates: Partial<CreateProviderRequest>): Promise<Provider> {
    this.validateRequired({ id }, ['id']);
    
    return this.request({
      method: 'PUT',
      url: `/providers/${id}`,
      data: updates,
    });
  }

  /**
   * Delete a provider
   */
  async delete(id: string): Promise<void> {
    this.validateRequired({ id }, ['id']);
    
    await this.request({
      method: 'DELETE',
      url: `/providers/${id}`,
    });
  }

  /**
   * Select optimal provider based on criteria
   */
  async selectOptimal(criteria: ProviderSelectionCriteria): Promise<{
    provider: Provider;
    model: any;
    score: number;
  }> {
    return this.request({
      method: 'POST',
      url: '/providers/select',
      data: criteria,
    });
  }

  /**
   * Execute with a specific provider
   */
  async execute(request: ProviderExecutionRequest): Promise<ProviderExecutionResult> {
    this.validateRequired(request, ['providerId', 'modelId', 'prompt']);
    
    return this.request({
      method: 'POST',
      url: '/providers/execute',
      data: request,
    });
  }

  /**
   * Perform health check on a provider
   */
  async healthCheck(id: string, options?: {
    checkAllModels?: boolean;
    checkRateLimits?: boolean;
    checkLatency?: boolean;
    testPrompt?: string;
  }): Promise<{
    providerId: string;
    status: 'active' | 'inactive' | 'maintenance' | 'error';
    models: Array<{
      modelId: string;
      status: 'healthy' | 'unhealthy';
      latency?: number;
      error?: string;
    }>;
    rateLimits: Record<string, any>;
    latency: number;
    errors: string[];
    timestamp: string;
  }> {
    this.validateRequired({ id }, ['id']);
    
    return this.request({
      method: 'POST',
      url: `/providers/${id}/health-check`,
      data: options || {},
    });
  }

  /**
   * Get provider metrics
   */
  async getMetrics(id: string, options?: {
    timeRange?: string;
    modelId?: string;
    includeErrors?: boolean;
    includeCosts?: boolean;
  }): Promise<{
    providerId: string;
    timeRange: string;
    totalExecutions: number;
    successfulExecutions: number;
    failedExecutions: number;
    avgLatency: number;
    successRate: number;
    totalCost: number;
    errors: Array<{
      timestamp: string;
      error: string;
      modelId?: string;
    }>;
    executionsByModel: Record<string, any>;
    costsByModel: Record<string, number>;
    hourlyStats: Array<{
      hour: number;
      executions: number;
      successful: number;
      failed: number;
    }>;
  }> {
    this.validateRequired({ id }, ['id']);
    
    return this.request({
      method: 'GET',
      url: `/providers/${id}/metrics`,
      params: this.buildQueryParams(options || {}),
    });
  }

  /**
   * Get provider statistics
   */
  async getStats(): Promise<{
    total: number;
    executions: number;
    totalCost: number;
    byType: Record<string, number>;
    byStatus: Record<string, number>;
  }> {
    return this.request({
      method: 'GET',
      url: '/providers/stats',
    });
  }

  /**
   * Get overall health summary
   */
  async getHealthSummary(): Promise<{
    totalProviders: number;
    healthyProviders: number;
    unhealthyProviders: number;
    circuitBreakerStats: Record<string, any>;
  }> {
    return this.request({
      method: 'GET',
      url: '/providers/health',
    });
  }

  /**
   * Get circuit breaker statistics
   */
  async getCircuitBreakerStats(): Promise<Record<string, {
    providerId: string;
    state: 'closed' | 'open' | 'half_open';
    config: any;
    metrics: any;
    timings: any;
  }>> {
    return this.request({
      method: 'GET',
      url: '/providers/circuit-breaker',
    });
  }

  /**
   * Reset circuit breaker for a provider
   */
  async resetCircuitBreaker(id: string): Promise<{ message: string }> {
    this.validateRequired({ id }, ['id']);
    
    return this.request({
      method: 'POST',
      url: `/providers/${id}/circuit-breaker/reset`,
    });
  }

  /**
   * Manually open circuit breaker
   */
  async openCircuitBreaker(id: string): Promise<{ message: string }> {
    this.validateRequired({ id }, ['id']);
    
    return this.request({
      method: 'POST',
      url: `/providers/${id}/circuit-breaker/open`,
    });
  }

  /**
   * Manually close circuit breaker
   */
  async closeCircuitBreaker(id: string): Promise<{ message: string }> {
    this.validateRequired({ id }, ['id']);
    
    return this.request({
      method: 'POST',
      url: `/providers/${id}/circuit-breaker/close`,
    });
  }

  /**
   * Test provider connection
   */
  async test(id: string, options?: {
    modelId?: string;
    prompt?: string;
  }): Promise<{
    success: boolean;
    message: string;
    providerId: string;
    modelId?: string;
    testPrompt?: string;
    error?: string;
  }> {
    this.validateRequired({ id }, ['id']);
    
    return this.request({
      method: 'POST',
      url: `/providers/${id}/test`,
      data: options || {},
    });
  }

  /**
   * Validate provider connection
   */
  async validateConnection(id: string): Promise<{
    valid: boolean;
    message: string;
    providerId: string;
    providerName: string;
    providerType: string;
    error?: string;
  }> {
    this.validateRequired({ id }, ['id']);
    
    return this.request({
      method: 'POST',
      url: `/providers/${id}/validate`,
    });
  }

  /**
   * Get provider models
   */
  async getModels(id: string): Promise<{
    providerId: string;
    providerName: string;
    models: any[];
  }> {
    this.validateRequired({ id }, ['id']);
    
    return this.request({
      method: 'GET',
      url: `/providers/${id}/models`,
    });
  }

  /**
   * Test specific model
   */
  async testModel(id: string, modelId: string, prompt?: string): Promise<{
    success: boolean;
    message: string;
    providerId: string;
    modelId: string;
    modelName: string;
    testPrompt: string;
    error?: string;
  }> {
    this.validateRequired({ id, modelId }, ['id', 'modelId']);
    
    return this.request({
      method: 'POST',
      url: `/providers/${id}/models/${modelId}/test`,
      data: { prompt },
    });
  }

  /**
   * Get provider usage
   */
  async getUsage(id: string, days = 30): Promise<{
    providerId: string;
    providerName: string;
    timeRange: string;
    totalExecutions: number;
    totalCost: number;
    totalTokens: number;
    avgCostPerExecution: number;
    executions: Array<{
      id: string;
      cost: number;
      tokens: number;
      modelId?: string;
      executionTime?: number;
      timestamp: string;
    }>;
  }> {
    this.validateRequired({ id }, ['id']);
    
    return this.request({
      method: 'GET',
      url: `/providers/${id}/usage`,
      params: { days },
    });
  }

  /**
   * Bulk health check all providers
   */
  async bulkHealthCheck(): Promise<{
    totalProviders: number;
    results: Array<{
      providerId: string;
      providerName: string;
      success: boolean;
      health?: any;
      error?: string;
    }>;
    summary: {
      healthy: number;
      unhealthy: number;
    };
  }> {
    return this.request({
      method: 'POST',
      url: '/providers/bulk-health-check',
    });
  }

  /**
   * Get cost optimization recommendations
   */
  async getCostOptimizationRecommendations(): Promise<{
    totalRecommendations: number;
    recommendations: Array<{
      type: 'cost_optimization' | 'reliability_improvement';
      providerId: string;
      providerName: string;
      recommendation: string;
      currentCostPerToken?: number;
      potentialSavings?: number;
      alternativeModel?: any;
      currentSuccessRate?: number;
      issue?: string;
    }>;
    summary: {
      costOptimizations: number;
      reliabilityImprovements: number;
    };
  }> {
    return this.request({
      method: 'GET',
      url: '/providers/recommendations/cost-optimization',
    });
  }

  /**
   * Subscribe to provider events
   */
  onProviderEvent(providerId: string, callback: (event: any) => void): string {
    return this.subscribe({
      eventTypes: [
        'provider.created',
        'provider.updated',
        'provider.executed',
        'provider.health_checked',
        'provider.deleted'
      ],
      organizationId: this.getOrganizationId(),
      callback: (event) => {
        if (event.payload.providerId === providerId) {
          callback(event);
        }
      },
    });
  }

  /**
   * Subscribe to all provider events
   */
  onAllProviderEvents(callback: (event: any) => void): string {
    return this.subscribe({
      eventTypes: [
        'provider.created',
        'provider.updated',
        'provider.executed',
        'provider.execution_completed',
        'provider.execution_failed',
        'provider.health_checked',
        'provider.deleted'
      ],
      organizationId: this.getOrganizationId(),
      callback,
    });
  }

  /**
   * Monitor provider health in real-time
   */
  async monitorHealth(callback: (status: {
    providerId: string;
    providerName: string;
    status: 'healthy' | 'unhealthy' | 'maintenance';
    details?: any;
  }) => void): Promise<string> {
    return this.subscribe({
      eventTypes: ['provider.health_checked', 'provider.execution_failed'],
      organizationId: this.getOrganizationId(),
      callback: (event) => {
        const status: any = {
          providerId: event.payload.providerId,
          providerName: event.payload.providerName || 'Unknown',
        };

        if (event.type === 'provider.health_checked') {
          status.status = event.payload.status === 'active' ? 'healthy' : 'unhealthy';
          status.details = event.payload;
        } else if (event.type === 'provider.execution_failed') {
          status.status = 'unhealthy';
          status.details = { error: event.payload.error };
        }

        callback(status);
      },
    });
  }
}