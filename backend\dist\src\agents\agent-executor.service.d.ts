import { PrismaService } from '../prisma/prisma.service';
import { APIMXService } from '../apix/apix.service';
import { SessionsService } from '../sessions/sessions.service';
import { ExecuteAgentDto } from './dto/agent.dto';
import { ConfigService } from '@nestjs/config';
export interface ExecutionResult {
    success: boolean;
    output: string;
    usage?: {
        promptTokens: number;
        completionTokens: number;
        totalTokens: number;
    };
    executionTime: number;
    sessionId: string;
    error?: string;
}
export interface StreamingChunk {
    type: 'start' | 'chunk' | 'end' | 'error';
    content?: string;
    usage?: any;
    sessionId?: string;
    error?: string;
}
export declare class AgentExecutorService {
    private prisma;
    private apixService;
    private sessionsService;
    private configService;
    private openai;
    constructor(prisma: PrismaService, apixService: APIMXService, sessionsService: SessionsService, configService: ConfigService);
    executeAgent(agentId: string, organizationId: string, userId: string, executeDto: ExecuteAgentDto): Promise<ExecutionResult>;
    executeAgentStreaming(agentId: string, organizationId: string, userId: string, executeDto: ExecuteAgentDto): AsyncGenerator<StreamingChunk, void, unknown>;
    private prepareMessages;
    private executeWithProvider;
    private trackUsage;
    private calculateCost;
    getExecutionHistory(agentId: string, organizationId: string, limit?: number): Promise<any>;
    getExecutionMetrics(agentId: string, organizationId: string): Promise<{
        totalExecutions: any;
        avgTokensPerExecution: any;
        totalTokensUsed: any;
        lastExecutedAt: any;
    }>;
}
