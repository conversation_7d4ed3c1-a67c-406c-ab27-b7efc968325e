{"version": 3, "file": "sessions.service.js", "sourceRoot": "", "sources": ["../../../src/sessions/sessions.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,2CAAwG;AACxG,6DAAyD;AACzD,uDAAoD;AAK7C,IAAM,eAAe,GAArB,MAAM,eAAe;IAC1B,YACU,MAAqB,EACrB,WAAyB;QADzB,WAAM,GAAN,MAAM,CAAe;QACrB,gBAAW,GAAX,WAAW,CAAc;IAChC,CAAC;IAEJ,KAAK,CAAC,MAAM,CAAC,MAAc,EAAE,cAAsB,EAAE,gBAAkC;QACrF,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,QAAQ,GAAG,EAAE,EAAE,GAAG,gBAAgB,CAAC;QAE3D,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC;YAC/C,IAAI,EAAE;gBACJ,MAAM;gBACN,cAAc;gBACd,IAAI;gBACJ,QAAQ,EAAE;oBACR,QAAQ;oBACR,GAAG,QAAQ;iBACZ;gBACD,MAAM,EAAE;oBACN,QAAQ,EAAE,EAAE;oBACZ,OAAO,EAAE,EAAE;oBACX,UAAU,EAAE,CAAC;oBACb,SAAS,EAAE,IAAI;iBAChB;gBACD,MAAM,EAAE,QAAQ;aACjB;SACF,CAAC,CAAC;QAEH,MAAM,IAAI,CAAC,WAAW,CAAC,mBAAmB,CACxC,SAAS,EACT,OAAO,CAAC,EAAE,EACV,cAAc,EACd,MAAM,EACN;YACE,IAAI,EAAE,OAAO,CAAC,IAAI;YAClB,QAAQ;SACT,CACF,CAAC;QAEF,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,KAAK,CAAC,OAAO,CAAC,cAAsB,EAAE,KAAsB;QAC1D,MAAM,EACJ,IAAI,GAAG,CAAC,EACR,KAAK,GAAG,EAAE,EACV,IAAI,EACJ,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,GAAG,WAAW,EACpB,SAAS,GAAG,MAAM,GACnB,GAAG,KAAK,CAAC;QAEV,MAAM,IAAI,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC;QAEhC,MAAM,KAAK,GAAQ;YACjB,cAAc;YACd,GAAG,CAAC,IAAI,IAAI,EAAE,IAAI,EAAE,CAAC;YACrB,GAAG,CAAC,MAAM,IAAI,EAAE,MAAM,EAAE,CAAC;YACzB,GAAG,CAAC,MAAM,IAAI,EAAE,MAAM,EAAE,CAAC;SAC1B,CAAC;QAEF,IAAI,MAAM,EAAE,CAAC;YACX,KAAK,CAAC,EAAE,GAAG;gBACT,EAAE,EAAE,EAAE,EAAE,QAAQ,EAAE,MAAM,EAAE,IAAI,EAAE,aAAa,EAAE,EAAE;gBACjD,EAAE,QAAQ,EAAE,EAAE,IAAI,EAAE,CAAC,UAAU,CAAC,EAAE,eAAe,EAAE,MAAM,EAAE,EAAE;aAC9D,CAAC;QACJ,CAAC;QAED,MAAM,CAAC,QAAQ,EAAE,KAAK,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;YAC1C,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC;gBAC3B,KAAK;gBACL,OAAO,EAAE;oBACP,IAAI,EAAE;wBACJ,MAAM,EAAE;4BACN,EAAE,EAAE,IAAI;4BACR,IAAI,EAAE,IAAI;4BACV,KAAK,EAAE,IAAI;4BACX,SAAS,EAAE,IAAI;yBAChB;qBACF;iBACF;gBACD,OAAO,EAAE,EAAE,CAAC,MAAM,CAAC,EAAE,SAAS,EAAE;gBAChC,IAAI;gBACJ,IAAI,EAAE,KAAK;aACZ,CAAC;YACF,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,CAAC;SACrC,CAAC,CAAC;QAEH,OAAO;YACL,QAAQ;YACR,UAAU,EAAE;gBACV,IAAI;gBACJ,KAAK;gBACL,KAAK;gBACL,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;aAChC;SACF,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,OAAO,CAAC,EAAU,EAAE,cAAsB,EAAE,MAAe;QAC/D,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC;YACnD,KAAK,EAAE,EAAE,EAAE,EAAE;YACb,OAAO,EAAE;gBACP,IAAI,EAAE;oBACJ,MAAM,EAAE;wBACN,EAAE,EAAE,IAAI;wBACR,IAAI,EAAE,IAAI;wBACV,KAAK,EAAE,IAAI;wBACX,SAAS,EAAE,IAAI;qBAChB;iBACF;aACF;SACF,CAAC,CAAC;QAEH,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,MAAM,IAAI,0BAAiB,CAAC,mBAAmB,CAAC,CAAC;QACnD,CAAC;QAED,IAAI,OAAO,CAAC,cAAc,KAAK,cAAc,EAAE,CAAC;YAC9C,MAAM,IAAI,2BAAkB,CAAC,eAAe,CAAC,CAAC;QAChD,CAAC;QAED,IAAI,MAAM,IAAI,OAAO,CAAC,MAAM,KAAK,MAAM,EAAE,CAAC;YACxC,MAAM,IAAI,2BAAkB,CAAC,eAAe,CAAC,CAAC;QAChD,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,KAAK,CAAC,MAAM,CAAC,EAAU,EAAE,cAAsB,EAAE,MAAc,EAAE,gBAAkC;QACjG,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,EAAE,EAAE,cAAc,EAAE,MAAM,CAAC,CAAC;QAE/D,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC;YACtD,KAAK,EAAE,EAAE,EAAE,EAAE;YACb,IAAI,EAAE;gBACJ,GAAG,gBAAgB;gBACnB,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB;SACF,CAAC,CAAC;QAEH,MAAM,IAAI,CAAC,WAAW,CAAC,mBAAmB,CACxC,SAAS,EACT,OAAO,CAAC,EAAE,EACV,cAAc,EACd,MAAM,EACN;YACE,OAAO,EAAE,gBAAgB;SAC1B,CACF,CAAC;QAEF,OAAO,cAAc,CAAC;IACxB,CAAC;IAED,KAAK,CAAC,MAAM,CAAC,EAAU,EAAE,cAAsB,EAAE,MAAc;QAC7D,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,EAAE,EAAE,cAAc,EAAE,MAAM,CAAC,CAAC;QAE/D,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC;YAC/B,KAAK,EAAE,EAAE,EAAE,EAAE;SACd,CAAC,CAAC;QAEH,MAAM,IAAI,CAAC,WAAW,CAAC,mBAAmB,CACxC,SAAS,EACT,OAAO,CAAC,EAAE,EACV,cAAc,EACd,MAAM,EACN,EAAE,CACH,CAAC;QAEF,OAAO,EAAE,OAAO,EAAE,8BAA8B,EAAE,CAAC;IACrD,CAAC;IAED,KAAK,CAAC,UAAU,CAAC,SAAiB,EAAE,cAAsB,EAAE,MAAc,EAAE,OAAY;QACtF,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,cAAc,EAAE,MAAM,CAAC,CAAC;QAEtE,MAAM,aAAa,GAAG,OAAO,CAAC,MAAa,CAAC;QAC5C,MAAM,QAAQ,GAAG,aAAa,CAAC,QAAQ,IAAI,EAAE,CAAC;QAE9C,MAAM,UAAU,GAAG;YACjB,EAAE,EAAE,OAAO,IAAI,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE;YAClE,IAAI,EAAE,OAAO,CAAC,IAAI;YAClB,OAAO,EAAE,OAAO,CAAC,OAAO;YACxB,QAAQ,EAAE,OAAO,CAAC,QAAQ,IAAI,EAAE;YAChC,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC;QAEF,MAAM,UAAU,GAAG,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;QAC5D,MAAM,aAAa,GAAG,aAAa,CAAC,UAAU,GAAG,UAAU,CAAC;QAE5D,MAAM,eAAe,GAAG,CAAC,GAAG,QAAQ,EAAE,UAAU,CAAC,CAAC;QAElD,IAAI,aAAa,GAAG,aAAa,CAAC,SAAS,EAAE,CAAC;YAC5C,MAAM,iBAAiB,GAAG,IAAI,CAAC,gBAAgB,CAAC,eAAe,EAAE,aAAa,CAAC,SAAS,CAAC,CAAC;YAC1F,eAAe,CAAC,MAAM,GAAG,CAAC,CAAC;YAC3B,eAAe,CAAC,IAAI,CAAC,GAAG,iBAAiB,CAAC,CAAC;QAC7C,CAAC;QAED,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC;YACtD,KAAK,EAAE,EAAE,EAAE,EAAE,SAAS,EAAE;YACxB,IAAI,EAAE;gBACJ,MAAM,EAAE;oBACN,GAAG,aAAa;oBAChB,QAAQ,EAAE,eAAe;oBACzB,UAAU,EAAE,IAAI,CAAC,oBAAoB,CAAC,eAAe,CAAC;iBACvD;gBACD,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB;SACF,CAAC,CAAC;QAEH,MAAM,IAAI,CAAC,WAAW,CAAC,mBAAmB,CACxC,eAAe,EACf,SAAS,EACT,cAAc,EACd,MAAM,EACN;YACE,OAAO,EAAE,UAAU;SACpB,CACF,CAAC;QAEF,OAAO,cAAc,CAAC;IACxB,CAAC;IAED,KAAK,CAAC,aAAa,CAAC,SAAiB,EAAE,cAAsB,EAAE,MAAc,EAAE,OAAY;QACzF,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,cAAc,EAAE,MAAM,CAAC,CAAC;QAEtE,MAAM,aAAa,GAAG,OAAO,CAAC,MAAa,CAAC;QAE5C,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC;YACtD,KAAK,EAAE,EAAE,EAAE,EAAE,SAAS,EAAE;YACxB,IAAI,EAAE;gBACJ,MAAM,EAAE;oBACN,GAAG,aAAa;oBAChB,OAAO,EAAE;wBACP,GAAG,aAAa,CAAC,OAAO;wBACxB,GAAG,OAAO;qBACX;iBACF;gBACD,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB;SACF,CAAC,CAAC;QAEH,MAAM,IAAI,CAAC,WAAW,CAAC,mBAAmB,CACxC,iBAAiB,EACjB,SAAS,EACT,cAAc,EACd,MAAM,EACN;YACE,OAAO;SACR,CACF,CAAC;QAEF,OAAO,cAAc,CAAC;IACxB,CAAC;IAED,KAAK,CAAC,WAAW,CAAC,SAAiB,EAAE,cAAsB,EAAE,MAAc;QACzE,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,cAAc,EAAE,MAAM,CAAC,CAAC;QAEtE,MAAM,aAAa,GAAG,OAAO,CAAC,MAAa,CAAC;QAE5C,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC;YACtD,KAAK,EAAE,EAAE,EAAE,EAAE,SAAS,EAAE;YACxB,IAAI,EAAE;gBACJ,MAAM,EAAE;oBACN,GAAG,aAAa;oBAChB,QAAQ,EAAE,EAAE;oBACZ,OAAO,EAAE,EAAE;oBACX,UAAU,EAAE,CAAC;iBACd;gBACD,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB;SACF,CAAC,CAAC;QAEH,MAAM,IAAI,CAAC,WAAW,CAAC,mBAAmB,CACxC,gBAAgB,EAChB,SAAS,EACT,cAAc,EACd,MAAM,EACN,EAAE,CACH,CAAC;QAEF,OAAO,cAAc,CAAC;IACxB,CAAC;IAED,KAAK,CAAC,eAAe,CAAC,cAAsB,EAAE,MAAe;QAC3D,MAAM,KAAK,GAAQ,EAAE,cAAc,EAAE,CAAC;QACtC,IAAI,MAAM;YAAE,KAAK,CAAC,MAAM,GAAG,MAAM,CAAC;QAElC,MAAM,CAAC,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;YAC1D,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,CAAC;YACpC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,EAAE,GAAG,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,EAAE,CAAC;YACpE,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC;gBAC1B,EAAE,EAAE,CAAC,MAAM,CAAC;gBACZ,KAAK;gBACL,MAAM,EAAE,IAAI;aACb,CAAC;YACF,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC;gBAC1B,EAAE,EAAE,CAAC,QAAQ,CAAC;gBACd,KAAK;gBACL,MAAM,EAAE,IAAI;aACb,CAAC;SACH,CAAC,CAAC;QAEH,OAAO;YACL,KAAK;YACL,MAAM;YACN,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE;gBAClC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC;gBAC7B,OAAO,GAAG,CAAC;YACb,CAAC,EAAE,EAAE,CAAC;YACN,QAAQ,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE;gBACtC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC;gBAC/B,OAAO,GAAG,CAAC;YACb,CAAC,EAAE,EAAE,CAAC;SACP,CAAC;IACJ,CAAC;IAEO,kBAAkB,CAAC,IAAY;QACrC,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;IACpC,CAAC;IAEO,oBAAoB,CAAC,QAAe;QAC1C,OAAO,QAAQ,CAAC,MAAM,CAAC,CAAC,KAAK,EAAE,OAAO,EAAE,EAAE;YACxC,OAAO,KAAK,GAAG,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;QAC1D,CAAC,EAAE,CAAC,CAAC,CAAC;IACR,CAAC;IAEO,gBAAgB,CAAC,QAAe,EAAE,SAAiB;QACzD,IAAI,WAAW,GAAG,CAAC,CAAC;QACpB,MAAM,MAAM,GAAG,EAAE,CAAC;QAElB,KAAK,IAAI,CAAC,GAAG,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;YAC9C,MAAM,aAAa,GAAG,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;YACnE,IAAI,WAAW,GAAG,aAAa,GAAG,SAAS,EAAE,CAAC;gBAC5C,MAAM;YACR,CAAC;YACD,WAAW,IAAI,aAAa,CAAC;YAC7B,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9B,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;CACF,CAAA;AAtVY,0CAAe;0BAAf,eAAe;IAD3B,IAAA,mBAAU,GAAE;qCAGO,8BAAa;QACR,2BAAY;GAHxB,eAAe,CAsV3B"}