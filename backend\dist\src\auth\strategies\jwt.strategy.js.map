{"version": 3, "file": "jwt.strategy.js", "sourceRoot": "", "sources": ["../../../../src/auth/strategies/jwt.strategy.ts"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA,2CAAmE;AACnE,+CAAoD;AACpD,+CAAoD;AACpD,2CAA+C;AAE/C,gEAA4D;AAGrD,IAAM,WAAW,GAAjB,MAAM,WAAY,SAAQ,IAAA,2BAAgB,EAAC,uBAAQ,CAAC;IACzD,YACU,aAA4B,EAC5B,MAAqB;QAE7B,KAAK,CAAC;YACJ,cAAc,EAAE,yBAAU,CAAC,2BAA2B,EAAE;YACxD,gBAAgB,EAAE,KAAK;YACvB,WAAW,EAAE,aAAa,CAAC,GAAG,CAAS,YAAY,CAAC;SACrD,CAAC,CAAC;QAPK,kBAAa,GAAb,aAAa,CAAe;QAC5B,WAAM,GAAN,MAAM,CAAe;IAO/B,CAAC;IAED,KAAK,CAAC,QAAQ,CAAC,OAAY;QACzB,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;YAC7C,KAAK,EAAE,EAAE,EAAE,EAAE,OAAO,CAAC,GAAG,EAAE;YAC1B,OAAO,EAAE;gBACP,YAAY,EAAE,IAAI;gBAClB,SAAS,EAAE;oBACT,OAAO,EAAE;wBACP,IAAI,EAAE,IAAI;qBACX;iBACF;aACF;SACF,CAAC,CAAC;QAEH,IAAI,CAAC,IAAI,EAAE,CAAC;YACV,MAAM,IAAI,8BAAqB,CAAC,gBAAgB,CAAC,CAAC;QACpD,CAAC;QAED,MAAM,EAAE,YAAY,EAAE,GAAG,mBAAmB,EAAE,GAAG,IAAI,CAAC;QACtD,OAAO,mBAAmB,CAAC;IAC7B,CAAC;CACF,CAAA;AAhCY,kCAAW;sBAAX,WAAW;IADvB,IAAA,mBAAU,GAAE;yDAGc,sBAAa,oBAAb,sBAAa,gCACpB,8BAAa;GAHpB,WAAW,CAgCvB"}