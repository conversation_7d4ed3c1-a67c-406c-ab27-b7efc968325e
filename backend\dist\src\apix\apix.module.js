"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.APIMXModule = void 0;
const common_1 = require("@nestjs/common");
const apix_gateway_1 = require("./apix.gateway");
const apix_service_1 = require("./apix.service");
const auth_module_1 = require("../auth/auth.module");
let APIMXModule = class APIMXModule {
};
exports.APIMXModule = APIMXModule;
exports.APIMXModule = APIMXModule = __decorate([
    (0, common_1.Module)({
        imports: [auth_module_1.AuthModule],
        providers: [apix_gateway_1.APIMXGateway, apix_service_1.APIMXService],
        exports: [apix_service_1.APIMXService],
    })
], APIMXModule);
//# sourceMappingURL=apix.module.js.map