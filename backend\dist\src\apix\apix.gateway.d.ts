import { OnGatewayConnection, OnGatewayDisconnect } from '@nestjs/websockets';
import { Server, Socket } from 'socket.io';
import { JwtService } from '@nestjs/jwt';
import { APIMXEvent } from './apix.service';
interface AuthenticatedSocket extends Socket {
    userId?: string;
    organizationId?: string;
    userRoles?: string[];
}
export declare class APIMXGateway implements OnGatewayConnection, OnGatewayDisconnect {
    private jwtService;
    server: Server;
    private readonly logger;
    private connectedClients;
    private userSockets;
    private organizationSockets;
    private sessionSockets;
    constructor(jwtService: JwtService);
    handleConnection(client: AuthenticatedSocket): Promise<void>;
    handleDisconnect(client: AuthenticatedSocket): Promise<void>;
    handleJoinSession(client: AuthenticatedSocket, data: {
        sessionId: string;
    }): Promise<void>;
    handleLeaveSession(client: AuthenticatedSocket, data: {
        sessionId: string;
    }): Promise<void>;
    handlePing(client: AuthenticatedSocket): void;
    broadcastToOrganization(organizationId: string, event: APIMXEvent): void;
    broadcastToUser(userId: string, event: APIMXEvent): void;
    broadcastToSession(sessionId: string, event: APIMXEvent): void;
    broadcastToAll(event: APIMXEvent): void;
    getConnectedClientsCount(): number;
    getOrganizationClientsCount(organizationId: string): number;
    getUserClientsCount(userId: string): number;
    isUserOnline(userId: string): boolean;
}
export {};
