import { OrganizationsService } from './organizations.service';
export declare class OrganizationsController {
    private readonly organizationsService;
    constructor(organizationsService: OrganizationsService);
    getCurrent(organizationId: string): Promise<any>;
    updateSettings(organizationId: string, settings: any): Promise<any>;
    getQuotas(organizationId: string): Promise<any>;
    updateQuota(organizationId: string, body: {
        limit: number;
    }): Promise<any>;
    getUsage(organizationId: string, type?: string, from?: string, to?: string): Promise<any>;
}
