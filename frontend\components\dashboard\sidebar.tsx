'use client';

import { Fragment } from 'react';
import { Dialog, Transition } from '@headlessui/react';
import { usePathname } from 'next/navigation';
import Link from 'next/link';
import { useAuth } from '@/lib/auth-context';
import { useOrganization } from '@/lib/organization-context';
import {
  X,
  Home,
  Bot,
  Wrench,
  Workflow,
  Users,
  BookOpen,
  BarChart3,
  Widget,
  Settings,
  CreditCard,
  Zap,
  TestTube,
} from 'lucide-react';
import clsx from 'clsx';

interface SidebarProps {
  open: boolean;
  setOpen: (open: boolean) => void;
}

export function DashboardSidebar({ open, setOpen }: SidebarProps) {
  const pathname = usePathname();
  const { user, hasPermission } = useAuth();
  const { organization, isFeatureEnabled } = useOrganization();

  const navigation = [
    {
      name: 'Dashboard',
      href: '/dashboard',
      icon: Home,
      current: pathname === '/dashboard',
      permissions: [],
    },
    {
      name: 'Agents',
      href: '/dashboard/agents',
      icon: Bo<PERSON>,
      current: pathname.startsWith('/dashboard/agents'),
      permissions: ['agents:read'],
      feature: 'agents',
    },
    {
      name: 'Tools',
      href: '/dashboard/tools',
      icon: Wrench,
      current: pathname.startsWith('/dashboard/tools'),
      permissions: ['tools:read'],
      feature: 'tools',
    },
    {
      name: 'Workflows',
      href: '/dashboard/workflows',
      icon: Workflow,
      current: pathname.startsWith('/dashboard/workflows'),
      permissions: ['sessions:read'],
      feature: 'hybrids',
    },
    {
      name: 'HITL Requests',
      href: '/dashboard/hitl',
      icon: Users,
      current: pathname.startsWith('/dashboard/hitl'),
      permissions: ['hitl:read'],
      feature: 'hitl',
    },
    {
      name: 'Knowledge Base',
      href: '/dashboard/knowledge',
      icon: BookOpen,
      current: pathname.startsWith('/dashboard/knowledge'),
      permissions: ['knowledge:read'],
      feature: 'knowledge',
    },
    {
      name: 'Analytics',
      href: '/dashboard/analytics',
      icon: BarChart3,
      current: pathname.startsWith('/dashboard/analytics'),
      permissions: ['analytics:read'],
    },
    {
      name: 'Widgets',
      href: '/dashboard/widgets',
      icon: Widget,
      current: pathname.startsWith('/dashboard/widgets'),
      permissions: ['widgets:read'],
      feature: 'widgets',
    },
    {
      name: 'Sandbox',
      href: '/dashboard/sandbox',
      icon: TestTube,
      current: pathname.startsWith('/dashboard/sandbox'),
      permissions: ['agents:create', 'tools:create'],
    },
  ];

  const bottomNavigation = [
    {
      name: 'Billing',
      href: '/dashboard/billing',
      icon: CreditCard,
      current: pathname.startsWith('/dashboard/billing'),
      permissions: ['billing:read'],
    },
    {
      name: 'Settings',
      href: '/dashboard/settings',
      icon: Settings,
      current: pathname.startsWith('/dashboard/settings'),
      permissions: [],
    },
  ];

  const filteredNavigation = navigation.filter(item => {
    if (item.permissions.length > 0 && !item.permissions.some(permission => hasPermission(permission))) {
      return false;
    }
    if (item.feature && !isFeatureEnabled(item.feature)) {
      return false;
    }
    return true;
  });

  const filteredBottomNavigation = bottomNavigation.filter(item => {
    if (item.permissions.length > 0 && !item.permissions.some(permission => hasPermission(permission))) {
      return false;
    }
    return true;
  });

  const SidebarContent = () => (
    <div className="flex h-full flex-col">
      {/* Logo */}
      <div className="flex h-16 shrink-0 items-center border-b border-gray-200 dark:border-gray-700 px-6">
        <div className="flex items-center space-x-3">
          <div className="h-8 w-8 flex items-center justify-center rounded-lg bg-primary-600">
            <Zap className="h-5 w-5 text-white" />
          </div>
          <div>
            <h1 className="text-lg font-semibold text-gray-900 dark:text-white">
              SynapseAI
            </h1>
            {organization && (
              <p className="text-xs text-gray-500 dark:text-gray-400">
                {organization.name}
              </p>
            )}
          </div>
        </div>
      </div>

      {/* Navigation */}
      <nav className="flex flex-1 flex-col px-6 py-6">
        <ul role="list" className="flex flex-1 flex-col gap-y-7">
          <li>
            <ul role="list" className="-mx-2 space-y-1">
              {filteredNavigation.map((item) => (
                <li key={item.name}>
                  <Link
                    href={item.href}
                    onClick={() => setOpen(false)}
                    className={clsx(
                      item.current
                        ? 'sidebar-link-active'
                        : 'sidebar-link-inactive',
                      'group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold'
                    )}
                  >
                    <item.icon
                      className={clsx(
                        item.current
                          ? 'text-primary-600 dark:text-primary-400'
                          : 'text-gray-400 group-hover:text-gray-600 dark:group-hover:text-gray-300',
                        'h-6 w-6 shrink-0'
                      )}
                      aria-hidden="true"
                    />
                    {item.name}
                  </Link>
                </li>
              ))}
            </ul>
          </li>

          {/* Bottom navigation */}
          <li className="-mx-6 mt-auto">
            <div className="border-t border-gray-200 dark:border-gray-700 pt-6 px-6">
              <ul role="list" className="-mx-2 space-y-1">
                {filteredBottomNavigation.map((item) => (
                  <li key={item.name}>
                    <Link
                      href={item.href}
                      onClick={() => setOpen(false)}
                      className={clsx(
                        item.current
                          ? 'sidebar-link-active'
                          : 'sidebar-link-inactive',
                        'group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold'
                      )}
                    >
                      <item.icon
                        className={clsx(
                          item.current
                            ? 'text-primary-600 dark:text-primary-400'
                            : 'text-gray-400 group-hover:text-gray-600 dark:group-hover:text-gray-300',
                          'h-6 w-6 shrink-0'
                        )}
                        aria-hidden="true"
                      />
                      {item.name}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
          </li>

          {/* User info */}
          <li className="-mx-6">
            <div className="border-t border-gray-200 dark:border-gray-700 pt-6 px-6">
              <div className="flex items-center gap-x-4">
                <div className="h-8 w-8 rounded-full bg-primary-100 dark:bg-primary-900 flex items-center justify-center">
                  <span className="text-sm font-medium text-primary-600 dark:text-primary-400">
                    {user?.name?.[0]?.toUpperCase()}
                  </span>
                </div>
                <div className="flex-1 text-sm leading-6">
                  <p className="font-semibold text-gray-900 dark:text-white">
                    {user?.name}
                  </p>
                  <p className="text-gray-400 dark:text-gray-500">
                    {user?.email}
                  </p>
                </div>
              </div>
            </div>
          </li>
        </ul>
      </nav>
    </div>
  );

  return (
    <>
      {/* Mobile sidebar */}
      <Transition.Root show={open} as={Fragment}>
        <Dialog as="div" className="relative z-50 lg:hidden" onClose={setOpen}>
          <Transition.Child
            as={Fragment}
            enter="transition-opacity ease-linear duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="transition-opacity ease-linear duration-300"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-gray-900/80" />
          </Transition.Child>

          <div className="fixed inset-0 flex">
            <Transition.Child
              as={Fragment}
              enter="transition ease-in-out duration-300 transform"
              enterFrom="-translate-x-full"
              enterTo="translate-x-0"
              leave="transition ease-in-out duration-300 transform"
              leaveFrom="translate-x-0"
              leaveTo="-translate-x-full"
            >
              <Dialog.Panel className="relative mr-16 flex w-full max-w-xs flex-1">
                <Transition.Child
                  as={Fragment}
                  enter="ease-in-out duration-300"
                  enterFrom="opacity-0"
                  enterTo="opacity-100"
                  leave="ease-in-out duration-300"
                  leaveFrom="opacity-100"
                  leaveTo="opacity-0"
                >
                  <div className="absolute left-full top-0 flex w-16 justify-center pt-5">
                    <button
                      type="button"
                      className="-m-2.5 p-2.5"
                      onClick={() => setOpen(false)}
                    >
                      <span className="sr-only">Close sidebar</span>
                      <X className="h-6 w-6 text-white" aria-hidden="true" />
                    </button>
                  </div>
                </Transition.Child>
                
                <div className="flex grow flex-col gap-y-5 overflow-y-auto bg-white dark:bg-gray-800 pb-2">
                  <SidebarContent />
                </div>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </Dialog>
      </Transition.Root>

      {/* Static sidebar for desktop */}
      <div className="hidden lg:fixed lg:inset-y-0 lg:z-50 lg:flex lg:w-72 lg:flex-col">
        <div className="flex grow flex-col gap-y-5 overflow-y-auto border-r border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 pb-4">
          <SidebarContent />
        </div>
      </div>
    </>
  );
}