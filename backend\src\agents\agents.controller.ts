import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  Res,
  Sse,
} from '@nestjs/common';
import { Response } from 'express';
import { Observable, fromEvent, map } from 'rxjs';
import { AgentsService } from './agents.service';
import { AgentExecutorService } from './agent-executor.service';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { OrganizationGuard } from '../common/guards/organization.guard';
import { CurrentUser, CurrentOrganization } from '../common/decorators/current-user.decorator';
import {
  CreateAgentDto,
  UpdateAgentDto,
  AgentQueryDto,
  ExecuteAgentDto,
  AgentVersionDto,
  CloneAgentDto,
} from './dto/agent.dto';

@Controller('agents')
@UseGuards(JwtAuthGuard, OrganizationGuard)
export class AgentsController {
  constructor(
    private readonly agentsService: AgentsService,
    private readonly agentExecutorService: AgentExecutorService,
  ) {}

  @Post()
  create(
    @CurrentUser('id') userId: string,
    @CurrentOrganization('id') organizationId: string,
    @Body() createAgentDto: CreateAgentDto,
  ) {
    return this.agentsService.create(userId, organizationId, createAgentDto);
  }

  @Get()
  findAll(
    @CurrentOrganization('id') organizationId: string,
    @Query() query: AgentQueryDto,
  ) {
    return this.agentsService.findAll(organizationId, query);
  }

  @Get('stats')
  getStats(@CurrentOrganization('id') organizationId: string) {
    return this.agentsService.getAgentStats(organizationId);
  }

  @Get('models/popular')
  getPopularModels(@CurrentOrganization('id') organizationId: string) {
    return this.agentsService.getPopularModels(organizationId);
  }

  @Get('search/tags')
  searchByTags(
    @CurrentOrganization('id') organizationId: string,
    @Query('tags') tags: string[],
  ) {
    const tagArray = Array.isArray(tags) ? tags : [tags];
    return this.agentsService.searchAgentsByTags(organizationId, tagArray);
  }

  @Get(':id')
  findOne(
    @Param('id') id: string,
    @CurrentOrganization('id') organizationId: string,
  ) {
    return this.agentsService.findOne(id, organizationId);
  }

  @Patch(':id')
  update(
    @Param('id') id: string,
    @CurrentOrganization('id') organizationId: string,
    @Body() updateAgentDto: UpdateAgentDto,
  ) {
    return this.agentsService.update(id, organizationId, updateAgentDto);
  }

  @Delete(':id')
  remove(
    @Param('id') id: string,
    @CurrentOrganization('id') organizationId: string,
  ) {
    return this.agentsService.remove(id, organizationId);
  }

  @Post(':id/execute')
  async executeAgent(
    @Param('id') id: string,
    @CurrentUser('id') userId: string,
    @CurrentOrganization('id') organizationId: string,
    @Body() executeDto: ExecuteAgentDto,
    @Res() res: Response,
  ) {
    if (executeDto.streaming) {
      res.setHeader('Content-Type', 'text/event-stream');
      res.setHeader('Cache-Control', 'no-cache');
      res.setHeader('Connection', 'keep-alive');
      res.setHeader('Access-Control-Allow-Origin', '*');

      const stream = this.agentExecutorService.executeAgentStreaming(
        id,
        organizationId,
        userId,
        executeDto,
      );

      for await (const chunk of stream) {
        res.write(`data: ${JSON.stringify(chunk)}\n\n`);
      }

      res.end();
    } else {
      const result = await this.agentExecutorService.executeAgent(
        id,
        organizationId,
        userId,
        executeDto,
      );
      res.json(result);
    }
  }

  @Post(':id/versions')
  createVersion(
    @Param('id') id: string,
    @CurrentOrganization('id') organizationId: string,
    @Body() versionDto: AgentVersionDto,
  ) {
    return this.agentsService.createVersion(id, organizationId, versionDto);
  }

  @Post(':id/rollback/:version')
  rollbackToVersion(
    @Param('id') id: string,
    @Param('version') version: string,
    @CurrentOrganization('id') organizationId: string,
  ) {
    return this.agentsService.rollbackToVersion(id, organizationId, version);
  }

  @Post(':id/clone')
  clone(
    @Param('id') id: string,
    @CurrentOrganization('id') organizationId: string,
    @Body() cloneDto: CloneAgentDto,
  ) {
    return this.agentsService.clone(id, organizationId, cloneDto);
  }

  @Get(':id/executions')
  getExecutionHistory(
    @Param('id') id: string,
    @CurrentOrganization('id') organizationId: string,
    @Query('limit') limit?: number,
  ) {
    return this.agentExecutorService.getExecutionHistory(
      id,
      organizationId,
      limit ? parseInt(limit.toString()) : 50,
    );
  }

  @Get(':id/metrics')
  getExecutionMetrics(
    @Param('id') id: string,
    @CurrentOrganization('id') organizationId: string,
  ) {
    return this.agentExecutorService.getExecutionMetrics(id, organizationId);
  }

  @Post(':id/test')
  async testAgent(
    @Param('id') id: string,
    @CurrentUser('id') userId: string,
    @CurrentOrganization('id') organizationId: string,
    @Body() testDto: { input: string; context?: any },
  ) {
    return this.agentExecutorService.executeAgent(id, organizationId, userId, {
      ...testDto,
      streaming: false,
    });
  }
}