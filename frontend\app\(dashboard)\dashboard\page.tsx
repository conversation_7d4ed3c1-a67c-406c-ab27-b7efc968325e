'use client';

import { useAuth } from '@/lib/auth-context';
import { useOrganization } from '@/lib/organization-context';
import { useAPIMX } from '@/lib/apix-context';
import { 
  Bot, 
  Wrench, 
  Workflow, 
  Activity, 
  Users, 
  BarChart3,
  TrendingUp,
  Clock,
  Zap,
  AlertTriangle,
} from 'lucide-react';
import Link from 'next/link';

export default function DashboardPage() {
  const { user } = useAuth();
  const { organization, quotas, checkQuota } = useOrganization();
  const { connected, connectionStatus } = useAPIMX();

  const stats = [
    {
      name: 'Total Agents',
      value: '12',
      change: '+2.1%',
      changeType: 'positive',
      icon: Bot,
      href: '/dashboard/agents',
    },
    {
      name: 'Active Tools',
      value: '8',
      change: '+5.4%',
      changeType: 'positive',
      icon: Wrench,
      href: '/dashboard/tools',
    },
    {
      name: 'Workflows Run',
      value: '147',
      change: '+12.3%',
      changeType: 'positive',
      icon: Workflow,
      href: '/dashboard/workflows',
    },
    {
      name: 'API Calls Today',
      value: '2,847',
      change: '-0.4%',
      changeType: 'negative',
      icon: Activity,
      href: '/dashboard/analytics',
    },
  ];

  const recentActivity = [
    {
      id: 1,
      type: 'agent_execution',
      title: 'Customer Support Agent completed task',
      description: 'Resolved customer inquiry #CS-4829',
      timestamp: '2 minutes ago',
      status: 'completed',
    },
    {
      id: 2,
      type: 'tool_execution',
      title: 'Email Tool sent notification',
      description: 'Welcome email sent to new user',
      timestamp: '5 minutes ago',
      status: 'completed',
    },
    {
      id: 3,
      type: 'workflow_started',
      title: 'Lead Processing Workflow initiated',
      description: 'Processing new lead from website form',
      timestamp: '8 minutes ago',
      status: 'in_progress',
    },
    {
      id: 4,
      type: 'hitl_request',
      title: 'Human approval requested',
      description: 'High-value transaction requires review',
      timestamp: '12 minutes ago',
      status: 'pending',
    },
  ];

  const getQuotaStatus = (type: string) => {
    const quota = checkQuota(type);
    if (quota.exceeded) return 'exceeded';
    if (quota.percentage > 80) return 'warning';
    if (quota.percentage > 60) return 'caution';
    return 'good';
  };

  const quotaCards = [
    { type: 'agents', label: 'Agents', icon: Bot },
    { type: 'tools', label: 'Tools', icon: Wrench },
    { type: 'api_calls', label: 'API Calls', icon: Zap },
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
          Welcome back, {user?.name?.split(' ')[0]}!
        </h1>
        <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
          Here's what's happening with your SynapseAI workspace today.
        </p>
      </div>

      {/* Connection Status Banner */}
      {!connected && (
        <div className="rounded-md bg-warning-50 dark:bg-warning-900/20 p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <AlertTriangle className="h-5 w-5 text-warning-400" />
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-warning-800 dark:text-warning-200">
                Real-time connection {connectionStatus}
              </h3>
              <div className="mt-2 text-sm text-warning-700 dark:text-warning-300">
                <p>
                  Some real-time features may be unavailable. The system will attempt to reconnect automatically.
                </p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Stats Grid */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
        {stats.map((stat) => (
          <Link
            key={stat.name}
            href={stat.href}
            className="card p-5 hover:shadow-md transition-shadow"
          >
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <stat.icon className="h-6 w-6 text-gray-400" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                    {stat.name}
                  </dt>
                  <dd className="flex items-baseline">
                    <div className="text-2xl font-semibold text-gray-900 dark:text-white">
                      {stat.value}
                    </div>
                    <div
                      className={`ml-2 flex items-baseline text-sm font-semibold ${
                        stat.changeType === 'positive'
                          ? 'text-success-600 dark:text-success-400'
                          : 'text-error-600 dark:text-error-400'
                      }`}
                    >
                      {stat.change}
                    </div>
                  </dd>
                </dl>
              </div>
            </div>
          </Link>
        ))}
      </div>

      {/* Quota Status */}
      <div className="card p-6">
        <h2 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
          Resource Usage
        </h2>
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-3">
          {quotaCards.map((quota) => {
            const status = getQuotaStatus(quota.type);
            const quotaInfo = checkQuota(quota.type);
            
            return (
              <div
                key={quota.type}
                className={`p-4 rounded-lg border-2 ${
                  status === 'exceeded'
                    ? 'border-error-200 bg-error-50 dark:border-error-800 dark:bg-error-900/20'
                    : status === 'warning'
                    ? 'border-warning-200 bg-warning-50 dark:border-warning-800 dark:bg-warning-900/20'
                    : status === 'caution'
                    ? 'border-warning-200 bg-warning-50 dark:border-warning-800 dark:bg-warning-900/20'
                    : 'border-success-200 bg-success-50 dark:border-success-800 dark:bg-success-900/20'
                }`}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <quota.icon className="h-5 w-5 text-gray-500 mr-2" />
                    <span className="text-sm font-medium text-gray-900 dark:text-white">
                      {quota.label}
                    </span>
                  </div>
                  <span className="text-xs text-gray-500 dark:text-gray-400">
                    {quotaInfo.percentage.toFixed(0)}%
                  </span>
                </div>
                <div className="mt-2">
                  <div className="bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                    <div
                      className={`h-2 rounded-full transition-all duration-300 ${
                        status === 'exceeded' || status === 'warning'
                          ? 'bg-error-500'
                          : status === 'caution'
                          ? 'bg-warning-500'
                          : 'bg-success-500'
                      }`}
                      style={{ width: `${Math.min(100, quotaInfo.percentage)}%` }}
                    />
                  </div>
                  <div className="mt-1 text-xs text-gray-600 dark:text-gray-400">
                    {quotaInfo.available === Infinity ? 'Unlimited' : `${quotaInfo.available} remaining`}
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </div>

      <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
        {/* Recent Activity */}
        <div className="card p-6">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-medium text-gray-900 dark:text-white">
              Recent Activity
            </h2>
            <Link
              href="/dashboard/analytics"
              className="text-sm text-primary-600 hover:text-primary-500 dark:text-primary-400"
            >
              View all
            </Link>
          </div>
          
          <div className="flow-root">
            <ul className="-mb-8">
              {recentActivity.map((activity, activityIdx) => (
                <li key={activity.id}>
                  <div className="relative pb-8">
                    {activityIdx !== recentActivity.length - 1 ? (
                      <span
                        className="absolute top-4 left-4 -ml-px h-full w-0.5 bg-gray-200 dark:bg-gray-700"
                        aria-hidden="true"
                      />
                    ) : null}
                    <div className="relative flex space-x-3">
                      <div>
                        <span
                          className={`h-8 w-8 rounded-full flex items-center justify-center ring-8 ring-white dark:ring-gray-800 ${
                            activity.status === 'completed'
                              ? 'bg-success-500'
                              : activity.status === 'in_progress'
                              ? 'bg-primary-500'
                              : activity.status === 'pending'
                              ? 'bg-warning-500'
                              : 'bg-gray-500'
                          }`}
                        >
                          {activity.type === 'agent_execution' && <Bot className="h-4 w-4 text-white" />}
                          {activity.type === 'tool_execution' && <Wrench className="h-4 w-4 text-white" />}
                          {activity.type === 'workflow_started' && <Workflow className="h-4 w-4 text-white" />}
                          {activity.type === 'hitl_request' && <Users className="h-4 w-4 text-white" />}
                        </span>
                      </div>
                      <div className="min-w-0 flex-1 pt-1.5 flex justify-between space-x-4">
                        <div>
                          <p className="text-sm text-gray-900 dark:text-white font-medium">
                            {activity.title}
                          </p>
                          <p className="text-sm text-gray-500 dark:text-gray-400">
                            {activity.description}
                          </p>
                        </div>
                        <div className="text-right text-sm whitespace-nowrap text-gray-500 dark:text-gray-400">
                          <time>{activity.timestamp}</time>
                        </div>
                      </div>
                    </div>
                  </div>
                </li>
              ))}
            </ul>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="card p-6">
          <h2 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            Quick Actions
          </h2>
          
          <div className="grid grid-cols-2 gap-4">
            <Link
              href="/dashboard/agents/new"
              className="flex flex-col items-center justify-center p-4 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg hover:border-primary-500 dark:hover:border-primary-400 transition-colors group"
            >
              <Bot className="h-8 w-8 text-gray-400 group-hover:text-primary-500 dark:group-hover:text-primary-400 mb-2" />
              <span className="text-sm font-medium text-gray-900 dark:text-white">
                Create Agent
              </span>
            </Link>

            <Link
              href="/dashboard/tools/new"
              className="flex flex-col items-center justify-center p-4 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg hover:border-primary-500 dark:hover:border-primary-400 transition-colors group"
            >
              <Wrench className="h-8 w-8 text-gray-400 group-hover:text-primary-500 dark:group-hover:text-primary-400 mb-2" />
              <span className="text-sm font-medium text-gray-900 dark:text-white">
                Create Tool
              </span>
            </Link>

            <Link
              href="/dashboard/workflows/new"
              className="flex flex-col items-center justify-center p-4 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg hover:border-primary-500 dark:hover:border-primary-400 transition-colors group"
            >
              <Workflow className="h-8 w-8 text-gray-400 group-hover:text-primary-500 dark:group-hover:text-primary-400 mb-2" />
              <span className="text-sm font-medium text-gray-900 dark:text-white">
                Create Workflow
              </span>
            </Link>

            <Link
              href="/dashboard/analytics"
              className="flex flex-col items-center justify-center p-4 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg hover:border-primary-500 dark:hover:border-primary-400 transition-colors group"
            >
              <BarChart3 className="h-8 w-8 text-gray-400 group-hover:text-primary-500 dark:group-hover:text-primary-400 mb-2" />
              <span className="text-sm font-medium text-gray-900 dark:text-white">
                View Analytics
              </span>
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}