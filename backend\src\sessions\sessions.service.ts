import { Injectable, NotFoundException, ForbiddenException, BadRequestException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { APIMXService } from '../apix/apix.service';
import { CreateSessionDto, UpdateSessionDto, SessionQueryDto } from './dto/session.dto';
import { Session } from '@prisma/client';

@Injectable()
export class SessionsService {
  constructor(
    private prisma: PrismaService,
    private apixService: APIMXService,
  ) {}

  async create(userId: string, organizationId: string, createSessionDto: CreateSessionDto) {
    const { type, targetId, metadata = {} } = createSessionDto;

    const session = await this.prisma.session.create({
      data: {
        userId,
        organizationId,
        type,
        metadata: {
          targetId,
          ...metadata,
        },
        memory: {
          messages: [],
          context: {},
          tokenCount: 0,
          maxTokens: 4000,
        },
        status: 'active',
      },
    });

    await this.apixService.publishSessionEvent(
      'created',
      session.id,
      organizationId,
      userId,
      {
        type: session.type,
        targetId,
      }
    );

    return session;
  }

  async findAll(organizationId: string, query: SessionQueryDto) {
    const {
      page = 1,
      limit = 20,
      type,
      status,
      userId,
      search,
      sortBy = 'createdAt',
      sortOrder = 'desc',
    } = query;

    const skip = (page - 1) * limit;

    const where: any = {
      organizationId,
      ...(type && { type }),
      ...(status && { status }),
      ...(userId && { userId }),
    };

    if (search) {
      where.OR = [
        { id: { contains: search, mode: 'insensitive' } },
        { metadata: { path: ['targetId'], string_contains: search } },
      ];
    }

    const [sessions, total] = await Promise.all([
      this.prisma.session.findMany({
        where,
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
              avatarUrl: true,
            },
          },
        },
        orderBy: { [sortBy]: sortOrder },
        skip,
        take: limit,
      }),
      this.prisma.session.count({ where }),
    ]);

    return {
      sessions,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    };
  }

  async findOne(id: string, organizationId: string, userId?: string) {
    const session = await this.prisma.session.findUnique({
      where: { id },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            avatarUrl: true,
          },
        },
      },
    });

    if (!session) {
      throw new NotFoundException('Session not found');
    }

    if (session.organizationId !== organizationId) {
      throw new ForbiddenException('Access denied');
    }

    if (userId && session.userId !== userId) {
      throw new ForbiddenException('Access denied');
    }

    return session;
  }

  async update(id: string, organizationId: string, userId: string, updateSessionDto: UpdateSessionDto) {
    const session = await this.findOne(id, organizationId, userId);

    const updatedSession = await this.prisma.session.update({
      where: { id },
      data: {
        ...updateSessionDto,
        updatedAt: new Date(),
      },
    });

    await this.apixService.publishSessionEvent(
      'updated',
      session.id,
      organizationId,
      userId,
      {
        changes: updateSessionDto,
      }
    );

    return updatedSession;
  }

  async remove(id: string, organizationId: string, userId: string) {
    const session = await this.findOne(id, organizationId, userId);

    await this.prisma.session.delete({
      where: { id },
    });

    await this.apixService.publishSessionEvent(
      'deleted',
      session.id,
      organizationId,
      userId,
      {}
    );

    return { message: 'Session deleted successfully' };
  }

  async addMessage(sessionId: string, organizationId: string, userId: string, message: any) {
    const session = await this.findOne(sessionId, organizationId, userId);

    const currentMemory = session.memory as any;
    const messages = currentMemory.messages || [];
    
    const newMessage = {
      id: `msg_${Date.now()}_${Math.random().toString(36).substring(2)}`,
      role: message.role,
      content: message.content,
      metadata: message.metadata || {},
      timestamp: new Date(),
    };

    const tokenCount = this.estimateTokenCount(message.content);
    const newTokenCount = currentMemory.tokenCount + tokenCount;

    const updatedMessages = [...messages, newMessage];
    
    if (newTokenCount > currentMemory.maxTokens) {
      const truncatedMessages = this.truncateMessages(updatedMessages, currentMemory.maxTokens);
      updatedMessages.length = 0;
      updatedMessages.push(...truncatedMessages);
    }

    const updatedSession = await this.prisma.session.update({
      where: { id: sessionId },
      data: {
        memory: {
          ...currentMemory,
          messages: updatedMessages,
          tokenCount: this.calculateTotalTokens(updatedMessages),
        },
        updatedAt: new Date(),
      },
    });

    await this.apixService.publishSessionEvent(
      'message_added',
      sessionId,
      organizationId,
      userId,
      {
        message: newMessage,
      }
    );

    return updatedSession;
  }

  async updateContext(sessionId: string, organizationId: string, userId: string, context: any) {
    const session = await this.findOne(sessionId, organizationId, userId);

    const currentMemory = session.memory as any;

    const updatedSession = await this.prisma.session.update({
      where: { id: sessionId },
      data: {
        memory: {
          ...currentMemory,
          context: {
            ...currentMemory.context,
            ...context,
          },
        },
        updatedAt: new Date(),
      },
    });

    await this.apixService.publishSessionEvent(
      'context_updated',
      sessionId,
      organizationId,
      userId,
      {
        context,
      }
    );

    return updatedSession;
  }

  async clearMemory(sessionId: string, organizationId: string, userId: string) {
    const session = await this.findOne(sessionId, organizationId, userId);

    const currentMemory = session.memory as any;

    const updatedSession = await this.prisma.session.update({
      where: { id: sessionId },
      data: {
        memory: {
          ...currentMemory,
          messages: [],
          context: {},
          tokenCount: 0,
        },
        updatedAt: new Date(),
      },
    });

    await this.apixService.publishSessionEvent(
      'memory_cleared',
      sessionId,
      organizationId,
      userId,
      {}
    );

    return updatedSession;
  }

  async getSessionStats(organizationId: string, userId?: string) {
    const where: any = { organizationId };
    if (userId) where.userId = userId;

    const [total, active, byType, byStatus] = await Promise.all([
      this.prisma.session.count({ where }),
      this.prisma.session.count({ where: { ...where, status: 'active' } }),
      this.prisma.session.groupBy({
        by: ['type'],
        where,
        _count: true,
      }),
      this.prisma.session.groupBy({
        by: ['status'],
        where,
        _count: true,
      }),
    ]);

    return {
      total,
      active,
      byType: byType.reduce((acc, item) => {
        acc[item.type] = item._count;
        return acc;
      }, {}),
      byStatus: byStatus.reduce((acc, item) => {
        acc[item.status] = item._count;
        return acc;
      }, {}),
    };
  }

  private estimateTokenCount(text: string): number {
    return Math.ceil(text.length / 4);
  }

  private calculateTotalTokens(messages: any[]): number {
    return messages.reduce((total, message) => {
      return total + this.estimateTokenCount(message.content);
    }, 0);
  }

  private truncateMessages(messages: any[], maxTokens: number): any[] {
    let totalTokens = 0;
    const result = [];

    for (let i = messages.length - 1; i >= 0; i--) {
      const messageTokens = this.estimateTokenCount(messages[i].content);
      if (totalTokens + messageTokens > maxTokens) {
        break;
      }
      totalTokens += messageTokens;
      result.unshift(messages[i]);
    }

    return result;
  }
}