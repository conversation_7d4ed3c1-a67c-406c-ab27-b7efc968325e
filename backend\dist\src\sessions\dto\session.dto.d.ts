export declare enum SessionType {
    AGENT = "agent",
    TOOL = "tool",
    HYBRID = "hybrid",
    WIDGET = "widget"
}
export declare enum SessionStatus {
    ACTIVE = "active",
    COMPLETED = "completed",
    FAILED = "failed",
    PAUSED = "paused"
}
export declare class CreateSessionDto {
    type: SessionType;
    targetId: string;
    metadata?: Record<string, any>;
}
export declare class UpdateSessionDto {
    status?: SessionStatus;
    metadata?: Record<string, any>;
}
export declare class SessionQueryDto {
    page?: number;
    limit?: number;
    type?: SessionType;
    status?: SessionStatus;
    userId?: string;
    search?: string;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
}
export declare class AddMessageDto {
    role: 'user' | 'assistant' | 'system';
    content: string;
    metadata?: Record<string, any>;
}
export declare class UpdateContextDto {
    context: Record<string, any>;
}
