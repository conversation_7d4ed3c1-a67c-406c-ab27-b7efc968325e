import { Module } from '@nestjs/common';
import { WorkflowsController } from './workflows.controller';
import { WorkflowsService } from './workflows.service';
import { WorkflowExecutorService } from './workflow-executor.service';
import { AuthModule } from '../auth/auth.module';
import { APIMXModule } from '../apix/apix.module';
import { SessionsModule } from '../sessions/sessions.module';
import { AgentsModule } from '../agents/agents.module';
import { ToolsModule } from '../tools/tools.module';
import { ProvidersModule } from '../providers/providers.module';

@Module({
  imports: [
    AuthModule, 
    APIMXModule, 
    SessionsModule, 
    AgentsModule, 
    ToolsModule, 
    ProvidersModule
  ],
  controllers: [WorkflowsController],
  providers: [WorkflowsService, WorkflowExecutorService],
  exports: [WorkflowsService, WorkflowExecutorService],
})
export class WorkflowsModule {}