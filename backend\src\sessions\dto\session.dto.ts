import { IsString, IsOptional, <PERSON><PERSON><PERSON>, IsO<PERSON>, IsN<PERSON>ber, Min, <PERSON>, IsUUID } from 'class-validator';
import { Type } from 'class-transformer';

export enum SessionType {
  AGENT = 'agent',
  TOOL = 'tool',
  HYBRID = 'hybrid',
  WIDGET = 'widget',
}

export enum SessionStatus {
  ACTIVE = 'active',
  COMPLETED = 'completed',
  FAILED = 'failed',
  PAUSED = 'paused',
}

export class CreateSessionDto {
  @IsEnum(SessionType)
  type: SessionType;

  @IsString()
  @IsUUID()
  targetId: string;

  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;
}

export class UpdateSessionDto {
  @IsOptional()
  @IsEnum(SessionStatus)
  status?: SessionStatus;

  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;
}

export class SessionQueryDto {
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  page?: number = 1;

  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  @Max(100)
  limit?: number = 20;

  @IsOptional()
  @IsEnum(SessionType)
  type?: SessionType;

  @IsOptional()
  @IsEnum(SessionStatus)
  status?: SessionStatus;

  @IsOptional()
  @IsString()
  @IsUUID()
  userId?: string;

  @IsOptional()
  @IsString()
  search?: string;

  @IsOptional()
  @IsString()
  sortBy?: string = 'createdAt';

  @IsOptional()
  @IsEnum(['asc', 'desc'])
  sortOrder?: 'asc' | 'desc' = 'desc';
}

export class AddMessageDto {
  @IsEnum(['user', 'assistant', 'system'])
  role: 'user' | 'assistant' | 'system';

  @IsString()
  content: string;

  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;
}

export class UpdateContextDto {
  @IsObject()
  context: Record<string, any>;
}