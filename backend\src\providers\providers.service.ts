import { Injectable, NotFoundException, BadRequestException, Logger } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { APIMXService } from '../apix/apix.service';
import { ConfigService } from '@nestjs/config';
import {
  CreateProviderDto,
  UpdateProviderDto,
  ProviderQueryDto,
  ProviderSelectionDto,
  ProviderExecutionDto,
  ProviderHealthCheckDto,
  ProviderMetricsDto,
  ProviderType,
  ProviderStatus,
  ProviderCapability,
  ModelSize,
} from './dto/provider.dto';
import { CircuitBreakerService } from './circuit-breaker.service';
import { ProviderExecutorService } from './provider-executor.service';

interface ProviderScore {
  providerId: string;
  modelId: string;
  score: number;
  factors: {
    cost: number;
    latency: number;
    reliability: number;
    capabilities: number;
    availability: number;
  };
}

@Injectable()
export class ProvidersService {
  private readonly logger = new Logger(ProvidersService.name);

  constructor(
    private prisma: PrismaService,
    private apixService: APIMXService,
    private configService: ConfigService,
    private circuitBreaker: CircuitBreakerService,
    private providerExecutor: ProviderExecutorService,
  ) {}

  async create(organizationId: string, createProviderDto: CreateProviderDto) {
    const { models, metadata = {}, ...providerData } = createProviderDto;

    // Validate provider configuration
    await this.validateProviderConfig(createProviderDto.type, createProviderDto.config);

    const provider = await this.prisma.provider.create({
      data: {
        ...providerData,
        organizationId,
        models: models || [],
        metadata: {
          ...metadata,
          createdAt: new Date(),
          totalExecutions: 0,
          totalCost: 0,
          avgLatency: 0,
          successRate: 100,
          lastHealthCheck: null,
        },
      },
    });

    // Initialize circuit breaker for this provider
    this.circuitBreaker.initializeProvider(provider.id);

    // Schedule initial health check
    setTimeout(() => this.performHealthCheck(provider.id), 5000);

    await this.apixService.publishProviderEvent(
      'provider_created',
      provider.id,
      organizationId,
      {
        name: provider.name,
        type: provider.type,
        models: models?.length || 0,
      }
    );

    return provider;
  }

  async findAll(organizationId: string, query: ProviderQueryDto) {
    const {
      page = 1,
      limit = 20,
      type,
      status,
      search,
      capabilities,
      tags,
      sortBy = 'priority',
      sortOrder = 'desc',
    } = query;

    const skip = (page - 1) * limit;

    const where: any = {
      organizationId,
      ...(type && { type }),
      ...(status && { status }),
    };

    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } },
      ];
    }

    if (capabilities && capabilities.length > 0) {
      where.models = {
        path: [],
        array_contains: capabilities.map(cap => ({ capabilities: { has: cap } })),
      };
    }

    if (tags && tags.length > 0) {
      where.metadata = {
        path: ['tags'],
        array_contains: tags,
      };
    }

    const [providers, total] = await Promise.all([
      this.prisma.provider.findMany({
        where,
        orderBy: { [sortBy]: sortOrder },
        skip,
        take: limit,
      }),
      this.prisma.provider.count({ where }),
    ]);

    return {
      providers,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    };
  }

  async findOne(id: string, organizationId: string) {
    const provider = await this.prisma.provider.findFirst({
      where: { id, organizationId },
    });

    if (!provider) {
      throw new NotFoundException('Provider not found');
    }

    return provider;
  }

  async update(id: string, organizationId: string, updateProviderDto: UpdateProviderDto) {
    const provider = await this.findOne(id, organizationId);

    // Validate configuration if provided
    if (updateProviderDto.config) {
      await this.validateProviderConfig(
        provider.type as ProviderType,
        { ...provider.config, ...updateProviderDto.config } as any
      );
    }

    const updatedProvider = await this.prisma.provider.update({
      where: { id },
      data: {
        ...updateProviderDto,
        config: updateProviderDto.config ? {
          ...provider.config,
          ...updateProviderDto.config,
        } : provider.config,
        metadata: updateProviderDto.metadata ? {
          ...provider.metadata,
          ...updateProviderDto.metadata,
          updatedAt: new Date(),
        } : provider.metadata,
        updatedAt: new Date(),
      },
    });

    // Reset circuit breaker if provider is being reactivated
    if (updateProviderDto.status === ProviderStatus.ACTIVE) {
      this.circuitBreaker.resetProvider(id);
    }

    await this.apixService.publishProviderEvent(
      'provider_updated',
      id,
      organizationId,
      {
        changes: updateProviderDto,
        status: updatedProvider.status,
      }
    );

    return updatedProvider;
  }

  async remove(id: string, organizationId: string) {
    const provider = await this.findOne(id, organizationId);

    // Check if provider has active executions
    const activeExecutions = await this.prisma.session.count({
      where: {
        organizationId,
        status: 'active',
        metadata: {
          path: ['providerId'],
          equals: id,
        },
      },
    });

    if (activeExecutions > 0) {
      throw new BadRequestException('Cannot delete provider with active executions');
    }

    await this.prisma.provider.delete({
      where: { id },
    });

    // Clean up circuit breaker
    this.circuitBreaker.removeProvider(id);

    await this.apixService.publishProviderEvent(
      'provider_deleted',
      id,
      organizationId,
      {}
    );

    return { message: 'Provider deleted successfully' };
  }

  async selectOptimalProvider(
    organizationId: string,
    selectionCriteria: ProviderSelectionDto
  ): Promise<{ provider: any; model: any; score: number }> {
    const providers = await this.prisma.provider.findMany({
      where: {
        organizationId,
        status: ProviderStatus.ACTIVE,
      },
    });

    if (providers.length === 0) {
      throw new NotFoundException('No active providers available');
    }

    const scores: ProviderScore[] = [];

    for (const provider of providers) {
      const models = provider.models as any[];
      
      for (const model of models) {
        // Check if model meets basic requirements
        if (!this.modelMeetsRequirements(model, selectionCriteria)) {
          continue;
        }

        // Skip if circuit breaker is open
        if (!this.circuitBreaker.canExecute(provider.id)) {
          this.logger.warn(`Provider ${provider.id} circuit breaker is open`);
          continue;
        }

        const score = await this.calculateProviderScore(
          provider,
          model,
          selectionCriteria
        );

        scores.push({
          providerId: provider.id,
          modelId: model.id,
          score: score.score,
          factors: score.factors,
        });
      }
    }

    if (scores.length === 0) {
      throw new NotFoundException('No providers match the selection criteria');
    }

    // Sort by score (highest first)
    scores.sort((a, b) => b.score - a.score);

    const bestScore = scores[0];
    const selectedProvider = providers.find(p => p.id === bestScore.providerId);
    const selectedModel = (selectedProvider.models as any[]).find(m => m.id === bestScore.modelId);

    this.logger.log(`Selected provider: ${selectedProvider.name}, model: ${selectedModel.name}, score: ${bestScore.score}`);

    return {
      provider: selectedProvider,
      model: selectedModel,
      score: bestScore.score,
    };
  }

  async executeProvider(
    organizationId: string,
    userId: string,
    executionDto: ProviderExecutionDto
  ) {
    const provider = await this.findOne(executionDto.providerId, organizationId);
    
    if (provider.status !== ProviderStatus.ACTIVE) {
      throw new BadRequestException('Provider is not active');
    }

    if (!this.circuitBreaker.canExecute(provider.id)) {
      throw new BadRequestException('Provider is temporarily unavailable');
    }

    try {
      const result = await this.providerExecutor.execute(
        provider,
        executionDto,
        organizationId,
        userId
      );

      // Record successful execution
      this.circuitBreaker.recordSuccess(provider.id);
      await this.updateProviderMetrics(provider.id, result);

      return result;
    } catch (error) {
      // Record failure
      this.circuitBreaker.recordFailure(provider.id);
      
      await this.apixService.publishProviderEvent(
        'provider_execution_failed',
        provider.id,
        organizationId,
        {
          error: error.message,
          modelId: executionDto.modelId,
        }
      );

      throw error;
    }
  }

  async performHealthCheck(providerId: string, healthCheckDto?: ProviderHealthCheckDto) {
    const provider = await this.prisma.provider.findUnique({
      where: { id: providerId },
    });

    if (!provider) {
      throw new NotFoundException('Provider not found');
    }

    const healthResults = {
      providerId,
      status: ProviderStatus.ACTIVE,
      models: [],
      rateLimits: {},
      latency: 0,
      errors: [],
      timestamp: new Date(),
    };

    try {
      const config = provider.config as any;
      const models = provider.models as any[];

      // Test each model if requested
      if (healthCheckDto?.checkAllModels && models.length > 0) {
        for (const model of models) {
          try {
            const startTime = Date.now();
            
            await this.providerExecutor.testModel(
              provider,
              model,
              healthCheckDto.testPrompt || 'Health check'
            );

            const latency = Date.now() - startTime;
            healthResults.models.push({
              modelId: model.id,
              status: 'healthy',
              latency,
            });
          } catch (error) {
            healthResults.models.push({
              modelId: model.id,
              status: 'unhealthy',
              error: error.message,
            });
            healthResults.errors.push(`Model ${model.id}: ${error.message}`);
          }
        }
      }

      // Check rate limits
      if (healthCheckDto?.checkRateLimits && config.rateLimits) {
        healthResults.rateLimits = await this.checkRateLimits(provider);
      }

      // Calculate average latency
      const modelLatencies = healthResults.models
        .filter(m => m.latency)
        .map(m => m.latency);
      
      if (modelLatencies.length > 0) {
        healthResults.latency = Math.round(
          modelLatencies.reduce((sum, latency) => sum + latency, 0) / modelLatencies.length
        );
      }

      // Determine overall status
      const unhealthyModels = healthResults.models.filter(m => m.status === 'unhealthy');
      if (unhealthyModels.length === models.length) {
        healthResults.status = ProviderStatus.ERROR;
      } else if (unhealthyModels.length > 0) {
        healthResults.status = ProviderStatus.MAINTENANCE;
      }

      // Update provider metadata
      await this.prisma.provider.update({
        where: { id: providerId },
        data: {
          status: healthResults.status,
          metadata: {
            ...provider.metadata,
            lastHealthCheck: healthResults.timestamp,
            healthStatus: healthResults,
          },
        },
      });

      // Reset circuit breaker if provider is healthy
      if (healthResults.status === ProviderStatus.ACTIVE) {
        this.circuitBreaker.resetProvider(providerId);
      }

      await this.apixService.publishProviderEvent(
        'provider_health_checked',
        providerId,
        provider.organizationId,
        healthResults
      );

      return healthResults;
    } catch (error) {
      healthResults.status = ProviderStatus.ERROR;
      healthResults.errors.push(error.message);

      await this.prisma.provider.update({
        where: { id: providerId },
        data: {
          status: ProviderStatus.ERROR,
          metadata: {
            ...provider.metadata,
            lastHealthCheck: healthResults.timestamp,
            healthStatus: healthResults,
          },
        },
      });

      return healthResults;
    }
  }

  async getProviderMetrics(providerId: string, metricsDto: ProviderMetricsDto) {
    const provider = await this.prisma.provider.findUnique({
      where: { id: providerId },
    });

    if (!provider) {
      throw new NotFoundException('Provider not found');
    }

    const timeRangeHours = this.parseTimeRange(metricsDto.timeRange);
    const since = new Date(Date.now() - timeRangeHours * 60 * 60 * 1000);

    const [executions, costs, errors] = await Promise.all([
      this.prisma.session.findMany({
        where: {
          organizationId: provider.organizationId,
          metadata: {
            path: ['providerId'],
            equals: providerId,
          },
          createdAt: { gte: since },
        },
        include: {
          user: {
            select: { id: true, name: true },
          },
        },
      }),
      metricsDto.includeCosts ? this.prisma.billingUsage.findMany({
        where: {
          organizationId: provider.organizationId,
          type: 'provider_execution',
          metadata: {
            path: ['providerId'],
            equals: providerId,
          },
          createdAt: { gte: since },
        },
      }) : [],
      metricsDto.includeErrors ? this.prisma.aPIMXEvent.findMany({
        where: {
          organizationId: provider.organizationId,
          type: 'provider_execution_failed',
          payload: {
            path: ['providerId'],
            equals: providerId,
          },
          timestamp: { gte: since },
        },
      }) : [],
    ]);

    const metrics = {
      providerId,
      timeRange: metricsDto.timeRange,
      totalExecutions: executions.length,
      successfulExecutions: executions.filter(e => e.status === 'completed').length,
      failedExecutions: executions.filter(e => e.status === 'failed').length,
      avgLatency: this.calculateAvgLatency(executions),
      successRate: this.calculateSuccessRate(executions),
      totalCost: costs.reduce((sum, cost) => sum + cost.cost, 0),
      errors: errors.map(e => ({
        timestamp: e.timestamp,
        error: e.payload.error,
        modelId: e.payload.modelId,
      })),
      executionsByModel: this.groupExecutionsByModel(executions),
      costsByModel: this.groupCostsByModel(costs),
      hourlyStats: this.calculateHourlyStats(executions, timeRangeHours),
    };

    return metrics;
  }

  async getProviderStats(organizationId: string) {
    const [total, byType, byStatus, executions, costs] = await Promise.all([
      this.prisma.provider.count({ where: { organizationId } }),
      this.prisma.provider.groupBy({
        by: ['type'],
        where: { organizationId },
        _count: true,
      }),
      this.prisma.provider.groupBy({
        by: ['status'],
        where: { organizationId },
        _count: true,
      }),
      this.prisma.session.count({
        where: {
          organizationId,
          metadata: {
            path: ['providerId'],
            not: null,
          },
        },
      }),
      this.prisma.billingUsage.aggregate({
        where: {
          organizationId,
          type: 'provider_execution',
        },
        _sum: { cost: true },
      }),
    ]);

    return {
      total,
      executions,
      totalCost: costs._sum.cost || 0,
      byType: byType.reduce((acc, item) => {
        acc[item.type] = item._count;
        return acc;
      }, {}),
      byStatus: byStatus.reduce((acc, item) => {
        acc[item.status] = item._count;
        return acc;
      }, {}),
    };
  }

  // Private helper methods

  private async validateProviderConfig(type: ProviderType, config: any) {
    switch (type) {
      case ProviderType.OPENAI:
        if (!config.apiKey) {
          throw new BadRequestException('OpenAI providers require an API key');
        }
        break;
      case ProviderType.CLAUDE:
        if (!config.apiKey) {
          throw new BadRequestException('Claude providers require an API key');
        }
        break;
      case ProviderType.CUSTOM:
        if (!config.baseUrl) {
          throw new BadRequestException('Custom providers require a base URL');
        }
        break;
    }
  }

  private modelMeetsRequirements(model: any, criteria: ProviderSelectionDto): boolean {
    // Check required capabilities
    if (criteria.requiredCapabilities && criteria.requiredCapabilities.length > 0) {
      const hasAllCapabilities = criteria.requiredCapabilities.every(cap =>
        model.capabilities.includes(cap)
      );
      if (!hasAllCapabilities) return false;
    }

    // Check model size preference
    if (criteria.preferredModelSize && model.size !== criteria.preferredModelSize) {
      return false;
    }

    // Check cost threshold
    if (criteria.maxCostPer1KTokens && model.inputCostPer1KTokens > criteria.maxCostPer1KTokens) {
      return false;
    }

    // Check minimum tokens
    if (criteria.minTokens && model.maxTokens && model.maxTokens < criteria.minTokens) {
      return false;
    }

    return true;
  }

  private async calculateProviderScore(
    provider: any,
    model: any,
    criteria: ProviderSelectionDto
  ): Promise<{ score: number; factors: any }> {
    const factors = {
      cost: 0,
      latency: 0,
      reliability: 0,
      capabilities: 0,
      availability: 0,
    };

    const metadata = provider.metadata as any;

    // Cost factor (0-100, lower cost = higher score)
    const avgCost = (model.inputCostPer1KTokens + model.outputCostPer1KTokens) / 2;
    factors.cost = Math.max(0, 100 - (avgCost * 1000)); // Normalize to 0-100

    // Latency factor (0-100, lower latency = higher score)
    const avgLatency = metadata.avgLatency || 1000;
    factors.latency = Math.max(0, 100 - (avgLatency / 10)); // Normalize to 0-100

    // Reliability factor (success rate as percentage)
    factors.reliability = metadata.successRate || 100;

    // Capabilities factor (how many required capabilities are met)
    if (criteria.requiredCapabilities && criteria.requiredCapabilities.length > 0) {
      const matchedCapabilities = criteria.requiredCapabilities.filter(cap =>
        model.capabilities.includes(cap)
      ).length;
      factors.capabilities = (matchedCapabilities / criteria.requiredCapabilities.length) * 100;
    } else {
      factors.capabilities = 100;
    }

    // Availability factor (circuit breaker status)
    factors.availability = this.circuitBreaker.canExecute(provider.id) ? 100 : 0;

    // Apply preferences
    let weights = {
      cost: 25,
      latency: 25,
      reliability: 30,
      capabilities: 15,
      availability: 5,
    };

    if (criteria.preferLowCost) {
      weights.cost = 40;
      weights.latency = 15;
    }

    if (criteria.preferLowLatency) {
      weights.latency = 40;
      weights.cost = 15;
    }

    // Organization preferences
    if (criteria.organizationPreferences) {
      const prefs = criteria.organizationPreferences;
      
      if (prefs.preferredProviders && prefs.preferredProviders.includes(provider.id)) {
        weights = { ...weights, availability: weights.availability + 20 };
      }
      
      if (prefs.excludedProviders && prefs.excludedProviders.includes(provider.id)) {
        return { score: 0, factors };
      }
    }

    // Calculate weighted score
    const score = (
      (factors.cost * weights.cost) +
      (factors.latency * weights.latency) +
      (factors.reliability * weights.reliability) +
      (factors.capabilities * weights.capabilities) +
      (factors.availability * weights.availability)
    ) / 100;

    return { score, factors };
  }

  private async updateProviderMetrics(providerId: string, executionResult: any) {
    const provider = await this.prisma.provider.findUnique({
      where: { id: providerId },
    });

    if (!provider) return;

    const metadata = provider.metadata as any;
    const totalExecutions = (metadata.totalExecutions || 0) + 1;
    const totalCost = (metadata.totalCost || 0) + (executionResult.cost || 0);
    
    // Update average latency
    const currentAvgLatency = metadata.avgLatency || 0;
    const newAvgLatency = currentAvgLatency === 0 
      ? executionResult.executionTime
      : (currentAvgLatency * (totalExecutions - 1) + executionResult.executionTime) / totalExecutions;

    // Update success rate
    const successfulExecutions = executionResult.success 
      ? (metadata.successfulExecutions || 0) + 1
      : (metadata.successfulExecutions || 0);
    const successRate = (successfulExecutions / totalExecutions) * 100;

    await this.prisma.provider.update({
      where: { id: providerId },
      data: {
        metadata: {
          ...metadata,
          totalExecutions,
          totalCost,
          avgLatency: Math.round(newAvgLatency),
          successRate: Math.round(successRate * 100) / 100,
          successfulExecutions,
          lastExecution: new Date(),
        },
      },
    });
  }

  private async checkRateLimits(provider: any) {
    const config = provider.config as any;
    const rateLimits = config.rateLimits;

    if (!rateLimits) return {};

    // This would typically check against actual rate limit tracking
    // For now, return the configured limits
    return {
      requestsPerMinute: {
        limit: rateLimits.requestsPerMinute,
        used: 0, // Would be tracked in Redis
        remaining: rateLimits.requestsPerMinute,
      },
      tokensPerMinute: {
        limit: rateLimits.tokensPerMinute,
        used: 0,
        remaining: rateLimits.tokensPerMinute,
      },
    };
  }

  private parseTimeRange(timeRange: string): number {
    const unit = timeRange.slice(-1);
    const value = parseInt(timeRange.slice(0, -1));

    switch (unit) {
      case 'h': return value;
      case 'd': return value * 24;
      case 'w': return value * 24 * 7;
      case 'm': return value * 24 * 30;
      default: return 24;
    }
  }

  private calculateAvgLatency(executions: any[]): number {
    if (executions.length === 0) return 0;
    
    const latencies = executions
      .map(e => e.metadata?.executionTime)
      .filter(t => t != null);
    
    if (latencies.length === 0) return 0;
    
    return Math.round(latencies.reduce((sum, t) => sum + t, 0) / latencies.length);
  }

  private calculateSuccessRate(executions: any[]): number {
    if (executions.length === 0) return 100;
    
    const successful = executions.filter(e => e.status === 'completed').length;
    return Math.round((successful / executions.length) * 100 * 100) / 100;
  }

  private groupExecutionsByModel(executions: any[]) {
    const grouped = {};
    executions.forEach(e => {
      const modelId = e.metadata?.modelId || 'unknown';
      if (!grouped[modelId]) {
        grouped[modelId] = { total: 0, successful: 0, failed: 0 };
      }
      grouped[modelId].total++;
      if (e.status === 'completed') {
        grouped[modelId].successful++;
      } else {
        grouped[modelId].failed++;
      }
    });
    return grouped;
  }

  private groupCostsByModel(costs: any[]) {
    const grouped = {};
    costs.forEach(c => {
      const modelId = c.metadata?.modelId || 'unknown';
      if (!grouped[modelId]) {
        grouped[modelId] = 0;
      }
      grouped[modelId] += c.cost;
    });
    return grouped;
  }

  private calculateHourlyStats(executions: any[], timeRangeHours: number) {
    const stats = Array(Math.min(timeRangeHours, 24)).fill(0).map((_, i) => ({
      hour: new Date(Date.now() - (i * 60 * 60 * 1000)).getHours(),
      executions: 0,
      successful: 0,
      failed: 0,
    }));

    executions.forEach(e => {
      const hourAgo = Math.floor((Date.now() - new Date(e.createdAt).getTime()) / (60 * 60 * 1000));
      if (hourAgo < stats.length) {
        const stat = stats[hourAgo];
        stat.executions++;
        if (e.status === 'completed') {
          stat.successful++;
        } else {
          stat.failed++;
        }
      }
    });

    return stats.reverse();
  }
}