import { 
  Tool, 
  CreateToolRequest, 
  ExecuteToolRequest, 
  ExecuteToolResponse,
  PaginatedResponse,
  QueryParams
} from '../types';
import { BaseManager } from './base-manager';

export interface ToolQueryParams extends QueryParams {
  type?: 'api' | 'function' | 'workflow';
  status?: 'draft' | 'active' | 'archived';
  category?: string;
  tags?: string[];
}

export interface ToolTestRequest {
  input: Record<string, any>;
  dryRun?: boolean;
}

export class ToolManager extends BaseManager {
  /**
   * Create a new tool
   */
  async create(tool: CreateToolRequest): Promise<Tool> {
    this.validateRequired(tool, ['name', 'description', 'type', 'config', 'schema']);
    
    return this.request({
      method: 'POST',
      url: '/tools',
      data: tool,
    });
  }

  /**
   * Get all tools
   */
  async list(params?: ToolQueryParams): Promise<PaginatedResponse<Tool>> {
    return this.request({
      method: 'GET',
      url: '/tools',
      params: this.buildQueryParams(params || {}),
    });
  }

  /**
   * Get a specific tool by ID
   */
  async get(id: string): Promise<Tool> {
    this.validateRequired({ id }, ['id']);
    
    return this.request({
      method: 'GET',
      url: `/tools/${id}`,
    });
  }

  /**
   * Update a tool
   */
  async update(id: string, updates: Partial<CreateToolRequest>): Promise<Tool> {
    this.validateRequired({ id }, ['id']);
    
    return this.request({
      method: 'PUT',
      url: `/tools/${id}`,
      data: updates,
    });
  }

  /**
   * Delete a tool
   */
  async delete(id: string): Promise<void> {
    this.validateRequired({ id }, ['id']);
    
    await this.request({
      method: 'DELETE',
      url: `/tools/${id}`,
    });
  }

  /**
   * Execute a tool
   */
  async execute(id: string, request: ExecuteToolRequest): Promise<ExecuteToolResponse> {
    this.validateRequired({ id, request }, ['id', 'request']);
    this.validateRequired(request, ['input']);
    
    return this.request({
      method: 'POST',
      url: `/tools/${id}/execute`,
      data: request,
    });
  }

  /**
   * Test a tool configuration
   */
  async test(id: string, testRequest: ToolTestRequest): Promise<ExecuteToolResponse> {
    this.validateRequired({ id, testRequest }, ['id', 'testRequest']);
    this.validateRequired(testRequest, ['input']);
    
    return this.request({
      method: 'POST',
      url: `/tools/${id}/test`,
      data: testRequest,
    });
  }

  /**
   * Clone an existing tool
   */
  async clone(id: string, newName: string, description?: string): Promise<Tool> {
    this.validateRequired({ id, newName }, ['id', 'newName']);
    
    return this.request({
      method: 'POST',
      url: `/tools/${id}/clone`,
      data: {
        name: newName,
        description,
      },
    });
  }

  /**
   * Create a new version of a tool
   */
  async createVersion(
    id: string, 
    version: string, 
    description: string,
    config?: any,
    schema?: any
  ): Promise<Tool> {
    this.validateRequired({ id, version, description }, ['id', 'version', 'description']);
    
    return this.request({
      method: 'POST',
      url: `/tools/${id}/version`,
      data: {
        version,
        description,
        config,
        schema,
      },
    });
  }

  /**
   * Rollback to a specific version
   */
  async rollbackToVersion(id: string, version: string): Promise<Tool> {
    this.validateRequired({ id, version }, ['id', 'version']);
    
    return this.request({
      method: 'PUT',
      url: `/tools/${id}/version/${version}/rollback`,
    });
  }

  /**
   * Get execution history for a tool
   */
  async getExecutionHistory(id: string, limit = 50): Promise<Array<{
    id: string;
    sessionId: string;
    input: any;
    output: any;
    success: boolean;
    executionTime: number;
    cost?: number;
    createdAt: string;
    user: {
      id: string;
      name: string;
      email: string;
    };
  }>> {
    this.validateRequired({ id }, ['id']);
    
    return this.request({
      method: 'GET',
      url: `/tools/${id}/execution/history`,
      params: { limit },
    });
  }

  /**
   * Get performance metrics for a tool
   */
  async getMetrics(id: string): Promise<{
    totalExecutions: number;
    avgExecutionTime: number;
    successRate: number;
    lastExecutedAt?: string;
  }> {
    this.validateRequired({ id }, ['id']);
    
    return this.request({
      method: 'GET',
      url: `/tools/${id}/execution/metrics`,
    });
  }

  /**
   * Get tool statistics for the organization
   */
  async getStats(): Promise<{
    total: number;
    executions: number;
    byType: Record<string, number>;
    byStatus: Record<string, number>;
  }> {
    return this.request({
      method: 'GET',
      url: '/tools/stats',
    });
  }

  /**
   * Get popular tools
   */
  async getPopular(limit = 10): Promise<Tool[]> {
    return this.request({
      method: 'GET',
      url: '/tools/popular',
      params: { limit },
    });
  }

  /**
   * Search tools by tags
   */
  async searchByTags(tags: string[]): Promise<Tool[]> {
    this.validateRequired({ tags }, ['tags']);
    
    return this.request({
      method: 'GET',
      url: '/tools/search/tags',
      params: { tags },
    });
  }

  /**
   * Get tools by category
   */
  async getByCategory(category: string): Promise<Tool[]> {
    this.validateRequired({ category }, ['category']);
    
    return this.request({
      method: 'GET',
      url: `/tools/category/${category}`,
    });
  }

  /**
   * Validate tool configuration
   */
  async validateConfig(type: 'api' | 'function' | 'workflow', config: any): Promise<{
    valid: boolean;
    errors: string[];
    warnings: string[];
  }> {
    this.validateRequired({ type, config }, ['type', 'config']);
    
    return this.request({
      method: 'POST',
      url: '/tools/validate',
      data: { type, config },
    });
  }

  /**
   * Generate schema from example data
   */
  async generateSchema(examples: Array<{
    input: any;
    output: any;
    description?: string;
  }>): Promise<{
    inputSchema: Record<string, any>;
    outputSchema: Record<string, any>;
    confidence: number;
  }> {
    this.validateRequired({ examples }, ['examples']);
    
    return this.request({
      method: 'POST',
      url: '/tools/generate-schema',
      data: { examples },
    });
  }

  /**
   * Get tool templates
   */
  async getTemplates(type?: 'api' | 'function' | 'workflow'): Promise<Array<{
    id: string;
    name: string;
    description: string;
    type: string;
    config: any;
    schema: any;
    tags: string[];
  }>> {
    return this.request({
      method: 'GET',
      url: '/tools/templates',
      params: type ? { type } : {},
    });
  }

  /**
   * Create tool from template
   */
  async createFromTemplate(
    templateId: string, 
    name: string, 
    customConfig?: any
  ): Promise<Tool> {
    this.validateRequired({ templateId, name }, ['templateId', 'name']);
    
    return this.request({
      method: 'POST',
      url: `/tools/templates/${templateId}/create`,
      data: {
        name,
        customConfig,
      },
    });
  }

  /**
   * Export tool configuration
   */
  async export(id: string): Promise<{
    tool: Tool;
    exportedAt: string;
    format: string;
  }> {
    this.validateRequired({ id }, ['id']);
    
    return this.request({
      method: 'GET',
      url: `/tools/${id}/export`,
    });
  }

  /**
   * Import tool from exported configuration
   */
  async import(exportedTool: any, name?: string): Promise<Tool> {
    this.validateRequired({ exportedTool }, ['exportedTool']);
    
    return this.request({
      method: 'POST',
      url: '/tools/import',
      data: {
        ...exportedTool,
        ...(name && { name }),
      },
    });
  }

  /**
   * Batch execute multiple tools
   */
  async batchExecute(executions: Array<{
    toolId: string;
    input: Record<string, any>;
    context?: Record<string, any>;
  }>): Promise<Array<{
    toolId: string;
    result: ExecuteToolResponse;
  }>> {
    this.validateRequired({ executions }, ['executions']);
    
    return this.request({
      method: 'POST',
      url: '/tools/batch/execute',
      data: { executions },
    });
  }

  /**
   * Create a tool chain (sequential execution)
   */
  async createChain(tools: Array<{
    toolId: string;
    outputMapping?: Record<string, string>;
    condition?: string;
  }>): Promise<{
    chainId: string;
    tools: any[];
  }> {
    this.validateRequired({ tools }, ['tools']);
    
    return this.request({
      method: 'POST',
      url: '/tools/chains',
      data: { tools },
    });
  }

  /**
   * Execute a tool chain
   */
  async executeChain(
    chainId: string, 
    initialInput: Record<string, any>
  ): Promise<{
    success: boolean;
    results: Array<{
      toolId: string;
      output: any;
      executionTime: number;
    }>;
    totalExecutionTime: number;
    finalOutput: any;
  }> {
    this.validateRequired({ chainId, initialInput }, ['chainId', 'initialInput']);
    
    return this.request({
      method: 'POST',
      url: `/tools/chains/${chainId}/execute`,
      data: { input: initialInput },
    });
  }

  /**
   * Subscribe to tool events
   */
  onToolEvent(toolId: string, callback: (event: any) => void): string {
    return this.subscribe({
      eventTypes: ['tool.executed', 'tool.updated', 'tool.deleted'],
      organizationId: this.getOrganizationId(),
      callback: (event) => {
        if (event.payload.toolId === toolId) {
          callback(event);
        }
      },
    });
  }

  /**
   * Subscribe to all tool events in the organization
   */
  onAllToolEvents(callback: (event: any) => void): string {
    return this.subscribe({
      eventTypes: ['tool.created', 'tool.updated', 'tool.executed', 'tool.deleted'],
      organizationId: this.getOrganizationId(),
      callback,
    });
  }

  /**
   * Monitor tool execution in real-time
   */
  async monitorExecution(
    toolId: string,
    callback: (status: {
      status: 'started' | 'running' | 'completed' | 'failed';
      progress?: number;
      message?: string;
      result?: any;
    }) => void
  ): Promise<string> {
    this.validateRequired({ toolId, callback }, ['toolId', 'callback']);
    
    return this.subscribe({
      eventTypes: ['tool.execution_started', 'tool.execution_progress', 'tool.execution_completed', 'tool.execution_failed'],
      organizationId: this.getOrganizationId(),
      callback: (event) => {
        if (event.payload.toolId === toolId) {
          const status: any = { status: event.type.split('.')[1] };
          
          if (event.payload.progress !== undefined) {
            status.progress = event.payload.progress;
          }
          
          if (event.payload.message) {
            status.message = event.payload.message;
          }
          
          if (event.payload.result) {
            status.result = event.payload.result;
          }
          
          callback(status);
        }
      },
    });
  }
}