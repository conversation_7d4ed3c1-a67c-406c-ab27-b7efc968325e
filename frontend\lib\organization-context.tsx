'use client';

import { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { useAuth } from '@/lib/auth-context';
import { organizationApi } from '@/lib/api/organization';

interface OrganizationSettings {
  branding?: {
    logo?: string;
    primaryColor?: string;
    theme?: 'light' | 'dark';
  };
  billing?: {
    planId: string;
    quotas: Record<string, number>;
  };
  features?: string[];
}

interface Organization {
  id: string;
  name: string;
  slug: string;
  settings: OrganizationSettings;
  createdAt: string;
  updatedAt: string;
}

interface Quota {
  type: string;
  limit: number;
  used: number;
  resetAt: string;
}

interface OrganizationContextType {
  organization: Organization | null;
  quotas: Quota[];
  loading: boolean;
  updateSettings: (settings: Partial<OrganizationSettings>) => Promise<void>;
  checkQuota: (type: string) => { available: number; percentage: number; exceeded: boolean };
  refreshQuotas: () => Promise<void>;
  isFeatureEnabled: (feature: string) => boolean;
}

const OrganizationContext = createContext<OrganizationContextType | undefined>(undefined);

export function OrganizationProvider({ children }: { children: ReactNode }) {
  const { user, loading: authLoading } = useAuth();
  const [organization, setOrganization] = useState<Organization | null>(null);
  const [quotas, setQuotas] = useState<Quota[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (!authLoading && user) {
      loadOrganizationData();
    } else if (!authLoading && !user) {
      setOrganization(null);
      setQuotas([]);
      setLoading(false);
    }
  }, [user, authLoading]);

  const loadOrganizationData = async () => {
    try {
      if (!user?.organizationId) return;

      const [orgData, quotasData] = await Promise.all([
        organizationApi.getCurrent(),
        organizationApi.getQuotas(),
      ]);

      setOrganization(orgData);
      setQuotas(quotasData);
    } catch (error) {
      console.error('Failed to load organization data:', error);
    } finally {
      setLoading(false);
    }
  };

  const updateSettings = async (settings: Partial<OrganizationSettings>) => {
    try {
      if (!organization) throw new Error('No organization loaded');

      const updatedOrg = await organizationApi.updateSettings(settings);
      setOrganization(updatedOrg);
    } catch (error) {
      console.error('Failed to update organization settings:', error);
      throw error;
    }
  };

  const checkQuota = (type: string) => {
    const quota = quotas.find(q => q.type === type);
    
    if (!quota) {
      return { available: Infinity, percentage: 0, exceeded: false };
    }

    const available = Math.max(0, quota.limit - quota.used);
    const percentage = quota.limit > 0 ? (quota.used / quota.limit) * 100 : 0;
    const exceeded = quota.used >= quota.limit;

    return { available, percentage, exceeded };
  };

  const refreshQuotas = async () => {
    try {
      const quotasData = await organizationApi.getQuotas();
      setQuotas(quotasData);
    } catch (error) {
      console.error('Failed to refresh quotas:', error);
    }
  };

  const isFeatureEnabled = (feature: string): boolean => {
    if (!organization?.settings?.features) return false;
    return organization.settings.features.includes(feature);
  };

  const value: OrganizationContextType = {
    organization,
    quotas,
    loading: loading || authLoading,
    updateSettings,
    checkQuota,
    refreshQuotas,
    isFeatureEnabled,
  };

  return (
    <OrganizationContext.Provider value={value}>
      {children}
    </OrganizationContext.Provider>
  );
}

export function useOrganization() {
  const context = useContext(OrganizationContext);
  if (context === undefined) {
    throw new Error('useOrganization must be used within an OrganizationProvider');
  }
  return context;
}