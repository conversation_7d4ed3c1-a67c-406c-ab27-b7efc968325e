import { Injectable, UnauthorizedException, BadRequestException } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import * as bcrypt from 'bcryptjs';

import { PrismaService } from '../prisma/prisma.service';
import { LoginDto, RegisterDto } from './dto/auth.dto';

@Injectable()
export class AuthService {
  constructor(
    private prisma: PrismaService,
    private jwtService: JwtService,
  ) {}

  async validateUser(email: string, password: string) {
    const user = await this.prisma.user.findUnique({
      where: { email },
      include: {
        organization: true,
        userRoles: {
          include: {
            role: true,
          },
        },
      },
    });

    if (!user || !user.passwordHash) {
      throw new UnauthorizedException('Invalid credentials');
    }

    const isPasswordValid = await bcrypt.compare(password, user.passwordHash);
    if (!isPasswordValid) {
      throw new UnauthorizedException('Invalid credentials');
    }

    const { passwordHash, ...userWithoutPassword } = user;
    return userWithoutPassword;
  }

  async login(loginDto: LoginDto) {
    const user = await this.validateUser(loginDto.email, loginDto.password);

    await this.prisma.user.update({
      where: { id: user.id },
      data: { lastLoginAt: new Date() },
    });

    const payload = {
      sub: user.id,
      email: user.email,
      organizationId: user.organizationId,
      roles: user.userRoles.map(ur => ur.role.name),
    };

    return {
      access_token: this.jwtService.sign(payload),
      user: {
        id: user.id,
        email: user.email,
        name: user.name,
        avatarUrl: user.avatarUrl,
        organizationId: user.organizationId,
        organization: user.organization,
        roles: user.userRoles.map(ur => ur.role),
        settings: user.settings,
      },
    };
  }

  async register(registerDto: RegisterDto) {
    const existingUser = await this.prisma.user.findUnique({
      where: { email: registerDto.email },
    });

    if (existingUser) {
      throw new BadRequestException('User already exists');
    }

    const existingOrg = await this.prisma.organization.findUnique({
      where: { slug: registerDto.organizationSlug },
    });

    if (existingOrg) {
      throw new BadRequestException('Organization slug already taken');
    }

    const hashedPassword = await bcrypt.hash(registerDto.password, 12);

    const result = await this.prisma.$transaction(async (prisma) => {
      const organization = await prisma.organization.create({
        data: {
          name: registerDto.organizationName,
          slug: registerDto.organizationSlug,
          settings: {
            branding: { theme: 'light' },
            features: ['agents', 'tools'],
          },
        },
      });

      const adminRole = await prisma.role.create({
        data: {
          name: 'Admin',
          description: 'Organization administrator',
          organizationId: organization.id,
          permissions: [
            'agents:create', 'agents:read', 'agents:update', 'agents:delete',
            'tools:create', 'tools:read', 'tools:update', 'tools:delete',
            'sessions:create', 'sessions:read', 'sessions:update', 'sessions:delete',
            'analytics:read', 'billing:read', 'admin:manage',
          ],
        },
      });

      const user = await prisma.user.create({
        data: {
          email: registerDto.email,
          name: registerDto.name,
          passwordHash: hashedPassword,
          organizationId: organization.id,
          settings: {
            theme: 'light',
            notifications: {
              email: true,
              sms: false,
              webhook: false,
              push: true,
            },
          },
        },
      });

      await prisma.userRole.create({
        data: {
          userId: user.id,
          roleId: adminRole.id,
        },
      });

      await prisma.quota.createMany({
        data: [
          {
            organizationId: organization.id,
            type: 'agents',
            limit: 5,
            resetAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
          },
          {
            organizationId: organization.id,
            type: 'tools',
            limit: 10,
            resetAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
          },
          {
            organizationId: organization.id,
            type: 'api_calls',
            limit: 100,
            resetAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
          },
        ],
      });

      return { user, organization, role: adminRole };
    });

    const payload = {
      sub: result.user.id,
      email: result.user.email,
      organizationId: result.user.organizationId,
      roles: [result.role.name],
    };

    return {
      access_token: this.jwtService.sign(payload),
      user: {
        id: result.user.id,
        email: result.user.email,
        name: result.user.name,
        organizationId: result.user.organizationId,
        organization: result.organization,
        roles: [result.role],
        settings: result.user.settings,
      },
    };
  }

  async verifyToken(token: string) {
    try {
      const payload = this.jwtService.verify(token);
      const user = await this.prisma.user.findUnique({
        where: { id: payload.sub },
        include: {
          organization: true,
          userRoles: {
            include: {
              role: true,
            },
          },
        },
      });

      if (!user) {
        throw new UnauthorizedException('User not found');
      }

      const { passwordHash, ...userWithoutPassword } = user;
      return userWithoutPassword;
    } catch (error) {
      throw new UnauthorizedException('Invalid token');
    }
  }
}