import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

export enum CircuitBreakerState {
  CLOSED = 'closed',     // Normal operation
  OPEN = 'open',         // Circuit is open, reject requests
  HALF_OPEN = 'half_open' // Testing if service is back
}

interface CircuitBreakerConfig {
  failureThreshold: number;        // Number of failures before opening
  successThreshold: number;        // Number of successes needed to close
  timeout: number;                 // Time in ms before trying half-open
  monitoringPeriod: number;       // Time window for failure counting
  volumeThreshold: number;        // Minimum requests before considering failures
}

interface CircuitBreakerMetrics {
  state: CircuitBreakerState;
  failureCount: number;
  successCount: number;
  requestCount: number;
  lastFailureTime: number;
  lastSuccessTime: number;
  stateChangedTime: number;
  halfOpenSuccessCount: number;
}

@Injectable()
export class CircuitBreakerService {
  private readonly logger = new Logger(CircuitBreakerService.name);
  private readonly circuits = new Map<string, CircuitBreakerMetrics>();
  private readonly configs = new Map<string, CircuitBreakerConfig>();

  constructor(private configService: ConfigService) {
    // Start monitoring loop
    this.startMonitoring();
  }

  initializeProvider(providerId: string, customConfig?: Partial<CircuitBreakerConfig>) {
    const defaultConfig: CircuitBreakerConfig = {
      failureThreshold: 5,           // Open after 5 failures
      successThreshold: 3,           // Close after 3 successes in half-open
      timeout: 60000,                // 1 minute timeout
      monitoringPeriod: 300000,      // 5 minute monitoring window
      volumeThreshold: 10,           // At least 10 requests needed
    };

    const config = { ...defaultConfig, ...customConfig };
    this.configs.set(providerId, config);

    const metrics: CircuitBreakerMetrics = {
      state: CircuitBreakerState.CLOSED,
      failureCount: 0,
      successCount: 0,
      requestCount: 0,
      lastFailureTime: 0,
      lastSuccessTime: Date.now(),
      stateChangedTime: Date.now(),
      halfOpenSuccessCount: 0,
    };

    this.circuits.set(providerId, metrics);
    this.logger.log(`Initialized circuit breaker for provider ${providerId}`);
  }

  canExecute(providerId: string): boolean {
    const metrics = this.circuits.get(providerId);
    const config = this.configs.get(providerId);

    if (!metrics || !config) {
      this.logger.warn(`Circuit breaker not initialized for provider ${providerId}`);
      return false;
    }

    const now = Date.now();

    switch (metrics.state) {
      case CircuitBreakerState.CLOSED:
        return true;

      case CircuitBreakerState.OPEN:
        // Check if timeout has passed
        if (now - metrics.stateChangedTime >= config.timeout) {
          this.transitionToHalfOpen(providerId);
          return true;
        }
        return false;

      case CircuitBreakerState.HALF_OPEN:
        return true;

      default:
        return false;
    }
  }

  recordSuccess(providerId: string) {
    const metrics = this.circuits.get(providerId);
    const config = this.configs.get(providerId);

    if (!metrics || !config) {
      this.logger.warn(`Circuit breaker not initialized for provider ${providerId}`);
      return;
    }

    const now = Date.now();
    metrics.successCount++;
    metrics.requestCount++;
    metrics.lastSuccessTime = now;

    switch (metrics.state) {
      case CircuitBreakerState.HALF_OPEN:
        metrics.halfOpenSuccessCount++;
        if (metrics.halfOpenSuccessCount >= config.successThreshold) {
          this.transitionToClosed(providerId);
        }
        break;

      case CircuitBreakerState.CLOSED:
        // Reset failure count on success
        metrics.failureCount = 0;
        break;
    }

    this.logMetrics(providerId, 'SUCCESS');
  }

  recordFailure(providerId: string, error?: Error) {
    const metrics = this.circuits.get(providerId);
    const config = this.configs.get(providerId);

    if (!metrics || !config) {
      this.logger.warn(`Circuit breaker not initialized for provider ${providerId}`);
      return;
    }

    const now = Date.now();
    metrics.failureCount++;
    metrics.requestCount++;
    metrics.lastFailureTime = now;

    switch (metrics.state) {
      case CircuitBreakerState.HALF_OPEN:
        // Any failure in half-open state should open the circuit
        this.transitionToOpen(providerId);
        break;

      case CircuitBreakerState.CLOSED:
        // Check if we should open the circuit
        if (this.shouldOpen(providerId)) {
          this.transitionToOpen(providerId);
        }
        break;
    }

    this.logMetrics(providerId, 'FAILURE', error?.message);
  }

  resetProvider(providerId: string) {
    const metrics = this.circuits.get(providerId);
    
    if (!metrics) {
      this.logger.warn(`Circuit breaker not initialized for provider ${providerId}`);
      return;
    }

    metrics.state = CircuitBreakerState.CLOSED;
    metrics.failureCount = 0;
    metrics.successCount = 0;
    metrics.halfOpenSuccessCount = 0;
    metrics.stateChangedTime = Date.now();

    this.logger.log(`Reset circuit breaker for provider ${providerId}`);
  }

  removeProvider(providerId: string) {
    this.circuits.delete(providerId);
    this.configs.delete(providerId);
    this.logger.log(`Removed circuit breaker for provider ${providerId}`);
  }

  getProviderMetrics(providerId: string): CircuitBreakerMetrics | null {
    return this.circuits.get(providerId) || null;
  }

  getAllMetrics(): Record<string, CircuitBreakerMetrics> {
    const result = {};
    this.circuits.forEach((metrics, providerId) => {
      result[providerId] = { ...metrics };
    });
    return result;
  }

  getHealthStatus(providerId: string): {
    isHealthy: boolean;
    state: CircuitBreakerState;
    failureRate: number;
    lastFailure: number;
    canExecute: boolean;
  } {
    const metrics = this.circuits.get(providerId);
    const config = this.configs.get(providerId);

    if (!metrics || !config) {
      return {
        isHealthy: false,
        state: CircuitBreakerState.OPEN,
        failureRate: 100,
        lastFailure: 0,
        canExecute: false,
      };
    }

    const failureRate = metrics.requestCount > 0 
      ? (metrics.failureCount / metrics.requestCount) * 100 
      : 0;

    const isHealthy = metrics.state === CircuitBreakerState.CLOSED && 
                     failureRate < (config.failureThreshold / config.volumeThreshold) * 100;

    return {
      isHealthy,
      state: metrics.state,
      failureRate: Math.round(failureRate * 100) / 100,
      lastFailure: metrics.lastFailureTime,
      canExecute: this.canExecute(providerId),
    };
  }

  // Private methods

  private shouldOpen(providerId: string): boolean {
    const metrics = this.circuits.get(providerId);
    const config = this.configs.get(providerId);

    if (!metrics || !config) return false;

    // Need minimum volume of requests
    if (metrics.requestCount < config.volumeThreshold) {
      return false;
    }

    // Check failure threshold within monitoring period
    const now = Date.now();
    const monitoringWindowStart = now - config.monitoringPeriod;

    // For simplicity, we're using total failures vs threshold
    // In production, you'd track failures within the time window
    return metrics.failureCount >= config.failureThreshold;
  }

  private transitionToOpen(providerId: string) {
    const metrics = this.circuits.get(providerId);
    if (!metrics) return;

    metrics.state = CircuitBreakerState.OPEN;
    metrics.stateChangedTime = Date.now();
    metrics.halfOpenSuccessCount = 0;

    this.logger.warn(`Circuit breaker OPENED for provider ${providerId}`);
  }

  private transitionToHalfOpen(providerId: string) {
    const metrics = this.circuits.get(providerId);
    if (!metrics) return;

    metrics.state = CircuitBreakerState.HALF_OPEN;
    metrics.stateChangedTime = Date.now();
    metrics.halfOpenSuccessCount = 0;

    this.logger.log(`Circuit breaker HALF-OPEN for provider ${providerId}`);
  }

  private transitionToClosed(providerId: string) {
    const metrics = this.circuits.get(providerId);
    if (!metrics) return;

    metrics.state = CircuitBreakerState.CLOSED;
    metrics.stateChangedTime = Date.now();
    metrics.failureCount = 0;
    metrics.halfOpenSuccessCount = 0;

    this.logger.log(`Circuit breaker CLOSED for provider ${providerId}`);
  }

  private logMetrics(providerId: string, event: string, details?: string) {
    const metrics = this.circuits.get(providerId);
    if (!metrics) return;

    this.logger.debug(`Provider ${providerId} - ${event}`, {
      state: metrics.state,
      failures: metrics.failureCount,
      successes: metrics.successCount,
      requests: metrics.requestCount,
      details,
    });
  }

  private startMonitoring() {
    // Reset metrics periodically to prevent memory leaks and stale data
    setInterval(() => {
      this.cleanupStaleMetrics();
    }, 300000); // Every 5 minutes

    // Log health status periodically
    setInterval(() => {
      this.logHealthSummary();
    }, 60000); // Every minute
  }

  private cleanupStaleMetrics() {
    const now = Date.now();
    const staleThreshold = 24 * 60 * 60 * 1000; // 24 hours

    this.circuits.forEach((metrics, providerId) => {
      const config = this.configs.get(providerId);
      if (!config) return;

      // Reset metrics if they're too old
      if (now - metrics.stateChangedTime > staleThreshold) {
        metrics.failureCount = 0;
        metrics.successCount = 0;
        metrics.requestCount = 0;
      }

      // Reset counts periodically in closed state to prevent accumulation
      if (metrics.state === CircuitBreakerState.CLOSED && 
          now - metrics.stateChangedTime > config.monitoringPeriod) {
        metrics.failureCount = Math.floor(metrics.failureCount * 0.9); // Decay failures
        metrics.successCount = Math.floor(metrics.successCount * 0.9); // Decay successes
        metrics.requestCount = Math.floor(metrics.requestCount * 0.9); // Decay requests
      }
    });
  }

  private logHealthSummary() {
    const summary = {
      totalProviders: this.circuits.size,
      healthy: 0,
      unhealthy: 0,
      halfOpen: 0,
    };

    this.circuits.forEach((metrics, providerId) => {
      switch (metrics.state) {
        case CircuitBreakerState.CLOSED:
          summary.healthy++;
          break;
        case CircuitBreakerState.OPEN:
          summary.unhealthy++;
          break;
        case CircuitBreakerState.HALF_OPEN:
          summary.halfOpen++;
          break;
      }
    });

    if (summary.unhealthy > 0 || summary.halfOpen > 0) {
      this.logger.warn('Circuit Breaker Health Summary', summary);
    } else {
      this.logger.debug('Circuit Breaker Health Summary', summary);
    }
  }

  // Utility methods for testing and monitoring

  forceOpen(providerId: string) {
    const metrics = this.circuits.get(providerId);
    if (metrics) {
      this.transitionToOpen(providerId);
      this.logger.warn(`Manually opened circuit breaker for provider ${providerId}`);
    }
  }

  forceClose(providerId: string) {
    const metrics = this.circuits.get(providerId);
    if (metrics) {
      this.transitionToClosed(providerId);
      this.logger.log(`Manually closed circuit breaker for provider ${providerId}`);
    }
  }

  getDetailedStats(providerId: string): any {
    const metrics = this.circuits.get(providerId);
    const config = this.configs.get(providerId);

    if (!metrics || !config) return null;

    const now = Date.now();
    const uptime = now - metrics.stateChangedTime;
    const failureRate = metrics.requestCount > 0 
      ? (metrics.failureCount / metrics.requestCount) * 100 
      : 0;

    return {
      providerId,
      state: metrics.state,
      config,
      metrics: {
        ...metrics,
        uptime,
        failureRate: Math.round(failureRate * 100) / 100,
        avgRequestsPerMinute: metrics.requestCount > 0 
          ? Math.round((metrics.requestCount / (uptime / 60000)) * 100) / 100 
          : 0,
      },
      timings: {
        lastFailureAgo: now - metrics.lastFailureTime,
        lastSuccessAgo: now - metrics.lastSuccessTime,
        stateChangedAgo: uptime,
      },
    };
  }
}