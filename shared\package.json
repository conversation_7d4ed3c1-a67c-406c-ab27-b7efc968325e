{"name": "@synapseai/shared", "version": "1.0.0", "description": "Shared TypeScript types and schemas for SynapseAI platform", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "test": "jest", "lint": "eslint src/**/*.ts", "type-check": "tsc --noEmit"}, "dependencies": {"zod": "^3.22.4"}, "devDependencies": {"@types/jest": "^29.5.8", "@types/node": "^20.0.0", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "eslint": "^8.54.0", "jest": "^29.7.0", "ts-jest": "^29.1.1", "typescript": "^5.3.3"}}