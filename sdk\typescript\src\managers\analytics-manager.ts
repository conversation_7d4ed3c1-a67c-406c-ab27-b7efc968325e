import { PaginatedResponse, QueryParams } from '../types';
import { BaseManager } from './base-manager';

export interface AnalyticsEvent {
  id: string;
  type: string;
  category: string;
  action: string;
  label?: string;
  value?: number;
  properties: Record<string, any>;
  userId?: string;
  sessionId?: string;
  organizationId: string;
  timestamp: string;
}

export interface AnalyticsQueryParams extends QueryParams {
  category?: string;
  action?: string;
  startDate?: string;
  endDate?: string;
  userId?: string;
  sessionId?: string;
}

export interface MetricsSummary {
  totalEvents: number;
  uniqueUsers: number;
  uniqueSessions: number;
  timeRange: {
    start: string;
    end: string;
  };
  topCategories: Array<{
    category: string;
    count: number;
    percentage: number;
  }>;
  topActions: Array<{
    action: string;
    count: number;
    percentage: number;
  }>;
  eventsByHour: Array<{
    hour: number;
    count: number;
  }>;
  eventsByDay: Array<{
    date: string;
    count: number;
  }>;
}

export interface UserAnalytics {
  userId: string;
  totalEvents: number;
  firstSeen: string;
  lastSeen: string;
  sessionsCount: number;
  avgSessionDuration: number;
  topActions: Array<{
    action: string;
    count: number;
  }>;
  activityTimeline: Array<{
    date: string;
    events: number;
  }>;
}

export interface PerformanceMetrics {
  averageResponseTime: number;
  successRate: number;
  errorRate: number;
  totalRequests: number;
  requestsByEndpoint: Record<string, {
    count: number;
    avgResponseTime: number;
    errorRate: number;
  }>;
  responseTimePercentiles: {
    p50: number;
    p95: number;
    p99: number;
  };
  errors: Array<{
    endpoint: string;
    error: string;
    count: number;
    lastOccurred: string;
  }>;
}

export interface FunnelAnalysis {
  steps: Array<{
    name: string;
    totalUsers: number;
    conversionRate: number;
    dropoffRate: number;
  }>;
  totalConversionRate: number;
  avgTimeToConvert: number;
  dropoffPoints: Array<{
    step: string;
    dropoffRate: number;
    reasons: string[];
  }>;
}

export interface RetentionAnalysis {
  cohorts: Array<{
    cohortDate: string;
    totalUsers: number;
    retentionByPeriod: Record<string, number>;
  }>;
  overallRetentionRate: number;
  avgRetentionTime: number;
  churnRate: number;
  retentionCurve: Array<{
    period: string;
    retentionRate: number;
  }>;
}

export class AnalyticsManager extends BaseManager {
  /**
   * Track an analytics event
   */
  async track(event: {
    category: string;
    action: string;
    label?: string;
    value?: number;
    properties?: Record<string, any>;
    userId?: string;
    sessionId?: string;
  }): Promise<{ eventId: string; tracked: boolean }> {
    this.validateRequired(event, ['category', 'action']);
    
    return this.request({
      method: 'POST',
      url: '/analytics/track',
      data: event,
    });
  }

  /**
   * Batch track multiple events
   */
  async trackBatch(events: Array<{
    category: string;
    action: string;
    label?: string;
    value?: number;
    properties?: Record<string, any>;
    userId?: string;
    sessionId?: string;
    timestamp?: string;
  }>): Promise<{
    tracked: number;
    failed: number;
    errors: string[];
  }> {
    this.validateRequired({ events }, ['events']);
    
    return this.request({
      method: 'POST',
      url: '/analytics/track/batch',
      data: { events },
    });
  }

  /**
   * Get analytics events
   */
  async getEvents(params?: AnalyticsQueryParams): Promise<PaginatedResponse<AnalyticsEvent>> {
    return this.request({
      method: 'GET',
      url: '/analytics/events',
      params: this.buildQueryParams(params || {}),
    });
  }

  /**
   * Get metrics summary
   */
  async getMetricsSummary(options?: {
    startDate?: string;
    endDate?: string;
    category?: string;
    groupBy?: 'hour' | 'day' | 'week' | 'month';
  }): Promise<MetricsSummary> {
    return this.request({
      method: 'GET',
      url: '/analytics/metrics/summary',
      params: this.buildQueryParams(options || {}),
    });
  }

  /**
   * Get user analytics
   */
  async getUserAnalytics(userId: string, options?: {
    startDate?: string;
    endDate?: string;
  }): Promise<UserAnalytics> {
    this.validateRequired({ userId }, ['userId']);
    
    return this.request({
      method: 'GET',
      url: `/analytics/users/${userId}`,
      params: this.buildQueryParams(options || {}),
    });
  }

  /**
   * Get organization analytics overview
   */
  async getOrganizationOverview(options?: {
    timeRange?: '1h' | '24h' | '7d' | '30d' | '90d';
  }): Promise<{
    totalUsers: number;
    activeUsers: number;
    totalSessions: number;
    avgSessionDuration: number;
    totalEvents: number;
    growth: {
      users: number;
      sessions: number;
      events: number;
    };
    topFeatures: Array<{
      feature: string;
      usage: number;
      trend: number;
    }>;
  }> {
    return this.request({
      method: 'GET',
      url: '/analytics/overview',
      params: this.buildQueryParams(options || {}),
    });
  }

  /**
   * Get performance metrics
   */
  async getPerformanceMetrics(options?: {
    startDate?: string;
    endDate?: string;
    service?: string;
  }): Promise<PerformanceMetrics> {
    return this.request({
      method: 'GET',
      url: '/analytics/performance',
      params: this.buildQueryParams(options || {}),
    });
  }

  /**
   * Get real-time analytics
   */
  async getRealTimeAnalytics(): Promise<{
    activeUsers: number;
    activeAgents: number;
    activeTools: number;
    currentExecutions: number;
    eventsPerSecond: number;
    topActivity: Array<{
      type: string;
      count: number;
    }>;
    recentEvents: AnalyticsEvent[];
  }> {
    return this.request({
      method: 'GET',
      url: '/analytics/realtime',
    });
  }

  /**
   * Create custom dashboard
   */
  async createDashboard(dashboard: {
    name: string;
    description?: string;
    widgets: Array<{
      type: 'metric' | 'chart' | 'table' | 'funnel';
      title: string;
      query: Record<string, any>;
      visualization?: Record<string, any>;
    }>;
    isPublic?: boolean;
    metadata?: Record<string, any>;
  }): Promise<{
    id: string;
    name: string;
    url: string;
    createdAt: string;
  }> {
    this.validateRequired(dashboard, ['name', 'widgets']);
    
    return this.request({
      method: 'POST',
      url: '/analytics/dashboards',
      data: dashboard,
    });
  }

  /**
   * Get custom dashboards
   */
  async getDashboards(): Promise<Array<{
    id: string;
    name: string;
    description?: string;
    isPublic: boolean;
    widgetCount: number;
    createdAt: string;
    updatedAt: string;
  }>> {
    return this.request({
      method: 'GET',
      url: '/analytics/dashboards',
    });
  }

  /**
   * Get dashboard data
   */
  async getDashboard(id: string): Promise<{
    id: string;
    name: string;
    description?: string;
    widgets: Array<{
      id: string;
      type: string;
      title: string;
      data: any;
      visualization: any;
    }>;
    isPublic: boolean;
    metadata: Record<string, any>;
  }> {
    this.validateRequired({ id }, ['id']);
    
    return this.request({
      method: 'GET',
      url: `/analytics/dashboards/${id}`,
    });
  }

  /**
   * Update dashboard
   */
  async updateDashboard(id: string, updates: {
    name?: string;
    description?: string;
    widgets?: Array<{
      type: 'metric' | 'chart' | 'table' | 'funnel';
      title: string;
      query: Record<string, any>;
      visualization?: Record<string, any>;
    }>;
    isPublic?: boolean;
    metadata?: Record<string, any>;
  }): Promise<void> {
    this.validateRequired({ id }, ['id']);
    
    await this.request({
      method: 'PUT',
      url: `/analytics/dashboards/${id}`,
      data: updates,
    });
  }

  /**
   * Delete dashboard
   */
  async deleteDashboard(id: string): Promise<void> {
    this.validateRequired({ id }, ['id']);
    
    await this.request({
      method: 'DELETE',
      url: `/analytics/dashboards/${id}`,
    });
  }

  /**
   * Create funnel analysis
   */
  async createFunnel(funnel: {
    name: string;
    steps: Array<{
      name: string;
      conditions: Record<string, any>;
    }>;
    timeRange: {
      start: string;
      end: string;
    };
    conversionWindow?: number; // hours
  }): Promise<FunnelAnalysis> {
    this.validateRequired(funnel, ['name', 'steps', 'timeRange']);
    
    return this.request({
      method: 'POST',
      url: '/analytics/funnels',
      data: funnel,
    });
  }

  /**
   * Get funnel analysis
   */
  async getFunnel(id: string): Promise<FunnelAnalysis> {
    this.validateRequired({ id }, ['id']);
    
    return this.request({
      method: 'GET',
      url: `/analytics/funnels/${id}`,
    });
  }

  /**
   * Create retention analysis
   */
  async createRetentionAnalysis(analysis: {
    name: string;
    cohortBy: 'signup' | 'first_action' | 'custom';
    timeUnit: 'day' | 'week' | 'month';
    periods: number;
    retentionEvent: Record<string, any>;
    startDate: string;
    endDate: string;
  }): Promise<RetentionAnalysis> {
    this.validateRequired(analysis, ['name', 'cohortBy', 'timeUnit', 'periods', 'retentionEvent', 'startDate', 'endDate']);
    
    return this.request({
      method: 'POST',
      url: '/analytics/retention',
      data: analysis,
    });
  }

  /**
   * Get A/B test results
   */
  async getABTestResults(testId: string): Promise<{
    testId: string;
    testName: string;
    status: 'draft' | 'running' | 'completed' | 'stopped';
    variants: Array<{
      name: string;
      traffic: number;
      conversions: number;
      conversionRate: number;
      confidence: number;
      isWinner: boolean;
    }>;
    statistics: {
      significanceLevel: number;
      pValue: number;
      confidenceInterval: [number, number];
    };
    startDate: string;
    endDate?: string;
  }> {
    this.validateRequired({ testId }, ['testId']);
    
    return this.request({
      method: 'GET',
      url: `/analytics/ab-tests/${testId}/results`,
    });
  }

  /**
   * Get cohort analysis
   */
  async getCohortAnalysis(options: {
    cohortBy: 'signup' | 'first_purchase' | 'custom';
    timeUnit: 'day' | 'week' | 'month';
    startDate: string;
    endDate: string;
    cohortEvent?: Record<string, any>;
    retentionEvent?: Record<string, any>;
  }): Promise<{
    cohorts: Array<{
      cohortDate: string;
      cohortSize: number;
      retentionByPeriod: Array<{
        period: number;
        users: number;
        percentage: number;
      }>;
    }>;
    averageRetention: Array<{
      period: number;
      percentage: number;
    }>;
  }> {
    this.validateRequired(options, ['cohortBy', 'timeUnit', 'startDate', 'endDate']);
    
    return this.request({
      method: 'POST',
      url: '/analytics/cohorts',
      data: options,
    });
  }

  /**
   * Export analytics data
   */
  async exportData(options: {
    format: 'csv' | 'json' | 'excel';
    query: Record<string, any>;
    startDate: string;
    endDate: string;
    includeMetadata?: boolean;
  }): Promise<{
    exportId: string;
    downloadUrl: string;
    expiresAt: string;
  }> {
    this.validateRequired(options, ['format', 'query', 'startDate', 'endDate']);
    
    return this.request({
      method: 'POST',
      url: '/analytics/export',
      data: options,
    });
  }

  /**
   * Set up custom alerts
   */
  async createAlert(alert: {
    name: string;
    description?: string;
    query: Record<string, any>;
    condition: {
      type: 'threshold' | 'change' | 'anomaly';
      operator?: '>' | '<' | '=' | '>=' | '<=';
      value?: number;
      percentage?: number;
    };
    notifications: Array<{
      type: 'email' | 'slack' | 'webhook';
      configuration: Record<string, any>;
    }>;
    frequency: 'realtime' | 'hourly' | 'daily' | 'weekly';
    enabled?: boolean;
  }): Promise<{
    id: string;
    name: string;
    enabled: boolean;
    createdAt: string;
  }> {
    this.validateRequired(alert, ['name', 'query', 'condition', 'notifications', 'frequency']);
    
    return this.request({
      method: 'POST',
      url: '/analytics/alerts',
      data: alert,
    });
  }

  /**
   * Get analytics alerts
   */
  async getAlerts(): Promise<Array<{
    id: string;
    name: string;
    description?: string;
    enabled: boolean;
    frequency: string;
    lastTriggered?: string;
    createdAt: string;
  }>> {
    return this.request({
      method: 'GET',
      url: '/analytics/alerts',
    });
  }

  /**
   * Update alert
   */
  async updateAlert(id: string, updates: {
    name?: string;
    description?: string;
    enabled?: boolean;
    query?: Record<string, any>;
    condition?: Record<string, any>;
    notifications?: Array<Record<string, any>>;
    frequency?: string;
  }): Promise<void> {
    this.validateRequired({ id }, ['id']);
    
    await this.request({
      method: 'PUT',
      url: `/analytics/alerts/${id}`,
      data: updates,
    });
  }

  /**
   * Delete alert
   */
  async deleteAlert(id: string): Promise<void> {
    this.validateRequired({ id }, ['id']);
    
    await this.request({
      method: 'DELETE',
      url: `/analytics/alerts/${id}`,
    });
  }

  /**
   * Subscribe to analytics events
   */
  onAnalyticsEvent(callback: (event: any) => void): string {
    return this.subscribe({
      eventTypes: [
        'analytics.event_tracked',
        'analytics.alert_triggered',
        'analytics.dashboard_updated'
      ],
      organizationId: this.getOrganizationId(),
      callback,
    });
  }

  /**
   * Monitor real-time analytics
   */
  async monitorRealTime(callback: (data: {
    timestamp: string;
    activeUsers: number;
    eventsPerSecond: number;
    topActivity: Array<{ type: string; count: number }>;
  }) => void): Promise<string> {
    return this.subscribe({
      eventTypes: ['analytics.realtime_update'],
      organizationId: this.getOrganizationId(),
      callback: (event) => {
        callback({
          timestamp: event.timestamp,
          activeUsers: event.payload.activeUsers,
          eventsPerSecond: event.payload.eventsPerSecond,
          topActivity: event.payload.topActivity,
        });
      },
    });
  }
}