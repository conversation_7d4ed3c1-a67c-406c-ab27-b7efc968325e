import { IsString, IsOptional, <PERSON><PERSON><PERSON>, IsObject, IsNumber, Min, Max, IsArray, IsBoolean, IsUrl } from 'class-validator';
import { Type } from 'class-transformer';

export enum ToolType {
  API = 'api',
  FUNCTION = 'function',
  WORKFLOW = 'workflow',
}

export enum ToolStatus {
  DRAFT = 'draft',
  ACTIVE = 'active',
  ARCHIVED = 'archived',
}

export enum HttpMethod {
  GET = 'GET',
  POST = 'POST',
  PUT = 'PUT',
  DELETE = 'DELETE',
  PATCH = 'PATCH',
}

export enum AuthType {
  NONE = 'none',
  API_KEY = 'api_key',
  BEARER = 'bearer',
  BASIC = 'basic',
  OAUTH2 = 'oauth2',
}

export class ToolConfigDto {
  @IsOptional()
  @IsUrl()
  endpoint?: string;

  @IsOptional()
  @IsEnum(HttpMethod)
  method?: HttpMethod;

  @IsOptional()
  @IsObject()
  headers?: Record<string, string>;

  @IsOptional()
  @IsObject()
  authentication?: {
    type: AuthType;
    credentials: Record<string, string>;
  };

  @IsOptional()
  @IsNumber()
  @Min(1000)
  @Max(300000)
  timeout?: number = 30000;

  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(10)
  retries?: number = 3;

  @IsOptional()
  @IsObject()
  parameters?: Record<string, any>;

  @IsOptional()
  @IsString()
  functionCode?: string;

  @IsOptional()
  @IsArray()
  @IsObject({ each: true })
  workflowSteps?: Array<{
    id: string;
    type: 'api' | 'function' | 'condition';
    config: any;
    nextSteps?: string[];
  }>;
}

export class ToolSchemaDto {
  @IsObject()
  input: Record<string, any>;

  @IsObject()
  output: Record<string, any>;

  @IsOptional()
  @IsArray()
  @IsObject({ each: true })
  examples?: Array<{
    name: string;
    description: string;
    input: any;
    output: any;
  }>;
}

export class CreateToolDto {
  @IsString()
  name: string;

  @IsString()
  description: string;

  @IsEnum(ToolType)
  type: ToolType;

  @IsObject()
  @Type(() => ToolConfigDto)
  config: ToolConfigDto;

  @IsObject()
  @Type(() => ToolSchemaDto)
  schema: ToolSchemaDto;

  @IsOptional()
  @IsEnum(ToolStatus)
  status?: ToolStatus = ToolStatus.DRAFT;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];

  @IsOptional()
  @IsObject()
  metadata?: {
    version?: string;
    author?: string;
    category?: string;
    documentation?: string;
  };
}

export class UpdateToolDto {
  @IsOptional()
  @IsString()
  name?: string;

  @IsOptional()
  @IsString()
  description?: string;

  @IsOptional()
  @IsObject()
  @Type(() => ToolConfigDto)
  config?: Partial<ToolConfigDto>;

  @IsOptional()
  @IsObject()
  @Type(() => ToolSchemaDto)
  schema?: Partial<ToolSchemaDto>;

  @IsOptional()
  @IsEnum(ToolStatus)
  status?: ToolStatus;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];

  @IsOptional()
  @IsObject()
  metadata?: any;
}

export class ToolQueryDto {
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  page?: number = 1;

  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  @Max(100)
  limit?: number = 20;

  @IsOptional()
  @IsEnum(ToolType)
  type?: ToolType;

  @IsOptional()
  @IsEnum(ToolStatus)
  status?: ToolStatus;

  @IsOptional()
  @IsString()
  search?: string;

  @IsOptional()
  @IsString()
  category?: string;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];

  @IsOptional()
  @IsString()
  sortBy?: string = 'createdAt';

  @IsOptional()
  @IsEnum(['asc', 'desc'])
  sortOrder?: 'asc' | 'desc' = 'desc';
}

export class ExecuteToolDto {
  @IsObject()
  input: Record<string, any>;

  @IsOptional()
  @IsString()
  sessionId?: string;

  @IsOptional()
  @IsObject()
  context?: Record<string, any>;

  @IsOptional()
  @IsBoolean()
  validateInput?: boolean = true;

  @IsOptional()
  @IsObject()
  overrides?: {
    timeout?: number;
    retries?: number;
    headers?: Record<string, string>;
  };
}

export class TestToolDto {
  @IsObject()
  input: Record<string, any>;

  @IsOptional()
  @IsBoolean()
  dryRun?: boolean = false;
}

export class ToolVersionDto {
  @IsString()
  version: string;

  @IsString()
  description: string;

  @IsObject()
  config: ToolConfigDto;

  @IsObject()
  schema: ToolSchemaDto;

  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;
}