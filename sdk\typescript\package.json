{"name": "@synapseai/sdk", "version": "1.1.0", "description": "Universal TypeScript/JavaScript SDK for SynapseAI platform with workflow orchestration", "main": "dist/index.js", "module": "dist/index.esm.js", "types": "dist/index.d.ts", "files": ["dist"], "scripts": {"build": "rollup -c", "build:watch": "rollup -c -w", "dev": "rollup -c -w", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src --ext .ts", "type-check": "tsc --noEmit", "prepublishOnly": "npm run build"}, "keywords": ["synapseai", "ai", "agents", "tools", "workflows", "automation", "orchestration", "sdk", "typescript", "javascript"], "author": "SynapseAI Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/synapseai/sdk-typescript"}, "bugs": {"url": "https://github.com/synapseai/sdk-typescript/issues"}, "homepage": "https://docs.synapseai.com/sdk", "dependencies": {"axios": "^1.6.0", "ws": "^8.14.0", "eventemitter3": "^5.0.0"}, "devDependencies": {"@types/jest": "^29.5.0", "@types/node": "^20.0.0", "@types/ws": "^8.5.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.50.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "jest": "^29.7.0", "prettier": "^3.0.0", "rollup": "^4.0.0", "@rollup/plugin-typescript": "^11.0.0", "@rollup/plugin-node-resolve": "^15.0.0", "@rollup/plugin-commonjs": "^25.0.0", "rollup-plugin-terser": "^7.0.0", "ts-jest": "^29.1.0", "typescript": "^5.2.0"}, "engines": {"node": ">=16.0.0"}, "exports": {".": {"import": "./dist/index.esm.js", "require": "./dist/index.js", "types": "./dist/index.d.ts"}}}