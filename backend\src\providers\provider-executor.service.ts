import { Injectable, BadRequestException, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PrismaService } from '../prisma/prisma.service';
import { APIMXService } from '../apix/apix.service';
import { SessionsService } from '../sessions/sessions.service';
import { ProviderExecutionDto, ProviderType } from './dto/provider.dto';
import OpenAI from 'openai';
import Anthropic from '@anthropic-ai/sdk';
import { GoogleGenerativeAI } from '@google/generative-ai';
import Groq from 'groq-sdk';
import axios, { AxiosRequestConfig } from 'axios';

export interface ProviderExecutionResult {
  success: boolean;
  response: string;
  usage: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  };
  cost: number;
  executionTime: number;
  modelId: string;
  providerId: string;
  sessionId?: string;
  metadata: {
    finishReason?: string;
    model?: string;
    version?: string;
    latency?: number;
    retryCount?: number;
  };
}

interface ProviderClient {
  openai?: OpenAI;
  claude?: Anthropic;
  gemini?: GoogleGenerativeAI;
  groq?: Groq;
  custom?: any;
}

@Injectable()
export class ProviderExecutorService {
  private readonly logger = new Logger(ProviderExecutorService.name);
  private readonly clients = new Map<string, ProviderClient>();

  constructor(
    private prisma: PrismaService,
    private apixService: APIMXService,
    private sessionsService: SessionsService,
    private configService: ConfigService,
  ) {}

  async execute(
    provider: any,
    executionDto: ProviderExecutionDto,
    organizationId: string,
    userId: string,
  ): Promise<ProviderExecutionResult> {
    const startTime = Date.now();

    try {
      // Get or create session
      let sessionId = executionDto.sessionId;
      if (!sessionId) {
        const session = await this.sessionsService.create(userId, organizationId, {
          type: 'provider' as any,
          targetId: provider.id,
          metadata: {
            providerId: provider.id,
            modelId: executionDto.modelId,
            providerType: provider.type,
          },
        });
        sessionId = session.id;
      }

      // Find the model in provider
      const models = provider.models as any[];
      const model = models.find(m => m.id === executionDto.modelId);
      if (!model) {
        throw new BadRequestException(`Model ${executionDto.modelId} not found in provider`);
      }

      // Execute based on provider type
      let result: ProviderExecutionResult;
      switch (provider.type) {
        case ProviderType.OPENAI:
          result = await this.executeOpenAI(provider, model, executionDto);
          break;
        case ProviderType.CLAUDE:
          result = await this.executeClaude(provider, model, executionDto);
          break;
        case ProviderType.GEMINI:
          result = await this.executeGemini(provider, model, executionDto);
          break;
        case ProviderType.GROQ:
          result = await this.executeGroq(provider, model, executionDto);
          break;
        case ProviderType.CUSTOM:
          result = await this.executeCustom(provider, model, executionDto);
          break;
        default:
          throw new BadRequestException(`Unsupported provider type: ${provider.type}`);
      }

      result.executionTime = Date.now() - startTime;
      result.sessionId = sessionId;
      result.providerId = provider.id;

      // Add messages to session
      await this.sessionsService.addMessage(sessionId, organizationId, userId, {
        role: 'user',
        content: executionDto.prompt,
        metadata: { 
          type: 'provider_input',
          providerId: provider.id,
          modelId: executionDto.modelId,
        },
      });

      await this.sessionsService.addMessage(sessionId, organizationId, userId, {
        role: 'assistant',
        content: result.response,
        metadata: {
          type: 'provider_output',
          providerId: provider.id,
          modelId: executionDto.modelId,
          usage: result.usage,
          cost: result.cost,
          executionTime: result.executionTime,
        },
      });

      // Track usage for billing
      await this.trackUsage(organizationId, provider.id, result);

      // Publish execution event
      await this.apixService.publishProviderEvent(
        'provider_execution_completed',
        provider.id,
        organizationId,
        {
          sessionId,
          modelId: executionDto.modelId,
          success: result.success,
          executionTime: result.executionTime,
          cost: result.cost,
          usage: result.usage,
        }
      );

      return result;
    } catch (error) {
      const executionTime = Date.now() - startTime;

      await this.apixService.publishProviderEvent(
        'provider_execution_failed',
        provider.id,
        organizationId,
        {
          error: error.message,
          modelId: executionDto.modelId,
          executionTime,
        }
      );

      throw error;
    }
  }

  async testModel(provider: any, model: any, testPrompt: string): Promise<boolean> {
    try {
      const testExecution: ProviderExecutionDto = {
        providerId: provider.id,
        modelId: model.id,
        prompt: testPrompt,
        parameters: {
          maxTokens: 50,
          temperature: 0.7,
        },
      };

      const result = await this.execute(provider, testExecution, provider.organizationId, 'system');
      return result.success;
    } catch (error) {
      this.logger.warn(`Model test failed for ${provider.name}:${model.id}`, error.message);
      return false;
    }
  }

  private async executeOpenAI(
    provider: any,
    model: any,
    executionDto: ProviderExecutionDto
  ): Promise<ProviderExecutionResult> {
    const client = await this.getOpenAIClient(provider);
    const config = provider.config as any;

    const requestParams = {
      model: model.id,
      messages: [{ role: 'user', content: executionDto.prompt }],
      max_tokens: executionDto.parameters?.maxTokens || model.parameters?.maxTokens || 2048,
      temperature: executionDto.parameters?.temperature || model.parameters?.temperature || 0.7,
      top_p: executionDto.parameters?.topP || model.parameters?.topP,
      frequency_penalty: executionDto.parameters?.frequencyPenalty || model.parameters?.frequencyPenalty,
      presence_penalty: executionDto.parameters?.presencePenalty || model.parameters?.presencePenalty,
      stop: executionDto.parameters?.stopSequences,
      stream: executionDto.stream || false,
    };

    let retryCount = 0;
    const maxRetries = executionDto.overrides?.maxRetries || config.maxRetries || 3;

    while (retryCount <= maxRetries) {
      try {
        const response = await client.chat.completions.create(requestParams);

        const usage = response.usage || { prompt_tokens: 0, completion_tokens: 0, total_tokens: 0 };
        const cost = this.calculateCost(model, usage.prompt_tokens, usage.completion_tokens);

        return {
          success: true,
          response: response.choices[0]?.message?.content || '',
          usage: {
            promptTokens: usage.prompt_tokens,
            completionTokens: usage.completion_tokens,
            totalTokens: usage.total_tokens,
          },
          cost,
          executionTime: 0, // Will be set by caller
          modelId: model.id,
          providerId: provider.id,
          metadata: {
            finishReason: response.choices[0]?.finish_reason,
            model: response.model,
            retryCount,
          },
        };
      } catch (error) {
        retryCount++;
        if (retryCount > maxRetries) {
          throw error;
        }
        
        // Exponential backoff
        await new Promise(resolve => setTimeout(resolve, Math.pow(2, retryCount) * 1000));
      }
    }
  }

  private async executeClaude(
    provider: any,
    model: any,
    executionDto: ProviderExecutionDto
  ): Promise<ProviderExecutionResult> {
    const client = await this.getClaudeClient(provider);
    const config = provider.config as any;

    const requestParams = {
      model: model.id,
      max_tokens: executionDto.parameters?.maxTokens || model.parameters?.maxTokens || 2048,
      temperature: executionDto.parameters?.temperature || model.parameters?.temperature || 0.7,
      top_p: executionDto.parameters?.topP || model.parameters?.topP,
      stop_sequences: executionDto.parameters?.stopSequences,
      messages: [{ role: 'user', content: executionDto.prompt }],
      stream: executionDto.stream || false,
    };

    let retryCount = 0;
    const maxRetries = executionDto.overrides?.maxRetries || config.maxRetries || 3;

    while (retryCount <= maxRetries) {
      try {
        const response = await client.messages.create(requestParams);

        const usage = response.usage || { input_tokens: 0, output_tokens: 0 };
        const cost = this.calculateCost(model, usage.input_tokens, usage.output_tokens);

        return {
          success: true,
          response: response.content[0]?.text || '',
          usage: {
            promptTokens: usage.input_tokens,
            completionTokens: usage.output_tokens,
            totalTokens: usage.input_tokens + usage.output_tokens,
          },
          cost,
          executionTime: 0,
          modelId: model.id,
          providerId: provider.id,
          metadata: {
            finishReason: response.stop_reason,
            model: response.model,
            retryCount,
          },
        };
      } catch (error) {
        retryCount++;
        if (retryCount > maxRetries) {
          throw error;
        }
        
        await new Promise(resolve => setTimeout(resolve, Math.pow(2, retryCount) * 1000));
      }
    }
  }

  private async executeGemini(
    provider: any,
    model: any,
    executionDto: ProviderExecutionDto
  ): Promise<ProviderExecutionResult> {
    const client = await this.getGeminiClient(provider);
    const config = provider.config as any;

    let retryCount = 0;
    const maxRetries = executionDto.overrides?.maxRetries || config.maxRetries || 3;

    while (retryCount <= maxRetries) {
      try {
        const geminiModel = client.getGenerativeModel({ model: model.id });
        
        const generationConfig = {
          maxOutputTokens: executionDto.parameters?.maxTokens || model.parameters?.maxTokens || 2048,
          temperature: executionDto.parameters?.temperature || model.parameters?.temperature || 0.7,
          topP: executionDto.parameters?.topP || model.parameters?.topP,
          stopSequences: executionDto.parameters?.stopSequences,
        };

        const result = await geminiModel.generateContent({
          contents: [{ role: 'user', parts: [{ text: executionDto.prompt }] }],
          generationConfig,
        });

        const response = result.response;
        const text = response.text();

        // Estimate token usage (Gemini doesn't always provide exact counts)
        const promptTokens = Math.ceil(executionDto.prompt.length / 4);
        const completionTokens = Math.ceil(text.length / 4);
        const cost = this.calculateCost(model, promptTokens, completionTokens);

        return {
          success: true,
          response: text,
          usage: {
            promptTokens,
            completionTokens,
            totalTokens: promptTokens + completionTokens,
          },
          cost,
          executionTime: 0,
          modelId: model.id,
          providerId: provider.id,
          metadata: {
            finishReason: response.candidates?.[0]?.finishReason,
            model: model.id,
            retryCount,
          },
        };
      } catch (error) {
        retryCount++;
        if (retryCount > maxRetries) {
          throw error;
        }
        
        await new Promise(resolve => setTimeout(resolve, Math.pow(2, retryCount) * 1000));
      }
    }
  }

  private async executeGroq(
    provider: any,
    model: any,
    executionDto: ProviderExecutionDto
  ): Promise<ProviderExecutionResult> {
    const client = await this.getGroqClient(provider);
    const config = provider.config as any;

    const requestParams = {
      model: model.id,
      messages: [{ role: 'user', content: executionDto.prompt }],
      max_tokens: executionDto.parameters?.maxTokens || model.parameters?.maxTokens || 2048,
      temperature: executionDto.parameters?.temperature || model.parameters?.temperature || 0.7,
      top_p: executionDto.parameters?.topP || model.parameters?.topP,
      frequency_penalty: executionDto.parameters?.frequencyPenalty || model.parameters?.frequencyPenalty,
      presence_penalty: executionDto.parameters?.presencePenalty || model.parameters?.presencePenalty,
      stop: executionDto.parameters?.stopSequences,
      stream: executionDto.stream || false,
    };

    let retryCount = 0;
    const maxRetries = executionDto.overrides?.maxRetries || config.maxRetries || 3;

    while (retryCount <= maxRetries) {
      try {
        const response = await client.chat.completions.create(requestParams);

        const usage = response.usage || { prompt_tokens: 0, completion_tokens: 0, total_tokens: 0 };
        const cost = this.calculateCost(model, usage.prompt_tokens, usage.completion_tokens);

        return {
          success: true,
          response: response.choices[0]?.message?.content || '',
          usage: {
            promptTokens: usage.prompt_tokens,
            completionTokens: usage.completion_tokens,
            totalTokens: usage.total_tokens,
          },
          cost,
          executionTime: 0,
          modelId: model.id,
          providerId: provider.id,
          metadata: {
            finishReason: response.choices[0]?.finish_reason,
            model: response.model,
            retryCount,
          },
        };
      } catch (error) {
        retryCount++;
        if (retryCount > maxRetries) {
          throw error;
        }
        
        await new Promise(resolve => setTimeout(resolve, Math.pow(2, retryCount) * 1000));
      }
    }
  }

  private async executeCustom(
    provider: any,
    model: any,
    executionDto: ProviderExecutionDto
  ): Promise<ProviderExecutionResult> {
    const config = provider.config as any;

    const requestConfig: AxiosRequestConfig = {
      method: 'POST',
      url: `${config.baseUrl}/v1/chat/completions`,
      headers: {
        'Content-Type': 'application/json',
        ...config.headers,
      },
      timeout: executionDto.overrides?.timeout || config.timeout || 30000,
      data: {
        model: model.id,
        messages: [{ role: 'user', content: executionDto.prompt }],
        max_tokens: executionDto.parameters?.maxTokens || model.parameters?.maxTokens || 2048,
        temperature: executionDto.parameters?.temperature || model.parameters?.temperature || 0.7,
        top_p: executionDto.parameters?.topP || model.parameters?.topP,
        frequency_penalty: executionDto.parameters?.frequencyPenalty || model.parameters?.frequencyPenalty,
        presence_penalty: executionDto.parameters?.presencePenalty || model.parameters?.presencePenalty,
        stop: executionDto.parameters?.stopSequences,
        stream: executionDto.stream || false,
      },
    };

    // Add authentication
    if (config.apiKey) {
      requestConfig.headers['Authorization'] = `Bearer ${config.apiKey}`;
    }

    let retryCount = 0;
    const maxRetries = executionDto.overrides?.maxRetries || config.maxRetries || 3;

    while (retryCount <= maxRetries) {
      try {
        const response = await axios(requestConfig);
        const data = response.data;

        const usage = data.usage || { prompt_tokens: 0, completion_tokens: 0, total_tokens: 0 };
        const cost = this.calculateCost(model, usage.prompt_tokens, usage.completion_tokens);

        return {
          success: true,
          response: data.choices[0]?.message?.content || '',
          usage: {
            promptTokens: usage.prompt_tokens,
            completionTokens: usage.completion_tokens,
            totalTokens: usage.total_tokens,
          },
          cost,
          executionTime: 0,
          modelId: model.id,
          providerId: provider.id,
          metadata: {
            finishReason: data.choices[0]?.finish_reason,
            model: data.model,
            retryCount,
          },
        };
      } catch (error) {
        retryCount++;
        if (retryCount > maxRetries) {
          throw error;
        }
        
        await new Promise(resolve => setTimeout(resolve, Math.pow(2, retryCount) * 1000));
      }
    }
  }

  // Client management methods

  private async getOpenAIClient(provider: any): Promise<OpenAI> {
    let client = this.clients.get(provider.id)?.openai;
    
    if (!client) {
      const config = provider.config as any;
      client = new OpenAI({
        apiKey: config.apiKey,
        baseURL: config.baseUrl,
        timeout: config.timeout || 30000,
        maxRetries: 0, // We handle retries ourselves
      });

      this.setClient(provider.id, 'openai', client);
    }

    return client;
  }

  private async getClaudeClient(provider: any): Promise<Anthropic> {
    let client = this.clients.get(provider.id)?.claude;
    
    if (!client) {
      const config = provider.config as any;
      client = new Anthropic({
        apiKey: config.apiKey,
        baseURL: config.baseUrl,
        timeout: config.timeout || 30000,
        maxRetries: 0,
      });

      this.setClient(provider.id, 'claude', client);
    }

    return client;
  }

  private async getGeminiClient(provider: any): Promise<GoogleGenerativeAI> {
    let client = this.clients.get(provider.id)?.gemini;
    
    if (!client) {
      const config = provider.config as any;
      client = new GoogleGenerativeAI(config.apiKey);

      this.setClient(provider.id, 'gemini', client);
    }

    return client;
  }

  private async getGroqClient(provider: any): Promise<Groq> {
    let client = this.clients.get(provider.id)?.groq;
    
    if (!client) {
      const config = provider.config as any;
      client = new Groq({
        apiKey: config.apiKey,
        baseURL: config.baseUrl,
        timeout: config.timeout || 30000,
        maxRetries: 0,
      });

      this.setClient(provider.id, 'groq', client);
    }

    return client;
  }

  private setClient(providerId: string, type: keyof ProviderClient, client: any) {
    const existing = this.clients.get(providerId) || {};
    existing[type] = client;
    this.clients.set(providerId, existing);
  }

  private calculateCost(model: any, promptTokens: number, completionTokens: number): number {
    const inputCost = (promptTokens / 1000) * model.inputCostPer1KTokens;
    const outputCost = (completionTokens / 1000) * model.outputCostPer1KTokens;
    return Math.round((inputCost + outputCost) * 100000) / 100000; // Round to 5 decimal places
  }

  private async trackUsage(organizationId: string, providerId: string, result: ProviderExecutionResult) {
    await this.prisma.billingUsage.create({
      data: {
        organizationId,
        type: 'provider_execution',
        quantity: 1,
        cost: result.cost,
        metadata: {
          providerId,
          modelId: result.modelId,
          promptTokens: result.usage.promptTokens,
          completionTokens: result.usage.completionTokens,
          totalTokens: result.usage.totalTokens,
          executionTime: result.executionTime,
          success: result.success,
        },
      },
    });

    // Update quota usage
    await this.prisma.quota.upsert({
      where: {
        organizationId_type: {
          organizationId,
          type: 'ai_executions',
        },
      },
      create: {
        organizationId,
        type: 'ai_executions',
        limit: 10000,
        used: 1,
        resetAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
      },
      update: {
        used: {
          increment: 1,
        },
      },
    });

    // Update token quota
    await this.prisma.quota.upsert({
      where: {
        organizationId_type: {
          organizationId,
          type: 'ai_tokens',
        },
      },
      create: {
        organizationId,
        type: 'ai_tokens',
        limit: 1000000,
        used: result.usage.totalTokens,
        resetAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
      },
      update: {
        used: {
          increment: result.usage.totalTokens,
        },
      },
    });
  }

  // Cleanup method for removing stale clients
  clearProviderClient(providerId: string) {
    this.clients.delete(providerId);
  }

  // Health check method
  async validateProviderConnection(provider: any): Promise<boolean> {
    try {
      const models = provider.models as any[];
      if (models.length === 0) return false;

      const testModel = models[0];
      return await this.testModel(provider, testModel, 'Connection test');
    } catch (error) {
      this.logger.error(`Provider connection validation failed for ${provider.id}`, error);
      return false;
    }
  }
}