import { IsString, IsOptional, IsEnum, IsObject, IsNumber, Min, Max, IsArray, IsBoolean, IsUrl } from 'class-validator';
import { Type } from 'class-transformer';

export enum ProviderType {
  OPENAI = 'openai',
  CLAUDE = 'claude',
  GEMINI = 'gemini',
  MISTRAL = 'mistral',
  GROQ = 'groq',
  CUSTOM = 'custom',
}

export enum ProviderStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  MAINTENANCE = 'maintenance',
  ERROR = 'error',
}

export enum ProviderCapability {
  TEXT_GENERATION = 'text_generation',
  CODE_GENERATION = 'code_generation',
  FUNCTION_CALLING = 'function_calling',
  VISION = 'vision',
  AUDIO = 'audio',
  EMBEDDINGS = 'embeddings',
  FINE_TUNING = 'fine_tuning',
}

export enum ModelSize {
  SMALL = 'small',
  MEDIUM = 'medium',
  LARGE = 'large',
  XLARGE = 'xlarge',
}

export class ProviderConfigDto {
  @IsOptional()
  @IsString()
  apiKey?: string;

  @IsOptional()
  @IsUrl()
  baseUrl?: string;

  @IsOptional()
  @IsObject()
  headers?: Record<string, string>;

  @IsOptional()
  @IsNumber()
  @Min(1000)
  @Max(600000)
  timeout?: number = 30000;

  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(10)
  maxRetries?: number = 3;

  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(1000)
  maxConcurrentRequests?: number = 10;

  @IsOptional()
  @IsNumber()
  @Min(0)
  maxRequestsPerMinute?: number = 60;

  @IsOptional()
  @IsObject()
  rateLimits?: {
    requestsPerMinute: number;
    tokensPerMinute: number;
    requestsPerDay: number;
    tokensPerDay: number;
  };

  @IsOptional()
  @IsObject()
  customConfig?: Record<string, any>;
}

export class ProviderModelDto {
  @IsString()
  id: string;

  @IsString()
  name: string;

  @IsOptional()
  @IsString()
  description?: string;

  @IsEnum(ModelSize)
  size: ModelSize;

  @IsArray()
  @IsEnum(ProviderCapability, { each: true })
  capabilities: ProviderCapability[];

  @IsNumber()
  @Min(0)
  inputCostPer1KTokens: number;

  @IsNumber()
  @Min(0)
  outputCostPer1KTokens: number;

  @IsOptional()
  @IsNumber()
  @Min(1)
  maxTokens?: number;

  @IsOptional()
  @IsNumber()
  @Min(1)
  contextWindow?: number;

  @IsOptional()
  @IsObject()
  parameters?: {
    temperature?: number;
    maxTokens?: number;
    topP?: number;
    frequencyPenalty?: number;
    presencePenalty?: number;
  };

  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;
}

export class CreateProviderDto {
  @IsString()
  name: string;

  @IsString()
  description: string;

  @IsEnum(ProviderType)
  type: ProviderType;

  @IsObject()
  @Type(() => ProviderConfigDto)
  config: ProviderConfigDto;

  @IsArray()
  @Type(() => ProviderModelDto)
  models: ProviderModelDto[];

  @IsOptional()
  @IsEnum(ProviderStatus)
  status?: ProviderStatus = ProviderStatus.ACTIVE;

  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(100)
  priority?: number = 50;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];

  @IsOptional()
  @IsObject()
  metadata?: {
    region?: string;
    compliance?: string[];
    supportedLanguages?: string[];
    version?: string;
  };
}

export class UpdateProviderDto {
  @IsOptional()
  @IsString()
  name?: string;

  @IsOptional()
  @IsString()
  description?: string;

  @IsOptional()
  @IsObject()
  @Type(() => ProviderConfigDto)
  config?: Partial<ProviderConfigDto>;

  @IsOptional()
  @IsArray()
  @Type(() => ProviderModelDto)
  models?: ProviderModelDto[];

  @IsOptional()
  @IsEnum(ProviderStatus)
  status?: ProviderStatus;

  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(100)
  priority?: number;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];

  @IsOptional()
  @IsObject()
  metadata?: any;
}

export class ProviderQueryDto {
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  page?: number = 1;

  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  @Max(100)
  limit?: number = 20;

  @IsOptional()
  @IsEnum(ProviderType)
  type?: ProviderType;

  @IsOptional()
  @IsEnum(ProviderStatus)
  status?: ProviderStatus;

  @IsOptional()
  @IsString()
  search?: string;

  @IsOptional()
  @IsArray()
  @IsEnum(ProviderCapability, { each: true })
  capabilities?: ProviderCapability[];

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];

  @IsOptional()
  @IsString()
  sortBy?: string = 'priority';

  @IsOptional()
  @IsEnum(['asc', 'desc'])
  sortOrder?: 'asc' | 'desc' = 'desc';
}

export class ProviderSelectionDto {
  @IsOptional()
  @IsArray()
  @IsEnum(ProviderCapability, { each: true })
  requiredCapabilities?: ProviderCapability[];

  @IsOptional()
  @IsEnum(ModelSize)
  preferredModelSize?: ModelSize;

  @IsOptional()
  @IsNumber()
  @Min(0)
  maxCostPer1KTokens?: number;

  @IsOptional()
  @IsNumber()
  @Min(1)
  minTokens?: number;

  @IsOptional()
  @IsString()
  region?: string;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  complianceRequirements?: string[];

  @IsOptional()
  @IsBoolean()
  preferLowLatency?: boolean = false;

  @IsOptional()
  @IsBoolean()
  preferLowCost?: boolean = false;

  @IsOptional()
  @IsObject()
  organizationPreferences?: {
    preferredProviders?: string[];
    excludedProviders?: string[];
    maxCostThreshold?: number;
  };
}

export class ProviderExecutionDto {
  @IsString()
  providerId: string;

  @IsString()
  modelId: string;

  @IsString()
  prompt: string;

  @IsOptional()
  @IsObject()
  parameters?: {
    temperature?: number;
    maxTokens?: number;
    topP?: number;
    frequencyPenalty?: number;
    presencePenalty?: number;
    stopSequences?: string[];
  };

  @IsOptional()
  @IsString()
  sessionId?: string;

  @IsOptional()
  @IsObject()
  context?: Record<string, any>;

  @IsOptional()
  @IsBoolean()
  stream?: boolean = false;

  @IsOptional()
  @IsObject()
  overrides?: {
    timeout?: number;
    maxRetries?: number;
  };
}

export class ProviderHealthCheckDto {
  @IsOptional()
  @IsBoolean()
  checkAllModels?: boolean = true;

  @IsOptional()
  @IsBoolean()
  checkRateLimits?: boolean = true;

  @IsOptional()
  @IsBoolean()
  checkLatency?: boolean = true;

  @IsOptional()
  @IsString()
  testPrompt?: string = 'Hello, how are you?';
}

export class ProviderMetricsDto {
  @IsOptional()
  @IsString()
  timeRange?: string = '24h';

  @IsOptional()
  @IsString()
  modelId?: string;

  @IsOptional()
  @IsBoolean()
  includeErrors?: boolean = true;

  @IsOptional()
  @IsBoolean()
  includeCosts?: boolean = true;
}