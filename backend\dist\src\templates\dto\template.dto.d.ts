export declare enum TemplateCategory {
    CUSTOMER_SERVICE = "customer-service",
    CONTENT_CREATION = "content-creation",
    DATA_ANALYSIS = "data-analysis",
    AUTOMATION = "automation",
    EDUCATION = "education",
    HEALTHCARE = "healthcare",
    FINANCE = "finance",
    MARKETING = "marketing",
    SALES = "sales",
    RESEARCH = "research",
    GENERAL = "general"
}
export declare class TemplateConfigDto {
    prompt: string;
    systemPrompt?: string;
    config?: {
        temperature?: number;
        maxTokens?: number;
        model?: string;
        memory?: {
            enabled: boolean;
            maxMessages: number;
            strategy: 'rolling' | 'summarize' | 'vector';
        };
    };
    tools?: string[];
    parameters?: Array<{
        name: string;
        type: 'string' | 'number' | 'boolean' | 'array' | 'object';
        description: string;
        required: boolean;
        defaultValue?: any;
    }>;
}
export declare class CreateTemplateDto {
    name: string;
    description: string;
    category: TemplateCategory;
    template: TemplateConfigDto;
    isPublic?: boolean;
    tags?: string[];
    metadata?: {
        version?: string;
        author?: string;
        license?: string;
        website?: string;
        examples?: Array<{
            input: string;
            output: string;
            description: string;
        }>;
    };
}
export declare class UpdateTemplateDto {
    name?: string;
    description?: string;
    category?: TemplateCategory;
    template?: Partial<TemplateConfigDto>;
    isPublic?: boolean;
    tags?: string[];
    metadata?: any;
}
export declare class TemplateQueryDto {
    page?: number;
    limit?: number;
    category?: TemplateCategory;
    search?: string;
    isPublic?: boolean;
    tags?: string[];
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
}
export declare class RateTemplateDto {
    rating: number;
    review?: string;
}
export declare class ForkTemplateDto {
    name: string;
    description?: string;
    isPublic?: boolean;
}
