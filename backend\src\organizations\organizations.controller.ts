import { Controller, Get, Patch, Body, Query, UseGuards } from '@nestjs/common';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { OrganizationsService } from './organizations.service';
import { OrganizationGuard } from '../common/guards/organization.guard';
import { CurrentOrganization } from '../common/decorators/current-user.decorator';

@Controller('organizations')
@UseGuards(JwtAuthGuard, OrganizationGuard)
export class OrganizationsController {
  constructor(private readonly organizationsService: OrganizationsService) {}

  @Get('current')
  getCurrent(@CurrentOrganization('id') organizationId: string) {
    return this.organizationsService.findCurrent(organizationId);
  }

  @Patch('current/settings')
  updateSettings(
    @CurrentOrganization('id') organizationId: string,
    @Body() settings: any,
  ) {
    return this.organizationsService.updateSettings(organizationId, settings);
  }

  @Get('current/quotas')
  getQuotas(@CurrentOrganization('id') organizationId: string) {
    return this.organizationsService.getQuotas(organizationId);
  }

  @Patch('current/quotas/:type')
  updateQuota(
    @CurrentOrganization('id') organizationId: string,
    @Body() body: { limit: number },
  ) {
    return this.organizationsService.updateQuota(organizationId, body.limit.toString(), body.limit);
  }

  @Get('current/usage')
  getUsage(
    @CurrentOrganization('id') organizationId: string,
    @Query('type') type?: string,
    @Query('from') from?: string,
    @Query('to') to?: string,
  ) {
    const fromDate = from ? new Date(from) : undefined;
    const toDate = to ? new Date(to) : undefined;
    
    return this.organizationsService.getUsage(organizationId, type, fromDate, toDate);
  }
}