import { Module } from '@nestjs/common';
import { Templates<PERSON>ontroller } from './templates.controller';
import { TemplatesService } from './templates.service';
import { AuthModule } from '../auth/auth.module';
import { APIMXModule } from '../apix/apix.module';

@Module({
  imports: [AuthModule, APIMXModule],
  controllers: [TemplatesController],
  providers: [TemplatesService],
  exports: [TemplatesService],
})
export class TemplatesModule {}