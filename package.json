{"name": "synapseai-platform", "version": "1.0.0", "description": "Production-ready SaaS platform for AI agents, tools, and workflows", "private": true, "workspaces": ["backend", "frontend", "shared"], "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:backend": "cd backend && npm run dev", "dev:frontend": "cd frontend && npm run dev", "build": "npm run build:shared && npm run build:backend && npm run build:frontend", "build:shared": "cd shared && npm run build", "build:backend": "cd backend && npm run build", "build:frontend": "cd frontend && npm run build", "start": "concurrently \"npm run start:backend\" \"npm run start:frontend\"", "start:backend": "cd backend && npm run start", "start:frontend": "cd frontend && npm run start", "test": "npm run test:shared && npm run test:backend && npm run test:frontend", "test:shared": "cd shared && npm run test", "test:backend": "cd backend && npm run test", "test:frontend": "cd frontend && npm run test", "test:e2e": "cd frontend && npm run test:e2e", "lint": "npm run lint:shared && npm run lint:backend && npm run lint:frontend", "lint:shared": "cd shared && npm run lint", "lint:backend": "cd backend && npm run lint", "lint:frontend": "cd frontend && npm run lint", "type-check": "npm run type-check:shared && npm run type-check:backend && npm run type-check:frontend", "type-check:shared": "cd shared && npm run type-check", "type-check:backend": "cd backend && npm run type-check", "type-check:frontend": "cd frontend && npm run type-check", "db:migrate": "cd backend && npm run db:migrate", "db:seed": "cd backend && npm run db:seed", "db:studio": "cd backend && npm run db:studio", "docker:dev": "docker-compose -f docker-compose.dev.yml up", "docker:prod": "docker-compose -f docker-compose.prod.yml up"}, "devDependencies": {"concurrently": "^8.2.2", "@types/node": "^20.10.4", "typescript": "^5.3.3"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}}