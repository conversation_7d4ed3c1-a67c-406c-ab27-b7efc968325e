import { PrismaService } from '../prisma/prisma.service';
export declare class OrganizationsService {
    private prisma;
    constructor(prisma: PrismaService);
    findCurrent(organizationId: string): Promise<any>;
    updateSettings(organizationId: string, settings: any): Promise<any>;
    getQuotas(organizationId: string): Promise<any>;
    updateQuota(organizationId: string, type: string, limit: number): Promise<any>;
    getUsage(organizationId: string, type?: string, from?: Date, to?: Date): Promise<any>;
}
