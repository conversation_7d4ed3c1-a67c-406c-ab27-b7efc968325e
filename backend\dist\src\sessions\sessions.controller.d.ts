import { SessionsService } from './sessions.service';
import { CreateSessionDto, UpdateSessionDto, SessionQueryDto, AddMessageDto, UpdateContextDto } from './dto/session.dto';
export declare class SessionsController {
    private readonly sessionsService;
    constructor(sessionsService: SessionsService);
    create(userId: string, organizationId: string, createSessionDto: CreateSessionDto): Promise<any>;
    findAll(organizationId: string, query: SessionQueryDto): Promise<{
        sessions: any;
        pagination: {
            page: number;
            limit: number;
            total: any;
            pages: number;
        };
    }>;
    getStats(organizationId: string, userId: string): Promise<{
        total: any;
        active: any;
        byType: any;
        byStatus: any;
    }>;
    findMy(userId: string, organizationId: string, query: SessionQueryDto): Promise<{
        sessions: any;
        pagination: {
            page: number;
            limit: number;
            total: any;
            pages: number;
        };
    }>;
    findOne(id: string, organizationId: string, userId: string): Promise<any>;
    update(id: string, organizationId: string, userId: string, updateSessionDto: UpdateSessionDto): Promise<any>;
    remove(id: string, organizationId: string, userId: string): Promise<{
        message: string;
    }>;
    addMessage(sessionId: string, organizationId: string, userId: string, addMessageDto: AddMessageDto): Promise<any>;
    updateContext(sessionId: string, organizationId: string, userId: string, updateContextDto: UpdateContextDto): Promise<any>;
    clearMemory(sessionId: string, organizationId: string, userId: string): Promise<any>;
}
