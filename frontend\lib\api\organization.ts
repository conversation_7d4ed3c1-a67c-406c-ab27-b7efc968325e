import { apiClient } from './client';

export interface Organization {
  id: string;
  name: string;
  slug: string;
  settings: {
    branding?: {
      logo?: string;
      primaryColor?: string;
      theme?: 'light' | 'dark';
    };
    billing?: {
      planId: string;
      quotas: Record<string, number>;
    };
    features?: string[];
  };
  createdAt: string;
  updatedAt: string;
}

export interface Quota {
  id: string;
  organizationId: string;
  type: string;
  limit: number;
  used: number;
  resetAt: string;
  createdAt: string;
  updatedAt: string;
}

export interface UpdateSettingsRequest {
  branding?: {
    logo?: string;
    primaryColor?: string;
    theme?: 'light' | 'dark';
  };
  features?: string[];
}

export const organizationApi = {
  async getCurrent(): Promise<Organization> {
    return apiClient.get('/api/v1/organizations/current');
  },

  async updateSettings(settings: UpdateSettingsRequest): Promise<Organization> {
    return apiClient.patch('/api/v1/organizations/current/settings', settings);
  },

  async getQuotas(): Promise<Quota[]> {
    return apiClient.get('/api/v1/organizations/current/quotas');
  },

  async updateQuota(type: string, limit: number): Promise<Quota> {
    return apiClient.patch(`/api/v1/organizations/current/quotas/${type}`, { limit });
  },

  async getUsage(type?: string, from?: string, to?: string): Promise<any[]> {
    const params = new URLSearchParams();
    if (type) params.append('type', type);
    if (from) params.append('from', from);
    if (to) params.append('to', to);
    
    return apiClient.get(`/api/v1/organizations/current/usage?${params.toString()}`);
  },
};