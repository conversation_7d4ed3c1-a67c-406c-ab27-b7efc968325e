import { Injectable, Logger } from '@nestjs/common';
import { VM } from 'vm2';

export interface ExecutionContext {
  input: any;
  config?: any;
  metadata?: any;
}

export interface ExecutionResult {
  success: boolean;
  output: any;
  executionTime: number;
  error?: string;
  logs?: string[];
}

@Injectable()
export class SafeExecutorService {
  private readonly logger = new Logger(SafeExecutorService.name);
  
  private readonly MAX_EXECUTION_TIME = 30000; // 30 seconds
  private readonly MAX_MEMORY = 128 * 1024 * 1024; // 128MB

  /**
   * Safely execute function code in a sandboxed environment
   */
  async executeFunctionCode(
    functionCode: string,
    context: ExecutionContext,
  ): Promise<ExecutionResult> {
    const startTime = Date.now();
    const logs: string[] = [];

    try {
      // Create a sandboxed VM with limited capabilities
      const vm = new VM({
        timeout: this.MAX_EXECUTION_TIME,
        allowAsync: true,
        sandbox: {
          // Provide safe globals
          console: {
            log: (...args: any[]) => logs.push(args.join(' ')),
            error: (...args: any[]) => logs.push(`ERROR: ${args.join(' ')}`),
            warn: (...args: any[]) => logs.push(`WARN: ${args.join(' ')}`),
          },
          JSON,
          Date,
          Math,
          String,
          Number,
          Boolean,
          Array,
          Object,
          // Utility functions
          setTimeout: undefined, // Disable setTimeout
          setInterval: undefined, // Disable setInterval
          require: undefined, // Disable require
          process: undefined, // Disable process access
          global: undefined, // Disable global access
          Buffer: undefined, // Disable Buffer
        },
      });

      // Wrap the function code in a safe execution wrapper
      const wrappedCode = `
        (function(context) {
          const { input } = context;
          try {
            ${functionCode}
          } catch (error) {
            throw new Error('Function execution failed: ' + error.message);
          }
        })
      `;

      // Execute the code
      const func = vm.run(wrappedCode);
      const result = await func(context);

      const executionTime = Date.now() - startTime;

      this.logger.debug(`Function executed successfully in ${executionTime}ms`);

      return {
        success: true,
        output: result,
        executionTime,
        logs,
      };

    } catch (error) {
      const executionTime = Date.now() - startTime;
      
      this.logger.error(`Function execution failed: ${error.message}`);

      return {
        success: false,
        output: null,
        executionTime,
        error: error.message,
        logs,
      };
    }
  }

  /**
   * Safely evaluate conditional expressions
   */
  evaluateCondition(condition: string, input: any): boolean {
    try {
      // Create a very limited VM for condition evaluation
      const vm = new VM({
        timeout: 5000, // 5 seconds max for conditions
        sandbox: {
          input,
          // Basic comparison operators and functions
          Math,
          String,
          Number,
          Boolean,
          Array,
          Object,
          Date,
        },
      });

      // Wrap the condition in a safe function
      const wrappedCondition = `
        (function() {
          return Boolean(${condition});
        })()
      `;

      return vm.run(wrappedCondition);

    } catch (error) {
      this.logger.warn(`Condition evaluation failed: ${error.message}`);
      return false;
    }
  }

  /**
   * Validate function code before execution
   */
  validateFunctionCode(functionCode: string): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Check for dangerous patterns
    const dangerousPatterns = [
      /eval\s*\(/,
      /Function\s*\(/,
      /require\s*\(/,
      /import\s+/,
      /process\./,
      /global\./,
      /Buffer\./,
      /__dirname/,
      /__filename/,
      /fs\./,
      /child_process/,
      /net\./,
      /http\./,
      /https\./,
      /cluster\./,
      /worker_threads/,
    ];

    for (const pattern of dangerousPatterns) {
      if (pattern.test(functionCode)) {
        errors.push(`Dangerous pattern detected: ${pattern.source}`);
      }
    }

    // Check for basic syntax errors
    try {
      new Function(functionCode);
    } catch (error) {
      errors.push(`Syntax error: ${error.message}`);
    }

    return {
      valid: errors.length === 0,
      errors,
    };
  }

  /**
   * Get execution metrics and resource usage
   */
  getExecutionMetrics(): {
    activeExecutions: number;
    totalExecutions: number;
    averageExecutionTime: number;
  } {
    // Implementation would track metrics
    return {
      activeExecutions: 0,
      totalExecutions: 0,
      averageExecutionTime: 0,
    };
  }
}