import { <PERSON><PERSON>anager } from './base-manager';
import { 
  Workflow, 
  WorkflowExecution, 
  CreateWorkflowRequest, 
  UpdateWorkflowRequest, 
  ExecuteWorkflowRequest,
  WorkflowQueryOptions,
  WorkflowExecutionQueryOptions,
  CloneWorkflowRequest,
  PaginatedResponse,
  WorkflowStats
} from '../types';

export class WorkflowManager extends BaseManager {
  /**
   * Create a new workflow
   */
  async create(data: CreateWorkflowRequest): Promise<Workflow> {
    return this.client.request({
      method: 'POST',
      url: '/workflows',
      data,
    });
  }

  /**
   * List workflows with filtering and pagination
   */
  async list(options: WorkflowQueryOptions = {}): Promise<PaginatedResponse<Workflow>> {
    return this.client.request({
      method: 'GET',
      url: '/workflows',
      params: options,
    });
  }

  /**
   * Get a specific workflow by ID
   */
  async get(workflowId: string): Promise<Workflow> {
    return this.client.request({
      method: 'GET',
      url: `/workflows/${workflowId}`,
    });
  }

  /**
   * Update a workflow
   */
  async update(workflowId: string, data: UpdateWorkflowRequest): Promise<Workflow> {
    return this.client.request({
      method: 'PATCH',
      url: `/workflows/${workflowId}`,
      data,
    });
  }

  /**
   * Delete a workflow
   */
  async delete(workflowId: string): Promise<void> {
    return this.client.request({
      method: 'DELETE',
      url: `/workflows/${workflowId}`,
    });
  }

  /**
   * Clone a workflow
   */
  async clone(workflowId: string, data: CloneWorkflowRequest): Promise<Workflow> {
    return this.client.request({
      method: 'POST',
      url: `/workflows/${workflowId}/clone`,
      data,
    });
  }

  /**
   * Execute a workflow
   */
  async execute(workflowId: string, data: ExecuteWorkflowRequest = {}): Promise<WorkflowExecution> {
    return this.client.request({
      method: 'POST',
      url: `/workflows/${workflowId}/execute`,
      data,
    });
  }

  /**
   * Execute a workflow with streaming support
   */
  async executeStreaming(
    workflowId: string, 
    data: ExecuteWorkflowRequest = {},
    onStep?: (step: any) => void,
    onComplete?: (result: WorkflowExecution) => void,
    onError?: (error: Error) => void
  ): Promise<string> {
    // Start execution
    const execution = await this.execute(workflowId, data);
    
    // Subscribe to real-time updates
    const subscriptionId = this.client.subscribe({
      eventTypes: [
        'workflow_step_completed',
        'workflow_execution_completed',
        'workflow_execution_failed'
      ],
      organizationId: this.client.getConfig().organizationId,
      callback: (event) => {
        if (event.payload.executionId !== execution.id) return;

        switch (event.type) {
          case 'workflow_step_completed':
            onStep?.(event.payload);
            break;
          case 'workflow_execution_completed':
            onComplete?.(event.payload);
            this.client.unsubscribe(subscriptionId);
            break;
          case 'workflow_execution_failed':
            onError?.(new Error(event.payload.error));
            this.client.unsubscribe(subscriptionId);
            break;
        }
      },
    });

    return execution.id;
  }

  /**
   * Get workflow executions
   */
  async getExecutions(
    workflowId: string, 
    options: WorkflowExecutionQueryOptions = {}
  ): Promise<PaginatedResponse<WorkflowExecution>> {
    return this.client.request({
      method: 'GET',
      url: `/workflows/${workflowId}/executions`,
      params: options,
    });
  }

  /**
   * Get all workflow executions across the organization
   */
  async getAllExecutions(
    options: WorkflowExecutionQueryOptions = {}
  ): Promise<PaginatedResponse<WorkflowExecution>> {
    return this.client.request({
      method: 'GET',
      url: '/workflows/executions/all',
      params: options,
    });
  }

  /**
   * Get a specific workflow execution
   */
  async getExecution(executionId: string): Promise<WorkflowExecution> {
    return this.client.request({
      method: 'GET',
      url: `/workflows/executions/${executionId}`,
    });
  }

  /**
   * Cancel a running workflow execution
   */
  async cancelExecution(executionId: string): Promise<void> {
    return this.client.request({
      method: 'POST',
      url: `/workflows/executions/${executionId}/cancel`,
    });
  }

  /**
   * Get workflow statistics
   */
  async getStats(): Promise<WorkflowStats> {
    return this.client.request({
      method: 'GET',
      url: '/workflows/stats',
    });
  }

  /**
   * Validate a workflow definition
   */
  async validate(workflowData: CreateWorkflowRequest): Promise<{ valid: boolean; errors: string[] }> {
    try {
      await this.client.request({
        method: 'POST',
        url: '/workflows/validate',
        data: workflowData,
      });
      return { valid: true, errors: [] };
    } catch (error: any) {
      return {
        valid: false,
        errors: error.details?.errors || [error.message],
      };
    }
  }

  /**
   * Get workflow templates
   */
  async getTemplates(): Promise<Workflow[]> {
    return this.client.request({
      method: 'GET',
      url: '/workflows/templates',
    });
  }

  /**
   * Import a workflow from a file or URL
   */
  async import(source: string | File): Promise<Workflow> {
    if (typeof source === 'string') {
      // Import from URL or JSON string
      return this.client.request({
        method: 'POST',
        url: '/workflows/import',
        data: { source },
      });
    } else {
      // Import from file
      const formData = new FormData();
      formData.append('file', source);
      
      return this.client.request({
        method: 'POST',
        url: '/workflows/import',
        data: formData,
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
    }
  }

  /**
   * Export a workflow
   */
  async export(workflowId: string, format: 'json' | 'yaml' = 'json'): Promise<string> {
    const response = await this.client.request({
      method: 'GET',
      url: `/workflows/${workflowId}/export`,
      params: { format },
      responseType: 'text',
    });
    return response;
  }

  /**
   * Subscribe to workflow events
   */
  subscribeToWorkflow(
    workflowId: string,
    callback: (event: any) => void
  ): string {
    return this.client.subscribe({
      eventTypes: [
        'workflow_updated',
        'workflow_execution_started',
        'workflow_execution_completed',
        'workflow_execution_failed',
        'workflow_step_completed',
      ],
      organizationId: this.client.getConfig().organizationId,
      callback: (event) => {
        if (event.payload.workflowId === workflowId) {
          callback(event);
        }
      },
    });
  }

  /**
   * Subscribe to all workflow events in the organization
   */
  subscribeToAllWorkflows(callback: (event: any) => void): string {
    return this.client.subscribe({
      eventTypes: [
        'workflow_created',
        'workflow_updated',
        'workflow_deleted',
        'workflow_execution_started',
        'workflow_execution_completed',
        'workflow_execution_failed',
      ],
      organizationId: this.client.getConfig().organizationId,
      callback,
    });
  }

  /**
   * Get workflow performance metrics
   */
  async getMetrics(
    workflowId: string,
    timeRange: {
      startDate: Date;
      endDate: Date;
    }
  ): Promise<{
    totalExecutions: number;
    successRate: number;
    averageExecutionTime: number;
    executionTrend: Array<{ date: string; count: number; avgTime: number }>;
  }> {
    return this.client.request({
      method: 'GET',
      url: `/workflows/${workflowId}/metrics`,
      params: {
        startDate: timeRange.startDate.toISOString(),
        endDate: timeRange.endDate.toISOString(),
      },
    });
  }
}