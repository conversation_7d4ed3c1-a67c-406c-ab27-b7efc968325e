{"version": 3, "file": "agent-executor.service.js", "sourceRoot": "", "sources": ["../../../src/agents/agent-executor.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA,2CAAoF;AACpF,6DAAyD;AACzD,uDAAoD;AACpD,mEAA+D;AAE/D,mCAA4B;AAC5B,2CAA+C;AAwBxC,IAAM,oBAAoB,GAA1B,MAAM,oBAAoB;IAG/B,YACU,MAAqB,EACrB,WAAyB,EACzB,eAAgC,EAChC,aAA4B;QAH5B,WAAM,GAAN,MAAM,CAAe;QACrB,gBAAW,GAAX,WAAW,CAAc;QACzB,oBAAe,GAAf,eAAe,CAAiB;QAChC,kBAAa,GAAb,aAAa,CAAe;QAEpC,IAAI,CAAC,MAAM,GAAG,IAAI,gBAAM,CAAC;YACvB,MAAM,EAAE,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,gBAAgB,CAAC;SACjD,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,YAAY,CAChB,OAAe,EACf,cAAsB,EACtB,MAAc,EACd,UAA2B;QAE3B,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,IAAI,CAAC;YAEH,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC;gBAC9C,KAAK,EAAE;oBACL,EAAE,EAAE,OAAO;oBACX,cAAc;oBACd,MAAM,EAAE,QAAQ;iBACjB;aACF,CAAC,CAAC;YAEH,IAAI,CAAC,KAAK,EAAE,CAAC;gBACX,MAAM,IAAI,0BAAiB,CAAC,+BAA+B,CAAC,CAAC;YAC/D,CAAC;YAED,MAAM,MAAM,GAAG,KAAK,CAAC,MAAa,CAAC;YAGnC,IAAI,SAAS,GAAG,UAAU,CAAC,SAAS,CAAC;YACrC,IAAI,CAAC,SAAS,EAAE,CAAC;gBACf,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,MAAM,EAAE,cAAc,EAAE;oBACxE,IAAI,EAAE,OAAc;oBACpB,QAAQ,EAAE,OAAO;oBACjB,QAAQ,EAAE;wBACR,SAAS,EAAE,KAAK,CAAC,IAAI;wBACrB,KAAK,EAAE,MAAM,CAAC,KAAK;qBACpB;iBACF,CAAC,CAAC;gBACH,SAAS,GAAG,OAAO,CAAC,EAAE,CAAC;YACzB,CAAC;YAGD,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,SAAS,EAAE,cAAc,EAAE,MAAM,CAAC,CAAC;YACtF,MAAM,aAAa,GAAG,OAAO,CAAC,MAAa,CAAC;YAG5C,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CACnC,MAAM,EACN,UAAU,CAAC,KAAK,EAChB,aAAa,CAAC,QAAQ,EACtB,UAAU,CAAC,OAAO,CACnB,CAAC;YAGF,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAC3C,MAAM,EACN,QAAQ,EACR,UAAU,CAAC,SAAS,CACrB,CAAC;YAGF,MAAM,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,SAAS,EAAE,cAAc,EAAE,MAAM,EAAE;gBACvE,IAAI,EAAE,MAAM;gBACZ,OAAO,EAAE,UAAU,CAAC,KAAK;gBACzB,QAAQ,EAAE,UAAU,CAAC,OAAO;aAC7B,CAAC,CAAC;YAEH,MAAM,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,SAAS,EAAE,cAAc,EAAE,MAAM,EAAE;gBACvE,IAAI,EAAE,WAAW;gBACjB,OAAO,EAAE,MAAM,CAAC,MAAM;gBACtB,QAAQ,EAAE;oBACR,KAAK,EAAE,MAAM,CAAC,KAAK;oBACnB,KAAK,EAAE,MAAM,CAAC,KAAK;oBACnB,aAAa,EAAE,MAAM,CAAC,aAAa;iBACpC;aACF,CAAC,CAAC;YAGH,IAAI,UAAU,CAAC,OAAO,EAAE,CAAC;gBACvB,MAAM,IAAI,CAAC,eAAe,CAAC,aAAa,CAAC,SAAS,EAAE,cAAc,EAAE,MAAM,EAAE,UAAU,CAAC,OAAO,CAAC,CAAC;YAClG,CAAC;YAGD,MAAM,IAAI,CAAC,UAAU,CAAC,cAAc,EAAE,OAAO,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC;YAG7D,MAAM,IAAI,CAAC,WAAW,CAAC,iBAAiB,CACtC,qBAAqB,EACrB,OAAO,EACP,cAAc,EACd;gBACE,SAAS;gBACT,KAAK,EAAE,MAAM,CAAC,KAAK;gBACnB,aAAa,EAAE,MAAM,CAAC,aAAa;aACpC,CACF,CAAC;YAEF,OAAO;gBACL,GAAG,MAAM;gBACT,SAAS;gBACT,aAAa,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS;aACtC,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,aAAa,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAE7C,MAAM,IAAI,CAAC,WAAW,CAAC,iBAAiB,CACtC,kBAAkB,EAClB,OAAO,EACP,cAAc,EACd;gBACE,KAAK,EAAE,KAAK,CAAC,OAAO;gBACpB,aAAa;aACd,CACF,CAAC;YAEF,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,CAAC,qBAAqB,CAC1B,OAAe,EACf,cAAsB,EACtB,MAAc,EACd,UAA2B;QAE3B,IAAI,CAAC;YAEH,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC;gBAC9C,KAAK,EAAE;oBACL,EAAE,EAAE,OAAO;oBACX,cAAc;oBACd,MAAM,EAAE,QAAQ;iBACjB;aACF,CAAC,CAAC;YAEH,IAAI,CAAC,KAAK,EAAE,CAAC;gBACX,MAAM,IAAI,0BAAiB,CAAC,+BAA+B,CAAC,CAAC;YAC/D,CAAC;YAED,MAAM,MAAM,GAAG,KAAK,CAAC,MAAa,CAAC;YAGnC,IAAI,SAAS,GAAG,UAAU,CAAC,SAAS,CAAC;YACrC,IAAI,CAAC,SAAS,EAAE,CAAC;gBACf,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,MAAM,EAAE,cAAc,EAAE;oBACxE,IAAI,EAAE,OAAc;oBACpB,QAAQ,EAAE,OAAO;oBACjB,QAAQ,EAAE;wBACR,SAAS,EAAE,KAAK,CAAC,IAAI;wBACrB,KAAK,EAAE,MAAM,CAAC,KAAK;qBACpB;iBACF,CAAC,CAAC;gBACH,SAAS,GAAG,OAAO,CAAC,EAAE,CAAC;YACzB,CAAC;YAED,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,SAAS,EAAE,CAAC;YAGnC,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,SAAS,EAAE,cAAc,EAAE,MAAM,CAAC,CAAC;YACtF,MAAM,aAAa,GAAG,OAAO,CAAC,MAAa,CAAC;YAG5C,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CACnC,MAAM,EACN,UAAU,CAAC,KAAK,EAChB,aAAa,CAAC,QAAQ,EACtB,UAAU,CAAC,OAAO,CACnB,CAAC;YAGF,IAAI,WAAW,GAAG,EAAE,CAAC;YACrB,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;gBACvD,KAAK,EAAE,UAAU,CAAC,SAAS,EAAE,KAAK,IAAI,MAAM,CAAC,KAAK;gBAClD,QAAQ;gBACR,WAAW,EAAE,UAAU,CAAC,SAAS,EAAE,WAAW,IAAI,MAAM,CAAC,WAAW;gBACpE,UAAU,EAAE,UAAU,CAAC,SAAS,EAAE,SAAS,IAAI,MAAM,CAAC,SAAS;gBAC/D,MAAM,EAAE,IAAI;aACb,CAAC,CAAC;YAEH,IAAI,KAAK,EAAE,MAAM,KAAK,IAAI,MAAM,EAAE,CAAC;gBACjC,MAAM,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,OAAO,IAAI,EAAE,CAAC;gBACvD,IAAI,OAAO,EAAE,CAAC;oBACZ,WAAW,IAAI,OAAO,CAAC;oBACvB,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,CAAC;gBACnC,CAAC;YACH,CAAC;YAGD,MAAM,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,SAAS,EAAE,cAAc,EAAE,MAAM,EAAE;gBACvE,IAAI,EAAE,MAAM;gBACZ,OAAO,EAAE,UAAU,CAAC,KAAK;gBACzB,QAAQ,EAAE,UAAU,CAAC,OAAO;aAC7B,CAAC,CAAC;YAEH,MAAM,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,SAAS,EAAE,cAAc,EAAE,MAAM,EAAE;gBACvE,IAAI,EAAE,WAAW;gBACjB,OAAO,EAAE,WAAW;gBACpB,QAAQ,EAAE;oBACR,KAAK,EAAE,MAAM,CAAC,KAAK;oBACnB,SAAS,EAAE,IAAI;iBAChB;aACF,CAAC,CAAC;YAEH,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,SAAS,EAAE,CAAC;YAEjC,MAAM,IAAI,CAAC,WAAW,CAAC,iBAAiB,CACtC,qBAAqB,EACrB,OAAO,EACP,cAAc,EACd;gBACE,SAAS;gBACT,SAAS,EAAE,IAAI;aAChB,CACF,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC;YAE9C,MAAM,IAAI,CAAC,WAAW,CAAC,iBAAiB,CACtC,kBAAkB,EAClB,OAAO,EACP,cAAc,EACd;gBACE,KAAK,EAAE,KAAK,CAAC,OAAO;gBACpB,SAAS,EAAE,IAAI;aAChB,CACF,CAAC;QACJ,CAAC;IACH,CAAC;IAEO,eAAe,CACrB,MAAW,EACX,KAAa,EACb,eAAsB,EACtB,OAA6B;QAE7B,MAAM,QAAQ,GAAU,EAAE,CAAC;QAG3B,IAAI,MAAM,CAAC,YAAY,EAAE,CAAC;YACxB,QAAQ,CAAC,IAAI,CAAC;gBACZ,IAAI,EAAE,QAAQ;gBACd,OAAO,EAAE,MAAM,CAAC,YAAY;aAC7B,CAAC,CAAC;QACL,CAAC;QAGD,IAAI,aAAa,GAAG,MAAM,CAAC,MAAM,CAAC;QAGlC,IAAI,OAAO,EAAE,CAAC;YACZ,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,EAAE;gBAC/C,MAAM,WAAW,GAAG,KAAK,GAAG,IAAI,CAAC;gBACjC,aAAa,GAAG,aAAa,CAAC,OAAO,CAAC,IAAI,MAAM,CAAC,WAAW,EAAE,GAAG,CAAC,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;YACrF,CAAC,CAAC,CAAC;QACL,CAAC;QAED,QAAQ,CAAC,IAAI,CAAC;YACZ,IAAI,EAAE,QAAQ;YACd,OAAO,EAAE,aAAa;SACvB,CAAC,CAAC;QAGH,IAAI,MAAM,CAAC,MAAM,EAAE,OAAO,IAAI,eAAe,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACzD,MAAM,cAAc,GAAG,MAAM,CAAC,MAAM,CAAC,QAAQ,IAAI,SAAS,CAAC;YAC3D,MAAM,WAAW,GAAG,MAAM,CAAC,MAAM,CAAC,WAAW,IAAI,EAAE,CAAC;YAEpD,IAAI,iBAAiB,GAAG,eAAe,CAAC;YAExC,IAAI,cAAc,KAAK,SAAS,EAAE,CAAC;gBACjC,iBAAiB,GAAG,eAAe,CAAC,KAAK,CAAC,CAAC,WAAW,CAAC,CAAC;YAC1D,CAAC;iBAAM,IAAI,cAAc,KAAK,WAAW,IAAI,eAAe,CAAC,MAAM,GAAG,WAAW,EAAE,CAAC;gBAGlF,iBAAiB,GAAG,eAAe,CAAC,KAAK,CAAC,CAAC,WAAW,CAAC,CAAC;YAC1D,CAAC;YAED,iBAAiB,CAAC,OAAO,CAAC,CAAC,GAAQ,EAAE,EAAE;gBACrC,IAAI,GAAG,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;oBAC1B,QAAQ,CAAC,IAAI,CAAC;wBACZ,IAAI,EAAE,GAAG,CAAC,IAAI;wBACd,OAAO,EAAE,GAAG,CAAC,OAAO;qBACrB,CAAC,CAAC;gBACL,CAAC;YACH,CAAC,CAAC,CAAC;QACL,CAAC;QAGD,QAAQ,CAAC,IAAI,CAAC;YACZ,IAAI,EAAE,MAAM;YACZ,OAAO,EAAE,KAAK;SACf,CAAC,CAAC;QAEH,OAAO,QAAQ,CAAC;IAClB,CAAC;IAEO,KAAK,CAAC,mBAAmB,CAC/B,MAAW,EACX,QAAe,EACf,SAAe;QAEf,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;gBACzD,KAAK,EAAE,SAAS,EAAE,KAAK,IAAI,MAAM,CAAC,KAAK;gBACvC,QAAQ;gBACR,WAAW,EAAE,SAAS,EAAE,WAAW,IAAI,MAAM,CAAC,WAAW;gBACzD,UAAU,EAAE,SAAS,EAAE,SAAS,IAAI,MAAM,CAAC,SAAS;aACrD,CAAC,CAAC;YAEH,MAAM,MAAM,GAAG,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,OAAO,IAAI,EAAE,CAAC;YAC3D,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC;YAE7B,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,MAAM;gBACN,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC;oBACb,YAAY,EAAE,KAAK,CAAC,aAAa;oBACjC,gBAAgB,EAAE,KAAK,CAAC,iBAAiB;oBACzC,WAAW,EAAE,KAAK,CAAC,YAAY;iBAChC,CAAC,CAAC,CAAC,SAAS;gBACb,aAAa,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS;gBACrC,SAAS,EAAE,EAAE;aACd,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,MAAM,EAAE,EAAE;gBACV,KAAK,EAAE,KAAK,CAAC,OAAO;gBACpB,aAAa,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS;gBACrC,SAAS,EAAE,EAAE;aACd,CAAC;QACJ,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,UAAU,CAAC,cAAsB,EAAE,OAAe,EAAE,KAAW;QAC3E,IAAI,CAAC,KAAK;YAAE,OAAO;QAEnB,MAAM,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC;YACpC,IAAI,EAAE;gBACJ,cAAc;gBACd,IAAI,EAAE,iBAAiB;gBACvB,QAAQ,EAAE,KAAK,CAAC,WAAW;gBAC3B,IAAI,EAAE,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,WAAW,CAAC;gBAC3C,QAAQ,EAAE;oBACR,OAAO;oBACP,YAAY,EAAE,KAAK,CAAC,YAAY;oBAChC,gBAAgB,EAAE,KAAK,CAAC,gBAAgB;iBACzC;aACF;SACF,CAAC,CAAC;QAGH,MAAM,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC;YAC7B,KAAK,EAAE;gBACL,mBAAmB,EAAE;oBACnB,cAAc;oBACd,IAAI,EAAE,WAAW;iBAClB;aACF;YACD,MAAM,EAAE;gBACN,cAAc;gBACd,IAAI,EAAE,WAAW;gBACjB,KAAK,EAAE,IAAI;gBACX,IAAI,EAAE,CAAC;gBACP,OAAO,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;aACzD;YACD,MAAM,EAAE;gBACN,IAAI,EAAE;oBACJ,SAAS,EAAE,CAAC;iBACb;aACF;SACF,CAAC,CAAC;IACL,CAAC;IAEO,aAAa,CAAC,MAAc;QAGlC,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC,GAAG,KAAK,CAAC;IACjC,CAAC;IAED,KAAK,CAAC,mBAAmB,CAAC,OAAe,EAAE,cAAsB,EAAE,KAAK,GAAG,EAAE;QAC3E,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC;YAClC,KAAK,EAAE;gBACL,cAAc;gBACd,IAAI,EAAE,OAAO;gBACb,QAAQ,EAAE;oBACR,IAAI,EAAE,CAAC,UAAU,CAAC;oBAClB,MAAM,EAAE,OAAO;iBAChB;aACF;YACD,OAAO,EAAE;gBACP,IAAI,EAAE;oBACJ,MAAM,EAAE;wBACN,EAAE,EAAE,IAAI;wBACR,IAAI,EAAE,IAAI;wBACV,KAAK,EAAE,IAAI;qBACZ;iBACF;aACF;YACD,OAAO,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE;YAC9B,IAAI,EAAE,KAAK;SACZ,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,mBAAmB,CAAC,OAAe,EAAE,cAAsB;QAC/D,MAAM,CAAC,eAAe,EAAE,gBAAgB,EAAE,WAAW,EAAE,aAAa,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;YACxF,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC;gBACxB,KAAK,EAAE;oBACL,cAAc;oBACd,IAAI,EAAE,OAAO;oBACb,QAAQ,EAAE;wBACR,IAAI,EAAE,CAAC,UAAU,CAAC;wBAClB,MAAM,EAAE,OAAO;qBAChB;iBACF;aACF,CAAC;YACF,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,SAAS,CAAC;gBACjC,KAAK,EAAE;oBACL,cAAc;oBACd,IAAI,EAAE,iBAAiB;oBACvB,QAAQ,EAAE;wBACR,IAAI,EAAE,CAAC,SAAS,CAAC;wBACjB,MAAM,EAAE,OAAO;qBAChB;iBACF;gBACD,IAAI,EAAE;oBACJ,QAAQ,EAAE,IAAI;iBACf;aACF,CAAC;YACF,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,SAAS,CAAC;gBACjC,KAAK,EAAE;oBACL,cAAc;oBACd,IAAI,EAAE,iBAAiB;oBACvB,QAAQ,EAAE;wBACR,IAAI,EAAE,CAAC,SAAS,CAAC;wBACjB,MAAM,EAAE,OAAO;qBAChB;iBACF;gBACD,IAAI,EAAE;oBACJ,QAAQ,EAAE,IAAI;iBACf;aACF,CAAC;YACF,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC;gBAC5B,KAAK,EAAE;oBACL,cAAc;oBACd,IAAI,EAAE,OAAO;oBACb,QAAQ,EAAE;wBACR,IAAI,EAAE,CAAC,UAAU,CAAC;wBAClB,MAAM,EAAE,OAAO;qBAChB;iBACF;gBACD,OAAO,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE;aAC/B,CAAC;SACH,CAAC,CAAC;QAEH,OAAO;YACL,eAAe;YACf,qBAAqB,EAAE,gBAAgB,CAAC,IAAI,CAAC,QAAQ,IAAI,CAAC;YAC1D,eAAe,EAAE,WAAW,CAAC,IAAI,CAAC,QAAQ,IAAI,CAAC;YAC/C,cAAc,EAAE,aAAa,EAAE,SAAS;SACzC,CAAC;IACJ,CAAC;CACF,CAAA;AA1dY,oDAAoB;+BAApB,oBAAoB;IADhC,IAAA,mBAAU,GAAE;qCAKO,8BAAa;QACR,2BAAY;QACR,kCAAe,sBACjB,sBAAa,oBAAb,sBAAa;GAP3B,oBAAoB,CA0dhC"}