import { PrismaService } from '../prisma/prisma.service';
import { APIMXService } from '../apix/apix.service';
import { CreateAgentDto, UpdateAgentDto, AgentQueryDto, AgentVersionDto, CloneAgentDto } from './dto/agent.dto';
export declare class AgentsService {
    private prisma;
    private apixService;
    constructor(prisma: PrismaService, apixService: APIMXService);
    create(userId: string, organizationId: string, createAgentDto: CreateAgentDto): Promise<any>;
    findAll(organizationId: string, query: AgentQueryDto): Promise<{
        agents: any;
        pagination: {
            page: number;
            limit: number;
            total: any;
            pages: number;
        };
    }>;
    findOne(id: string, organizationId: string): Promise<any>;
    update(id: string, organizationId: string, updateAgentDto: UpdateAgentDto): Promise<any>;
    remove(id: string, organizationId: string): Promise<{
        message: string;
    }>;
    createVersion(id: string, organizationId: string, versionDto: AgentVersionDto): Promise<any>;
    rollbackToVersion(id: string, organizationId: string, version: string): Promise<any>;
    clone(id: string, organizationId: string, cloneDto: CloneAgentDto): Promise<any>;
    getAgentStats(organizationId: string): Promise<{
        total: any;
        executions: any;
        byStatus: any;
        byModel: {};
    }>;
    getPopularModels(organizationId: string): Promise<{
        model: string;
        count: unknown;
    }[]>;
    searchAgentsByTags(organizationId: string, tags: string[]): Promise<any>;
}
