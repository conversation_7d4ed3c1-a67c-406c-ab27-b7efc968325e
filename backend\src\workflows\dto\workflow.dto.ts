import { IsString, IsOptional, Is<PERSON>rray, IsObject, IsEnum, IsBoolean, ValidateNested, IsUUID } from 'class-validator';
import { Type, Transform } from 'class-transformer';

export enum WorkflowNodeType {
  AGENT = 'agent',
  TOOL = 'tool',
  CONDITION = 'condition',
  PARALLEL = 'parallel',
  SEQUENCE = 'sequence',
  TRIGGER = 'trigger',
  HUMAN_APPROVAL = 'human_approval',
}

export enum WorkflowStatus {
  DRAFT = 'draft',
  ACTIVE = 'active',
  PAUSED = 'paused',
  ARCHIVED = 'archived',
}

export enum WorkflowExecutionStatus {
  PENDING = 'pending',
  RUNNING = 'running',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled',
  WAITING_APPROVAL = 'waiting_approval',
}

export class WorkflowNodeDto {
  @IsString()
  id: string;

  @IsEnum(WorkflowNodeType)
  type: WorkflowNodeType;

  @IsString()
  name: string;

  @IsOptional()
  @IsString()
  description?: string;

  @IsObject()
  config: any;

  @IsOptional()
  @IsArray()
  inputs?: string[];

  @IsOptional()
  @IsArray()
  outputs?: string[];

  @IsOptional()
  @IsObject()
  position?: { x: number; y: number };

  @IsOptional()
  @IsObject()
  metadata?: any;
}

export class WorkflowConnectionDto {
  @IsString()
  id: string;

  @IsString()
  sourceNodeId: string;

  @IsString()
  targetNodeId: string;

  @IsOptional()
  @IsString()
  sourceOutput?: string;

  @IsOptional()
  @IsString()
  targetInput?: string;

  @IsOptional()
  @IsString()
  condition?: string;

  @IsOptional()
  @IsObject()
  metadata?: any;
}

export class CreateWorkflowDto {
  @IsString()
  name: string;

  @IsOptional()
  @IsString()
  description?: string;

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => WorkflowNodeDto)
  nodes: WorkflowNodeDto[];

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => WorkflowConnectionDto)
  connections: WorkflowConnectionDto[];

  @IsOptional()
  @IsEnum(WorkflowStatus)
  status?: WorkflowStatus;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];

  @IsOptional()
  @IsObject()
  metadata?: any;

  @IsOptional()
  @IsObject()
  settings?: {
    timeout?: number;
    retryPolicy?: {
      maxRetries: number;
      backoffStrategy: 'linear' | 'exponential';
      backoffDelay: number;
    };
    errorHandling?: 'stop' | 'continue' | 'retry';
    parallelism?: number;
  };
}

export class UpdateWorkflowDto extends CreateWorkflowDto {
  @IsOptional()
  @IsString()
  name?: string;

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => WorkflowNodeDto)
  nodes?: WorkflowNodeDto[];

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => WorkflowConnectionDto)
  connections?: WorkflowConnectionDto[];
}

export class ExecuteWorkflowDto {
  @IsOptional()
  @IsObject()
  input?: any;

  @IsOptional()
  @IsString()
  sessionId?: string;

  @IsOptional()
  @IsObject()
  context?: any;

  @IsOptional()
  @IsObject()
  overrides?: {
    timeout?: number;
    skipValidation?: boolean;
    dryRun?: boolean;
  };
}

export class WorkflowQueryDto {
  @IsOptional()
  @IsString()
  search?: string;

  @IsOptional()
  @IsEnum(WorkflowStatus)
  status?: WorkflowStatus;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];

  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  page?: number = 1;

  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  limit?: number = 20;

  @IsOptional()
  @IsString()
  sortBy?: string = 'createdAt';

  @IsOptional()
  @IsString()
  sortOrder?: 'asc' | 'desc' = 'desc';
}

export class WorkflowExecutionQueryDto {
  @IsOptional()
  @IsEnum(WorkflowExecutionStatus)
  status?: WorkflowExecutionStatus;

  @IsOptional()
  @IsString()
  workflowId?: string;

  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  page?: number = 1;

  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  limit?: number = 20;
}

export class CloneWorkflowDto {
  @IsString()
  name: string;

  @IsOptional()
  @IsString()
  description?: string;

  @IsOptional()
  @IsBoolean()
  includeExecutions?: boolean = false;
}