"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TemplatesService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
const apix_service_1 = require("../apix/apix.service");
let TemplatesService = class TemplatesService {
    constructor(prisma, apixService) {
        this.prisma = prisma;
        this.apixService = apixService;
    }
    async create(userId, organizationId, createTemplateDto) {
        const { tags = [], metadata = {}, ...templateData } = createTemplateDto;
        const template = await this.prisma.template.create({
            data: {
                ...templateData,
                organizationId: createTemplateDto.isPublic ? null : organizationId,
                metadata: {
                    ...metadata,
                    tags,
                    createdBy: userId,
                    version: metadata.version || '1.0.0',
                    downloads: 0,
                    forks: 0,
                    ratings: [],
                    avgRating: 0,
                },
            },
        });
        await this.apixService.publishEvent({
            type: 'template.created',
            organizationId,
            payload: {
                templateId: template.id,
                name: template.name,
                category: template.category,
                isPublic: template.isPublic,
            },
        });
        return template;
    }
    async findAll(organizationId, query) {
        const { page = 1, limit = 20, category, search, isPublic, tags, sortBy = 'createdAt', sortOrder = 'desc', } = query;
        const skip = (page - 1) * limit;
        const where = {
            OR: [
                { organizationId },
                { isPublic: true },
            ],
        };
        if (category) {
            where.category = category;
        }
        if (isPublic !== undefined) {
            where.isPublic = isPublic;
        }
        if (search) {
            where.AND = [
                where.AND || {},
                {
                    OR: [
                        { name: { contains: search, mode: 'insensitive' } },
                        { description: { contains: search, mode: 'insensitive' } },
                    ],
                },
            ];
        }
        if (tags && tags.length > 0) {
            where.metadata = {
                path: ['tags'],
                array_contains: tags,
            };
        }
        const [templates, total] = await Promise.all([
            this.prisma.template.findMany({
                where,
                include: {
                    organization: {
                        select: {
                            id: true,
                            name: true,
                            slug: true,
                        },
                    },
                },
                orderBy: { [sortBy]: sortOrder },
                skip,
                take: limit,
            }),
            this.prisma.template.count({ where }),
        ]);
        return {
            templates,
            pagination: {
                page,
                limit,
                total,
                pages: Math.ceil(total / limit),
            },
        };
    }
    async findOne(id, organizationId) {
        const template = await this.prisma.template.findUnique({
            where: { id },
            include: {
                organization: {
                    select: {
                        id: true,
                        name: true,
                        slug: true,
                    },
                },
            },
        });
        if (!template) {
            throw new common_1.NotFoundException('Template not found');
        }
        if (!template.isPublic && template.organizationId !== organizationId) {
            throw new common_1.ForbiddenException('Access denied');
        }
        if (template.organizationId !== organizationId) {
            await this.incrementDownloadCount(template.id);
        }
        return template;
    }
    async update(id, organizationId, updateTemplateDto) {
        const template = await this.prisma.template.findUnique({
            where: { id },
        });
        if (!template) {
            throw new common_1.NotFoundException('Template not found');
        }
        if (template.organizationId !== organizationId) {
            throw new common_1.ForbiddenException('Only the creator can update this template');
        }
        const updatedTemplate = await this.prisma.template.update({
            where: { id },
            data: {
                ...updateTemplateDto,
                template: updateTemplateDto.template ? {
                    ...template.template,
                    ...updateTemplateDto.template,
                } : template.template,
                metadata: updateTemplateDto.metadata ? {
                    ...template.metadata,
                    ...updateTemplateDto.metadata,
                    updatedAt: new Date(),
                } : template.metadata,
                updatedAt: new Date(),
            },
        });
        await this.apixService.publishEvent({
            type: 'template.updated',
            organizationId,
            payload: {
                templateId: template.id,
                changes: updateTemplateDto,
            },
        });
        return updatedTemplate;
    }
    async remove(id, organizationId) {
        const template = await this.prisma.template.findUnique({
            where: { id },
        });
        if (!template) {
            throw new common_1.NotFoundException('Template not found');
        }
        if (template.organizationId !== organizationId) {
            throw new common_1.ForbiddenException('Only the creator can delete this template');
        }
        const agentsUsingTemplate = await this.prisma.agent.count({
            where: { templateId: id },
        });
        if (agentsUsingTemplate > 0) {
            throw new common_1.BadRequestException('Cannot delete template that is being used by agents');
        }
        await this.prisma.template.delete({
            where: { id },
        });
        await this.apixService.publishEvent({
            type: 'template.deleted',
            organizationId,
            payload: {
                templateId: id,
                name: template.name,
            },
        });
        return { message: 'Template deleted successfully' };
    }
    async fork(id, organizationId, forkDto) {
        const originalTemplate = await this.findOne(id, organizationId);
        const forkedTemplate = await this.prisma.template.create({
            data: {
                name: forkDto.name,
                description: forkDto.description || `Fork of ${originalTemplate.name}`,
                category: originalTemplate.category,
                template: originalTemplate.template,
                isPublic: forkDto.isPublic,
                organizationId: forkDto.isPublic ? null : organizationId,
                metadata: {
                    ...originalTemplate.metadata,
                    forkedFrom: originalTemplate.id,
                    forkedAt: new Date(),
                    downloads: 0,
                    forks: 0,
                    ratings: [],
                    avgRating: 0,
                },
            },
        });
        await this.incrementForkCount(originalTemplate.id);
        await this.apixService.publishEvent({
            type: 'template.forked',
            organizationId,
            payload: {
                originalId: originalTemplate.id,
                forkedId: forkedTemplate.id,
                originalName: originalTemplate.name,
                forkedName: forkedTemplate.name,
            },
        });
        return forkedTemplate;
    }
    async rate(id, organizationId, userId, rateDto) {
        const template = await this.findOne(id, organizationId);
        const metadata = template.metadata;
        const ratings = metadata.ratings || [];
        const existingRatingIndex = ratings.findIndex((r) => r.userId === userId);
        if (existingRatingIndex >= 0) {
            ratings[existingRatingIndex] = {
                userId,
                organizationId,
                rating: rateDto.rating,
                review: rateDto.review,
                updatedAt: new Date(),
            };
        }
        else {
            ratings.push({
                userId,
                organizationId,
                rating: rateDto.rating,
                review: rateDto.review,
                createdAt: new Date(),
            });
        }
        const avgRating = ratings.reduce((sum, r) => sum + r.rating, 0) / ratings.length;
        const updatedTemplate = await this.prisma.template.update({
            where: { id },
            data: {
                metadata: {
                    ...metadata,
                    ratings,
                    avgRating: parseFloat(avgRating.toFixed(1)),
                },
            },
        });
        await this.apixService.publishEvent({
            type: 'template.rated',
            organizationId,
            payload: {
                templateId: id,
                rating: rateDto.rating,
                avgRating: updatedTemplate.metadata['avgRating'],
            },
        });
        return updatedTemplate;
    }
    async getPopularTemplates(organizationId, limit = 10) {
        return this.prisma.template.findMany({
            where: {
                OR: [
                    { organizationId },
                    { isPublic: true },
                ],
            },
            orderBy: [
                { metadata: { path: ['downloads'], sort: 'desc' } },
                { metadata: { path: ['avgRating'], sort: 'desc' } },
            ],
            take: limit,
            include: {
                organization: {
                    select: {
                        id: true,
                        name: true,
                        slug: true,
                    },
                },
            },
        });
    }
    async getTemplatesByCategory(organizationId, category) {
        return this.prisma.template.findMany({
            where: {
                category,
                OR: [
                    { organizationId },
                    { isPublic: true },
                ],
            },
            orderBy: { metadata: { path: ['avgRating'], sort: 'desc' } },
            include: {
                organization: {
                    select: {
                        id: true,
                        name: true,
                        slug: true,
                    },
                },
            },
        });
    }
    async getTemplateStats(organizationId) {
        const [total, byCategory, byVisibility, totalDownloads] = await Promise.all([
            this.prisma.template.count({
                where: { organizationId },
            }),
            this.prisma.template.groupBy({
                by: ['category'],
                where: { organizationId },
                _count: true,
            }),
            this.prisma.template.groupBy({
                by: ['isPublic'],
                where: { organizationId },
                _count: true,
            }),
            this.prisma.template.aggregate({
                where: { organizationId },
                _sum: {
                    metadata: { path: ['downloads'] },
                },
            }),
        ]);
        return {
            total,
            totalDownloads: totalDownloads._sum || 0,
            byCategory: byCategory.reduce((acc, item) => {
                acc[item.category] = item._count;
                return acc;
            }, {}),
            byVisibility: byVisibility.reduce((acc, item) => {
                acc[item.isPublic ? 'public' : 'private'] = item._count;
                return acc;
            }, {}),
        };
    }
    async incrementDownloadCount(templateId) {
        const template = await this.prisma.template.findUnique({
            where: { id: templateId },
        });
        if (template) {
            const metadata = template.metadata;
            await this.prisma.template.update({
                where: { id: templateId },
                data: {
                    metadata: {
                        ...metadata,
                        downloads: (metadata.downloads || 0) + 1,
                    },
                },
            });
        }
    }
    async incrementForkCount(templateId) {
        const template = await this.prisma.template.findUnique({
            where: { id: templateId },
        });
        if (template) {
            const metadata = template.metadata;
            await this.prisma.template.update({
                where: { id: templateId },
                data: {
                    metadata: {
                        ...metadata,
                        forks: (metadata.forks || 0) + 1,
                    },
                },
            });
        }
    }
    async searchTemplates(organizationId, searchTerm, filters) {
        const where = {
            OR: [
                { organizationId },
                { isPublic: true },
            ],
            AND: {
                OR: [
                    { name: { contains: searchTerm, mode: 'insensitive' } },
                    { description: { contains: searchTerm, mode: 'insensitive' } },
                    { metadata: { path: ['tags'], array_contains: [searchTerm] } },
                ],
            },
        };
        if (filters?.category) {
            where.category = filters.category;
        }
        if (filters?.minRating) {
            where.metadata = {
                ...where.metadata,
                path: ['avgRating'],
                gte: filters.minRating,
            };
        }
        return this.prisma.template.findMany({
            where,
            include: {
                organization: {
                    select: {
                        id: true,
                        name: true,
                        slug: true,
                    },
                },
            },
            orderBy: [
                { metadata: { path: ['avgRating'], sort: 'desc' } },
                { metadata: { path: ['downloads'], sort: 'desc' } },
            ],
        });
    }
};
exports.TemplatesService = TemplatesService;
exports.TemplatesService = TemplatesService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService,
        apix_service_1.APIMXService])
], TemplatesService);
//# sourceMappingURL=templates.service.js.map