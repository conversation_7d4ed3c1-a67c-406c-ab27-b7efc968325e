import { AuthService } from './auth.service';
import { LoginDto, RegisterDto } from './dto/auth.dto';
export declare class AuthController {
    private authService;
    constructor(authService: AuthService);
    login(loginDto: LoginDto): Promise<{
        access_token: any;
        user: {
            id: any;
            email: any;
            name: any;
            avatarUrl: any;
            organizationId: any;
            organization: any;
            roles: any;
            settings: any;
        };
    }>;
    register(registerDto: RegisterDto): Promise<{
        access_token: any;
        user: {
            id: any;
            email: any;
            name: any;
            organizationId: any;
            organization: any;
            roles: any[];
            settings: any;
        };
    }>;
    getProfile(req: any): Promise<any>;
    refresh(req: any): Promise<{
        access_token: any;
    }>;
}
