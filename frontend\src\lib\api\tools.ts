import { apiClient } from './client';
import {
  Tool,
  CreateToolRequest,
  UpdateToolRequest,
  ExecuteToolRequest,
  TestToolRequest,
  ToolExecutionResult,
  ToolVersionRequest,
  ToolsResponse,
  ToolQueryParams,
  ToolStats,
  ToolMetrics,
  ToolExecutionHistory,
} from '@/types/tool';

export const toolsApi = {
  async create(data: CreateToolRequest): Promise<Tool> {
    const response = await apiClient.post('/tools', data);
    return response.data;
  },

  async getAll(params?: ToolQueryParams): Promise<ToolsResponse> {
    const response = await apiClient.get('/tools', { params });
    return response.data;
  },

  async getById(id: string): Promise<Tool> {
    const response = await apiClient.get(`/tools/${id}`);
    return response.data;
  },

  async update(id: string, data: UpdateToolRequest): Promise<Tool> {
    const response = await apiClient.put(`/tools/${id}`, data);
    return response.data;
  },

  async delete(id: string): Promise<void> {
    await apiClient.delete(`/tools/${id}`);
  },

  async execute(id: string, data: ExecuteToolRequest): Promise<ToolExecutionResult> {
    const response = await apiClient.post(`/tools/${id}/execute`, data);
    return response.data;
  },

  async test(id: string, data: TestToolRequest): Promise<ToolExecutionResult> {
    const response = await apiClient.post(`/tools/${id}/test`, data);
    return response.data;
  },

  async createVersion(id: string, data: ToolVersionRequest): Promise<Tool> {
    const response = await apiClient.post(`/tools/${id}/version`, data);
    return response.data;
  },

  async rollbackToVersion(id: string, version: string): Promise<Tool> {
    const response = await apiClient.put(`/tools/${id}/version/${version}/rollback`);
    return response.data;
  },

  async clone(id: string, data: { name: string; description?: string }): Promise<Tool> {
    const response = await apiClient.post(`/tools/${id}/clone`, data);
    return response.data;
  },

  async getStats(): Promise<ToolStats> {
    const response = await apiClient.get('/tools/stats');
    return response.data;
  },

  async getPopular(limit?: number): Promise<Tool[]> {
    const response = await apiClient.get('/tools/popular', {
      params: { limit },
    });
    return response.data;
  },

  async searchByTags(tags: string[]): Promise<Tool[]> {
    const response = await apiClient.get('/tools/search/tags', {
      params: { tags },
    });
    return response.data;
  },

  async getByCategory(category: string): Promise<Tool[]> {
    const response = await apiClient.get(`/tools/category/${category}`);
    return response.data;
  },

  async getExecutionHistory(id: string, limit?: number): Promise<ToolExecutionHistory[]> {
    const response = await apiClient.get(`/tools/${id}/execution/history`, {
      params: { limit },
    });
    return response.data;
  },

  async getExecutionMetrics(id: string): Promise<ToolMetrics> {
    const response = await apiClient.get(`/tools/${id}/execution/metrics`);
    return response.data;
  },

  // Streaming execution (for real-time tool execution)
  async executeStreaming(
    id: string,
    data: ExecuteToolRequest,
    onMessage: (message: any) => void,
    onComplete: (result: ToolExecutionResult) => void,
    onError: (error: Error) => void
  ): Promise<void> {
    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/tools/${id}/execute`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
        body: JSON.stringify({ ...data, stream: true }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error('Failed to get response reader');
      }

      const decoder = new TextDecoder();
      let buffer = '';

      while (true) {
        const { done, value } = await reader.read();
        
        if (done) break;

        buffer += decoder.decode(value, { stream: true });
        
        const lines = buffer.split('\n');
        buffer = lines.pop() || '';

        for (const line of lines) {
          if (line.trim()) {
            try {
              const message = JSON.parse(line);
              if (message.type === 'complete') {
                onComplete(message.data);
              } else {
                onMessage(message);
              }
            } catch (e) {
              console.warn('Failed to parse streaming message:', line);
            }
          }
        }
      }
    } catch (error) {
      onError(error instanceof Error ? error : new Error(String(error)));
    }
  },
};