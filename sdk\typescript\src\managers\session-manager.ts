import { Session, SessionMessage, CreateSessionRequest, PaginatedResponse, QueryParams } from '../types';
import { BaseManager } from './base-manager';

export interface SessionQueryParams extends QueryParams {
  type?: 'agent' | 'tool' | 'hybrid';
  status?: 'active' | 'completed' | 'failed';
  userId?: string;
  targetId?: string;
}

export interface AddMessageRequest {
  role: 'user' | 'assistant' | 'system';
  content: string;
  metadata?: Record<string, any>;
}

export interface UpdateContextRequest {
  context: Record<string, any>;
  merge?: boolean;
}

export class SessionManager extends BaseManager {
  /**
   * Create a new session
   */
  async create(request: CreateSessionRequest): Promise<Session> {
    this.validateRequired(request, ['type', 'targetId']);
    
    return this.request({
      method: 'POST',
      url: '/sessions',
      data: request,
    });
  }

  /**
   * Get all sessions
   */
  async list(params?: SessionQueryParams): Promise<PaginatedResponse<Session>> {
    return this.request({
      method: 'GET',
      url: '/sessions',
      params: this.buildQueryParams(params || {}),
    });
  }

  /**
   * Get a specific session by ID
   */
  async get(id: string): Promise<Session> {
    this.validateRequired({ id }, ['id']);
    
    return this.request({
      method: 'GET',
      url: `/sessions/${id}`,
    });
  }

  /**
   * Update a session
   */
  async update(id: string, updates: {
    status?: 'active' | 'completed' | 'failed';
    metadata?: Record<string, any>;
  }): Promise<Session> {
    this.validateRequired({ id }, ['id']);
    
    return this.request({
      method: 'PUT',
      url: `/sessions/${id}`,
      data: updates,
    });
  }

  /**
   * Delete a session
   */
  async delete(id: string): Promise<void> {
    this.validateRequired({ id }, ['id']);
    
    await this.request({
      method: 'DELETE',
      url: `/sessions/${id}`,
    });
  }

  /**
   * Add a message to a session
   */
  async addMessage(id: string, message: AddMessageRequest): Promise<SessionMessage> {
    this.validateRequired({ id }, ['id']);
    this.validateRequired(message, ['role', 'content']);
    
    return this.request({
      method: 'POST',
      url: `/sessions/${id}/messages`,
      data: message,
    });
  }

  /**
   * Get messages from a session
   */
  async getMessages(id: string, options?: {
    limit?: number;
    offset?: number;
    since?: string;
    role?: 'user' | 'assistant' | 'system';
  }): Promise<SessionMessage[]> {
    this.validateRequired({ id }, ['id']);
    
    return this.request({
      method: 'GET',
      url: `/sessions/${id}/messages`,
      params: this.buildQueryParams(options || {}),
    });
  }

  /**
   * Update session context
   */
  async updateContext(id: string, contextUpdate: UpdateContextRequest): Promise<Session> {
    this.validateRequired({ id }, ['id']);
    this.validateRequired(contextUpdate, ['context']);
    
    return this.request({
      method: 'PUT',
      url: `/sessions/${id}/context`,
      data: contextUpdate,
    });
  }

  /**
   * Get session context
   */
  async getContext(id: string): Promise<Record<string, any>> {
    this.validateRequired({ id }, ['id']);
    
    const session = await this.get(id);
    return session.context;
  }

  /**
   * Clear session memory
   */
  async clearMemory(id: string, options?: {
    keepLastN?: number;
    keepMessageTypes?: ('user' | 'assistant' | 'system')[];
  }): Promise<Session> {
    this.validateRequired({ id }, ['id']);
    
    return this.request({
      method: 'POST',
      url: `/sessions/${id}/clear-memory`,
      data: options || {},
    });
  }

  /**
   * Archive a session
   */
  async archive(id: string): Promise<Session> {
    this.validateRequired({ id }, ['id']);
    
    return this.request({
      method: 'POST',
      url: `/sessions/${id}/archive`,
    });
  }

  /**
   * Restore an archived session
   */
  async restore(id: string): Promise<Session> {
    this.validateRequired({ id }, ['id']);
    
    return this.request({
      method: 'POST',
      url: `/sessions/${id}/restore`,
    });
  }

  /**
   * Get session statistics
   */
  async getStats(): Promise<{
    total: number;
    active: number;
    completed: number;
    failed: number;
    byType: Record<string, number>;
    avgDuration: number;
    totalMessages: number;
  }> {
    return this.request({
      method: 'GET',
      url: '/sessions/stats',
    });
  }

  /**
   * Search sessions by content
   */
  async search(query: string, options?: {
    type?: 'agent' | 'tool' | 'hybrid';
    status?: 'active' | 'completed' | 'failed';
    dateRange?: {
      start: string;
      end: string;
    };
    limit?: number;
  }): Promise<{
    sessions: Session[];
    highlights: Record<string, string[]>;
    totalMatches: number;
  }> {
    this.validateRequired({ query }, ['query']);
    
    return this.request({
      method: 'POST',
      url: '/sessions/search',
      data: {
        query,
        ...options,
      },
    });
  }

  /**
   * Export session data
   */
  async export(id: string, format: 'json' | 'csv' | 'pdf' = 'json'): Promise<{
    session: Session;
    messages: SessionMessage[];
    exportedAt: string;
    format: string;
    downloadUrl?: string;
  }> {
    this.validateRequired({ id }, ['id']);
    
    return this.request({
      method: 'GET',
      url: `/sessions/${id}/export`,
      params: { format },
    });
  }

  /**
   * Batch export multiple sessions
   */
  async batchExport(
    sessionIds: string[], 
    format: 'json' | 'csv' | 'pdf' = 'json'
  ): Promise<{
    sessions: Session[];
    exportedAt: string;
    format: string;
    downloadUrl: string;
  }> {
    this.validateRequired({ sessionIds }, ['sessionIds']);
    
    return this.request({
      method: 'POST',
      url: '/sessions/batch/export',
      data: {
        sessionIds,
        format,
      },
    });
  }

  /**
   * Get session analytics
   */
  async getAnalytics(id: string): Promise<{
    sessionId: string;
    duration: number;
    messageCount: number;
    userMessages: number;
    assistantMessages: number;
    systemMessages: number;
    avgResponseTime: number;
    totalTokens: number;
    totalCost: number;
    sentiment: {
      overall: 'positive' | 'neutral' | 'negative';
      scores: number[];
    };
    topics: string[];
    summary: string;
  }> {
    this.validateRequired({ id }, ['id']);
    
    return this.request({
      method: 'GET',
      url: `/sessions/${id}/analytics`,
    });
  }

  /**
   * Get active sessions for current user
   */
  async getActiveSessions(): Promise<Session[]> {
    return this.request({
      method: 'GET',
      url: '/sessions/active',
    });
  }

  /**
   * Get recent sessions
   */
  async getRecent(limit = 10): Promise<Session[]> {
    return this.request({
      method: 'GET',
      url: '/sessions/recent',
      params: { limit },
    });
  }

  /**
   * Create a shared session link
   */
  async createShareLink(
    id: string, 
    options?: {
      expiresAt?: string;
      permissions?: ('read' | 'comment' | 'edit')[];
      password?: string;
    }
  ): Promise<{
    shareId: string;
    shareUrl: string;
    expiresAt?: string;
    permissions: string[];
  }> {
    this.validateRequired({ id }, ['id']);
    
    return this.request({
      method: 'POST',
      url: `/sessions/${id}/share`,
      data: options || {},
    });
  }

  /**
   * Access a shared session
   */
  async accessSharedSession(shareId: string, password?: string): Promise<{
    session: Session;
    messages: SessionMessage[];
    permissions: string[];
  }> {
    this.validateRequired({ shareId }, ['shareId']);
    
    return this.request({
      method: 'GET',
      url: `/sessions/shared/${shareId}`,
      params: password ? { password } : {},
    });
  }

  /**
   * Subscribe to session events
   */
  onSessionEvent(sessionId: string, callback: (event: any) => void): string {
    return this.subscribe({
      eventTypes: [
        'session.updated',
        'session.message_added',
        'session.completed',
        'session.context_updated'
      ],
      sessionId,
      organizationId: this.getOrganizationId(),
      callback,
    });
  }

  /**
   * Subscribe to all session events for current user
   */
  onAllSessionEvents(callback: (event: any) => void): string {
    return this.subscribe({
      eventTypes: [
        'session.created',
        'session.updated',
        'session.message_added',
        'session.completed',
        'session.deleted'
      ],
      organizationId: this.getOrganizationId(),
      callback,
    });
  }

  /**
   * Monitor session activity in real-time
   */
  async monitorActivity(
    sessionId: string,
    callback: (activity: {
      type: 'message_added' | 'typing' | 'status_changed' | 'context_updated';
      data: any;
      timestamp: string;
    }) => void
  ): Promise<string> {
    this.validateRequired({ sessionId, callback }, ['sessionId', 'callback']);
    
    return this.subscribe({
      eventTypes: [
        'session.message_added',
        'session.typing',
        'session.updated',
        'session.context_updated'
      ],
      sessionId,
      organizationId: this.getOrganizationId(),
      callback: (event) => {
        const activityType = event.type.split('.')[1];
        callback({
          type: activityType as any,
          data: event.payload,
          timestamp: event.timestamp,
        });
      },
    });
  }

  /**
   * Send typing indicator
   */
  async sendTyping(sessionId: string, isTyping: boolean): Promise<void> {
    this.validateRequired({ sessionId }, ['sessionId']);
    
    await this.request({
      method: 'POST',
      url: `/sessions/${sessionId}/typing`,
      data: { isTyping },
    });
  }

  /**
   * Set session as read
   */
  async markAsRead(sessionId: string, messageId?: string): Promise<void> {
    this.validateRequired({ sessionId }, ['sessionId']);
    
    await this.request({
      method: 'POST',
      url: `/sessions/${sessionId}/read`,
      data: messageId ? { messageId } : {},
    });
  }

  /**
   * Get session summary
   */
  async getSummary(sessionId: string): Promise<{
    sessionId: string;
    summary: string;
    keyPoints: string[];
    participants: string[];
    duration: number;
    messageCount: number;
    generatedAt: string;
  }> {
    this.validateRequired({ sessionId }, ['sessionId']);
    
    return this.request({
      method: 'GET',
      url: `/sessions/${sessionId}/summary`,
    });
  }

  /**
   * Generate session summary
   */
  async generateSummary(sessionId: string, options?: {
    includeKeyPoints?: boolean;
    maxLength?: number;
    language?: string;
  }): Promise<{
    sessionId: string;
    summary: string;
    keyPoints?: string[];
    generatedAt: string;
  }> {
    this.validateRequired({ sessionId }, ['sessionId']);
    
    return this.request({
      method: 'POST',
      url: `/sessions/${sessionId}/generate-summary`,
      data: options || {},
    });
  }
}