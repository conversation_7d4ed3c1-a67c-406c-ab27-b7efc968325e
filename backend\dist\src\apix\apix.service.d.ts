import { PrismaService } from '../prisma/prisma.service';
import { APIMXGateway } from './apix.gateway';
export interface APIMXEvent {
    id: string;
    type: string;
    organizationId: string;
    userId?: string;
    sessionId?: string;
    payload: any;
    timestamp: Date;
}
export declare class APIMXService {
    private prisma;
    private gateway;
    constructor(prisma: PrismaService, gateway: APIMXGateway);
    publishEvent(event: Omit<APIMXEvent, 'id' | 'timestamp'>): Promise<APIMXEvent>;
    getEventHistory(organizationId: string, options?: {
        type?: string;
        userId?: string;
        sessionId?: string;
        limit?: number;
        offset?: number;
    }): Promise<any>;
    cleanupOldEvents(daysToKeep?: number): Promise<any>;
    private generateEventId;
    publishAgentEvent(type: string, agentId: string, organizationId: string, data: any): Promise<APIMXEvent>;
    publishToolEvent(type: string, toolId: string, organizationId: string, data: any): Promise<APIMXEvent>;
    publishSessionEvent(type: string, sessionId: string, organizationId: string, userId: string, data: any): Promise<APIMXEvent>;
    publishBillingEvent(type: string, organizationId: string, data: any): Promise<APIMXEvent>;
    publishSystemEvent(type: string, organizationId: string, data: any): Promise<APIMXEvent>;
}
