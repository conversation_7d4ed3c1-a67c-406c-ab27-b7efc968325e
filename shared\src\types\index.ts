export interface Organization {
  id: string;
  name: string;
  slug: string;
  settings: OrganizationSettings;
  createdAt: Date;
  updatedAt: Date;
}

export interface OrganizationSettings {
  branding?: {
    logo?: string;
    primaryColor?: string;
    theme?: 'light' | 'dark';
  };
  billing?: {
    planId: string;
    quotas: Record<string, number>;
  };
  features?: string[];
}

export interface User {
  id: string;
  email: string;
  name: string;
  avatarUrl?: string;
  organizationId: string;
  roles: UserRole[];
  settings: UserSettings;
  createdAt: Date;
  updatedAt: Date;
}

export interface UserRole {
  id: string;
  name: string;
  permissions: Permission[];
  organizationId: string;
}

export interface Permission {
  id: string;
  name: string;
  resource: string;
  action: string;
}

export interface UserSettings {
  theme?: 'light' | 'dark';
  notifications?: NotificationPreferences;
  language?: string;
}

export interface NotificationPreferences {
  email: boolean;
  sms: boolean;
  webhook: boolean;
  push: boolean;
}

export interface Session {
  id: string;
  userId: string;
  organizationId: string;
  type: 'agent' | 'tool' | 'hybrid' | 'widget';
  metadata: Record<string, any>;
  memory: SessionMemory;
  status: 'active' | 'completed' | 'failed' | 'paused';
  createdAt: Date;
  updatedAt: Date;
}

export interface SessionMemory {
  messages: Message[];
  context: Record<string, any>;
  tokenCount: number;
  maxTokens: number;
}

export interface Message {
  id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  metadata?: Record<string, any>;
  timestamp: Date;
}

export interface Agent {
  id: string;
  name: string;
  description: string;
  organizationId: string;
  templateId?: string;
  config: AgentConfig;
  status: 'draft' | 'active' | 'archived';
  createdAt: Date;
  updatedAt: Date;
}

export interface AgentConfig {
  prompt: string;
  model: string;
  temperature: number;
  maxTokens: number;
  systemPrompt?: string;
  tools?: string[];
  memory?: MemoryConfig;
}

export interface MemoryConfig {
  enabled: boolean;
  maxMessages: number;
  strategy: 'rolling' | 'summarize' | 'vector';
}

export interface Tool {
  id: string;
  name: string;
  description: string;
  organizationId: string;
  type: 'api' | 'function' | 'workflow';
  config: ToolConfig;
  schema: ToolSchema;
  status: 'draft' | 'active' | 'archived';
  createdAt: Date;
  updatedAt: Date;
}

export interface ToolConfig {
  endpoint?: string;
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE';
  headers?: Record<string, string>;
  authentication?: AuthConfig;
  timeout?: number;
  retries?: number;
}

export interface AuthConfig {
  type: 'none' | 'api_key' | 'bearer' | 'basic' | 'oauth2';
  credentials: Record<string, string>;
}

export interface ToolSchema {
  input: Record<string, any>;
  output: Record<string, any>;
}

export interface Provider {
  id: string;
  name: string;
  type: 'openai' | 'anthropic' | 'google' | 'mistral' | 'groq' | 'custom';
  organizationId: string;
  config: ProviderConfig;
  status: 'active' | 'inactive' | 'error';
  metrics: ProviderMetrics;
  createdAt: Date;
  updatedAt: Date;
}

export interface ProviderConfig {
  apiKey: string;
  baseUrl?: string;
  models: string[];
  rateLimit?: number;
  timeout?: number;
}

export interface ProviderMetrics {
  requestCount: number;
  errorCount: number;
  avgLatency: number;
  avgCost: number;
  lastUsed: Date;
}

export interface HITLRequest {
  id: string;
  organizationId: string;
  sessionId: string;
  type: 'approval' | 'review' | 'escalation';
  title: string;
  description: string;
  payload: Record<string, any>;
  assigneeId?: string;
  status: 'pending' | 'approved' | 'rejected' | 'escalated';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  createdAt: Date;
  resolvedAt?: Date;
}

export interface Document {
  id: string;
  organizationId: string;
  name: string;
  type: string;
  size: number;
  url: string;
  content?: string;
  embedding?: number[];
  metadata: Record<string, any>;
  status: 'processing' | 'ready' | 'error';
  createdAt: Date;
  updatedAt: Date;
}

export interface Widget {
  id: string;
  organizationId: string;
  name: string;
  type: 'agent' | 'tool' | 'hybrid';
  targetId: string;
  config: WidgetConfig;
  embedCode: string;
  status: 'draft' | 'published' | 'archived';
  analytics: WidgetAnalytics;
  createdAt: Date;
  updatedAt: Date;
}

export interface WidgetConfig {
  theme: 'light' | 'dark' | 'custom';
  customCss?: string;
  branding: boolean;
  dimensions: {
    width: string;
    height: string;
  };
  behavior: {
    autoOpen?: boolean;
    persistent?: boolean;
  };
}

export interface WidgetAnalytics {
  views: number;
  interactions: number;
  conversions: number;
  avgSession: number;
}

export interface APIXEvent {
  id: string;
  type: string;
  organizationId: string;
  userId?: string;
  sessionId?: string;
  payload: Record<string, any>;
  timestamp: Date;
}

export interface BillingUsage {
  id: string;
  organizationId: string;
  type: string;
  quantity: number;
  cost: number;
  metadata: Record<string, any>;
  timestamp: Date;
}

export interface Quota {
  organizationId: string;
  type: string;
  limit: number;
  used: number;
  resetAt: Date;
}

export interface Notification {
  id: string;
  organizationId: string;
  userId?: string;
  type: string;
  channel: 'email' | 'sms' | 'webhook' | 'push';
  title: string;
  content: string;
  status: 'pending' | 'sent' | 'delivered' | 'failed';
  createdAt: Date;
  sentAt?: Date;
}