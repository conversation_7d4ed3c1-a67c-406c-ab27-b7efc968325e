import { JwtService } from '@nestjs/jwt';
import { PrismaService } from '../prisma/prisma.service';
import { LoginDto, RegisterDto } from './dto/auth.dto';
export declare class AuthService {
    private prisma;
    private jwtService;
    constructor(prisma: PrismaService, jwtService: JwtService);
    validateUser(email: string, password: string): Promise<any>;
    login(loginDto: LoginDto): Promise<{
        access_token: any;
        user: {
            id: any;
            email: any;
            name: any;
            avatarUrl: any;
            organizationId: any;
            organization: any;
            roles: any;
            settings: any;
        };
    }>;
    register(registerDto: RegisterDto): Promise<{
        access_token: any;
        user: {
            id: any;
            email: any;
            name: any;
            organizationId: any;
            organization: any;
            roles: any[];
            settings: any;
        };
    }>;
    verifyToken(token: string): Promise<any>;
}
