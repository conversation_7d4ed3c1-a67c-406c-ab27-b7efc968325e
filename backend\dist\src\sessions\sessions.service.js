"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SessionsService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
const apix_service_1 = require("../apix/apix.service");
let SessionsService = class SessionsService {
    constructor(prisma, apixService) {
        this.prisma = prisma;
        this.apixService = apixService;
    }
    async create(userId, organizationId, createSessionDto) {
        const { type, targetId, metadata = {} } = createSessionDto;
        const session = await this.prisma.session.create({
            data: {
                userId,
                organizationId,
                type,
                metadata: {
                    targetId,
                    ...metadata,
                },
                memory: {
                    messages: [],
                    context: {},
                    tokenCount: 0,
                    maxTokens: 4000,
                },
                status: 'active',
            },
        });
        await this.apixService.publishSessionEvent('created', session.id, organizationId, userId, {
            type: session.type,
            targetId,
        });
        return session;
    }
    async findAll(organizationId, query) {
        const { page = 1, limit = 20, type, status, userId, search, sortBy = 'createdAt', sortOrder = 'desc', } = query;
        const skip = (page - 1) * limit;
        const where = {
            organizationId,
            ...(type && { type }),
            ...(status && { status }),
            ...(userId && { userId }),
        };
        if (search) {
            where.OR = [
                { id: { contains: search, mode: 'insensitive' } },
                { metadata: { path: ['targetId'], string_contains: search } },
            ];
        }
        const [sessions, total] = await Promise.all([
            this.prisma.session.findMany({
                where,
                include: {
                    user: {
                        select: {
                            id: true,
                            name: true,
                            email: true,
                            avatarUrl: true,
                        },
                    },
                },
                orderBy: { [sortBy]: sortOrder },
                skip,
                take: limit,
            }),
            this.prisma.session.count({ where }),
        ]);
        return {
            sessions,
            pagination: {
                page,
                limit,
                total,
                pages: Math.ceil(total / limit),
            },
        };
    }
    async findOne(id, organizationId, userId) {
        const session = await this.prisma.session.findUnique({
            where: { id },
            include: {
                user: {
                    select: {
                        id: true,
                        name: true,
                        email: true,
                        avatarUrl: true,
                    },
                },
            },
        });
        if (!session) {
            throw new common_1.NotFoundException('Session not found');
        }
        if (session.organizationId !== organizationId) {
            throw new common_1.ForbiddenException('Access denied');
        }
        if (userId && session.userId !== userId) {
            throw new common_1.ForbiddenException('Access denied');
        }
        return session;
    }
    async update(id, organizationId, userId, updateSessionDto) {
        const session = await this.findOne(id, organizationId, userId);
        const updatedSession = await this.prisma.session.update({
            where: { id },
            data: {
                ...updateSessionDto,
                updatedAt: new Date(),
            },
        });
        await this.apixService.publishSessionEvent('updated', session.id, organizationId, userId, {
            changes: updateSessionDto,
        });
        return updatedSession;
    }
    async remove(id, organizationId, userId) {
        const session = await this.findOne(id, organizationId, userId);
        await this.prisma.session.delete({
            where: { id },
        });
        await this.apixService.publishSessionEvent('deleted', session.id, organizationId, userId, {});
        return { message: 'Session deleted successfully' };
    }
    async addMessage(sessionId, organizationId, userId, message) {
        const session = await this.findOne(sessionId, organizationId, userId);
        const currentMemory = session.memory;
        const messages = currentMemory.messages || [];
        const newMessage = {
            id: `msg_${Date.now()}_${Math.random().toString(36).substring(2)}`,
            role: message.role,
            content: message.content,
            metadata: message.metadata || {},
            timestamp: new Date(),
        };
        const tokenCount = this.estimateTokenCount(message.content);
        const newTokenCount = currentMemory.tokenCount + tokenCount;
        const updatedMessages = [...messages, newMessage];
        if (newTokenCount > currentMemory.maxTokens) {
            const truncatedMessages = this.truncateMessages(updatedMessages, currentMemory.maxTokens);
            updatedMessages.length = 0;
            updatedMessages.push(...truncatedMessages);
        }
        const updatedSession = await this.prisma.session.update({
            where: { id: sessionId },
            data: {
                memory: {
                    ...currentMemory,
                    messages: updatedMessages,
                    tokenCount: this.calculateTotalTokens(updatedMessages),
                },
                updatedAt: new Date(),
            },
        });
        await this.apixService.publishSessionEvent('message_added', sessionId, organizationId, userId, {
            message: newMessage,
        });
        return updatedSession;
    }
    async updateContext(sessionId, organizationId, userId, context) {
        const session = await this.findOne(sessionId, organizationId, userId);
        const currentMemory = session.memory;
        const updatedSession = await this.prisma.session.update({
            where: { id: sessionId },
            data: {
                memory: {
                    ...currentMemory,
                    context: {
                        ...currentMemory.context,
                        ...context,
                    },
                },
                updatedAt: new Date(),
            },
        });
        await this.apixService.publishSessionEvent('context_updated', sessionId, organizationId, userId, {
            context,
        });
        return updatedSession;
    }
    async clearMemory(sessionId, organizationId, userId) {
        const session = await this.findOne(sessionId, organizationId, userId);
        const currentMemory = session.memory;
        const updatedSession = await this.prisma.session.update({
            where: { id: sessionId },
            data: {
                memory: {
                    ...currentMemory,
                    messages: [],
                    context: {},
                    tokenCount: 0,
                },
                updatedAt: new Date(),
            },
        });
        await this.apixService.publishSessionEvent('memory_cleared', sessionId, organizationId, userId, {});
        return updatedSession;
    }
    async getSessionStats(organizationId, userId) {
        const where = { organizationId };
        if (userId)
            where.userId = userId;
        const [total, active, byType, byStatus] = await Promise.all([
            this.prisma.session.count({ where }),
            this.prisma.session.count({ where: { ...where, status: 'active' } }),
            this.prisma.session.groupBy({
                by: ['type'],
                where,
                _count: true,
            }),
            this.prisma.session.groupBy({
                by: ['status'],
                where,
                _count: true,
            }),
        ]);
        return {
            total,
            active,
            byType: byType.reduce((acc, item) => {
                acc[item.type] = item._count;
                return acc;
            }, {}),
            byStatus: byStatus.reduce((acc, item) => {
                acc[item.status] = item._count;
                return acc;
            }, {}),
        };
    }
    estimateTokenCount(text) {
        return Math.ceil(text.length / 4);
    }
    calculateTotalTokens(messages) {
        return messages.reduce((total, message) => {
            return total + this.estimateTokenCount(message.content);
        }, 0);
    }
    truncateMessages(messages, maxTokens) {
        let totalTokens = 0;
        const result = [];
        for (let i = messages.length - 1; i >= 0; i--) {
            const messageTokens = this.estimateTokenCount(messages[i].content);
            if (totalTokens + messageTokens > maxTokens) {
                break;
            }
            totalTokens += messageTokens;
            result.unshift(messages[i]);
        }
        return result;
    }
};
exports.SessionsService = SessionsService;
exports.SessionsService = SessionsService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService,
        apix_service_1.APIMXService])
], SessionsService);
//# sourceMappingURL=sessions.service.js.map