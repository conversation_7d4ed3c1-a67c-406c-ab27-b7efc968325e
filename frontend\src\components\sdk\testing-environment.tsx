'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { 
  Play, 
  Square, 
  RotateCcw, 
  Copy, 
  Download, 
  Settings,
  MessageSquare,
  Cog,
  Zap,
  CheckCircle,
  AlertCircle,
  Clock,
  Eye,
  Code2
} from 'lucide-react';

export function SDKTestingEnvironment() {
  const [isRunning, setIsRunning] = useState(false);
  const [selectedExample, setSelectedExample] = useState('agent-chat');
  const [testResults, setTestResults] = useState<any[]>([]);
  const [selectedAPI, setSelectedAPI] = useState('agents');

  const examples = {
    'agent-chat': {
      title: 'Chat with AI Agent',
      description: 'Test a customer service agent in real-time',
      category: 'Agents',
      complexity: 'Beginner',
      estimatedTime: '2 min',
      config: {
        agentType: 'customer-service',
        model: 'gpt-4',
        temperature: 0.7,
        systemPrompt: 'You are a helpful customer service agent.'
      }
    },
    'workflow-automation': {
      title: 'Workflow Automation',
      description: 'Test a multi-step workflow with tools',
      category: 'Workflows',
      complexity: 'Intermediate',
      estimatedTime: '5 min',
      config: {
        steps: 3,
        includesApproval: true,
        tools: ['email', 'crm', 'slack']
      }
    },
    'tool-integration': {
      title: 'API Tool Integration',
      description: 'Connect and test external API tools',
      category: 'Tools',
      complexity: 'Beginner',
      estimatedTime: '3 min',
      config: {
        apiType: 'rest',
        authentication: 'api-key',
        endpoint: 'https://api.example.com/data'
      }
    }
  };

  const apiEndpoints = {
    agents: [
      {
        method: 'POST',
        endpoint: '/agents',
        name: 'Create Agent',
        description: 'Create a new AI agent',
        params: ['name', 'description', 'config']
      },
      {
        method: 'POST',
        endpoint: '/agents/{id}/execute',
        name: 'Execute Agent',
        description: 'Run an agent with input',
        params: ['input', 'sessionId', 'stream']
      },
      {
        method: 'GET',
        endpoint: '/agents',
        name: 'List Agents',
        description: 'Get all agents in organization',
        params: ['page', 'limit', 'search']
      }
    ],
    tools: [
      {
        method: 'POST',
        endpoint: '/tools',
        name: 'Create Tool',
        description: 'Create a new API tool',
        params: ['name', 'type', 'config', 'schema']
      },
      {
        method: 'POST',
        endpoint: '/tools/{id}/execute',
        name: 'Execute Tool',
        description: 'Run a tool with parameters',
        params: ['input', 'context', 'validateInput']
      }
    ],
    workflows: [
      {
        method: 'POST',
        endpoint: '/workflows',
        name: 'Create Workflow',
        description: 'Create a new workflow',
        params: ['name', 'nodes', 'connections']
      },
      {
        method: 'POST',
        endpoint: '/workflows/{id}/execute',
        name: 'Execute Workflow',
        description: 'Run a workflow',
        params: ['input', 'context', 'overrides']
      }
    ]
  };

  const runTest = async (exampleId: string) => {
    setIsRunning(true);
    
    // Simulate API call
    const result = {
      id: Date.now(),
      example: exampleId,
      status: 'success',
      duration: Math.random() * 2000 + 500,
      timestamp: new Date(),
      response: generateMockResponse(exampleId)
    };

    setTimeout(() => {
      setTestResults(prev => [result, ...prev]);
      setIsRunning(false);
    }, result.duration);
  };

  const generateMockResponse = (exampleId: string) => {
    switch (exampleId) {
      case 'agent-chat':
        return {
          success: true,
          output: "Hello! I'm your customer service agent. How can I help you today?",
          sessionId: "sess_" + Math.random().toString(36).substr(2, 9),
          usage: {
            promptTokens: 45,
            completionTokens: 23,
            totalTokens: 68
          },
          executionTime: 1200,
          cost: 0.0034
        };
      case 'workflow-automation':
        return {
          success: true,
          executionId: "exec_" + Math.random().toString(36).substr(2, 9),
          status: 'completed',
          steps: [
            { name: 'Validate Input', status: 'completed', duration: 200 },
            { name: 'Process Data', status: 'completed', duration: 800 },
            { name: 'Send Notification', status: 'completed', duration: 300 }
          ],
          totalDuration: 1300
        };
      case 'tool-integration':
        return {
          success: true,
          output: {
            status: 200,
            data: { message: "API call successful", records: 5 },
            headers: { 'content-type': 'application/json' }
          },
          executionTime: 450,
          metadata: {
            httpStatus: 200,
            retryCount: 0
          }
        };
      default:
        return { success: true, message: 'Test completed' };
    }
  };

  return (
    <div className="space-y-6">
      <Card className="bg-gradient-to-r from-green-600 to-blue-600 text-white border-0">
        <CardContent className="p-8 text-center">
          <Play className="w-12 h-12 mx-auto mb-4 opacity-90" />
          <h2 className="text-2xl font-bold mb-2">SDK Testing Environment</h2>
          <p className="text-green-100 max-w-2xl mx-auto">
            Try out SynapseAI features without any setup. Run real examples and see the results instantly.
          </p>
        </CardContent>
      </Card>

      <Tabs defaultValue="examples" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="examples">Interactive Examples</TabsTrigger>
          <TabsTrigger value="api-testing">API Testing</TabsTrigger>
        </TabsList>

        <TabsContent value="examples" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Example Selection */}
            <div className="space-y-4">
              <h3 className="text-xl font-semibold">Choose an Example</h3>
              <div className="space-y-3">
                {Object.entries(examples).map(([id, example]) => (
                  <Card 
                    key={id}
                    className={`cursor-pointer transition-all hover:shadow-lg ${
                      selectedExample === id ? 'border-2 border-blue-500 bg-blue-50' : ''
                    }`}
                    onClick={() => setSelectedExample(id)}
                  >
                    <CardContent className="p-4">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <h4 className="font-semibold text-lg mb-1">{example.title}</h4>
                          <p className="text-gray-600 text-sm mb-3">{example.description}</p>
                          <div className="flex gap-2">
                            <Badge variant="outline" className="text-xs">
                              {example.category}
                            </Badge>
                            <Badge variant="outline" className="text-xs">
                              {example.complexity}
                            </Badge>
                            <Badge variant="outline" className="text-xs">
                              <Clock className="w-3 h-3 mr-1" />
                              {example.estimatedTime}
                            </Badge>
                          </div>
                        </div>
                        {selectedExample === id && (
                          <CheckCircle className="w-6 h-6 text-blue-600 flex-shrink-0" />
                        )}
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>

            {/* Testing Interface */}
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-xl font-semibold">Test Configuration</h3>
                <Button variant="outline" size="sm">
                  <Settings className="w-4 h-4 mr-2" />
                  Advanced
                </Button>
              </div>

              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">
                    {examples[selectedExample as keyof typeof examples].title}
                  </CardTitle>
                  <CardDescription>
                    {examples[selectedExample as keyof typeof examples].description}
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  {/* Configuration Preview */}
                  <div className="bg-gray-50 rounded-lg p-4">
                    <h5 className="font-medium mb-2">Configuration</h5>
                    <pre className="text-sm text-gray-700">
                      {JSON.stringify(examples[selectedExample as keyof typeof examples].config, null, 2)}
                    </pre>
                  </div>

                  {/* Test Input */}
                  {selectedExample === 'agent-chat' && (
                    <div>
                      <label className="block text-sm font-medium mb-2">Test Message</label>
                      <textarea 
                        className="w-full p-3 border rounded-lg resize-none"
                        rows={3}
                        placeholder="Type a message to test with the agent..."
                        defaultValue="Hi, I need help with my order status"
                      />
                    </div>
                  )}

                  {selectedExample === 'workflow-automation' && (
                    <div>
                      <label className="block text-sm font-medium mb-2">Workflow Input Data</label>
                      <textarea 
                        className="w-full p-3 border rounded-lg resize-none"
                        rows={3}
                        placeholder="Enter JSON data for the workflow..."
                        defaultValue='{"customerEmail": "<EMAIL>", "orderId": "12345"}'
                      />
                    </div>
                  )}

                  {/* Run Test Button */}
                  <Button 
                    onClick={() => runTest(selectedExample)}
                    disabled={isRunning}
                    className="w-full"
                    size="lg"
                  >
                    {isRunning ? (
                      <>
                        <Square className="w-5 h-5 mr-2 animate-pulse" />
                        Running Test...
                      </>
                    ) : (
                      <>
                        <Play className="w-5 h-5 mr-2" />
                        Run Test
                      </>
                    )}
                  </Button>
                </CardContent>
              </Card>
            </div>
          </div>

          {/* Test Results */}
          {testResults.length > 0 && (
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle>Test Results</CardTitle>
                  <Button variant="outline" size="sm" onClick={() => setTestResults([])}>
                    <RotateCcw className="w-4 h-4 mr-2" />
                    Clear
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {testResults.map((result) => (
                    <Card key={result.id} className="border-l-4 border-l-green-500">
                      <CardContent className="p-4">
                        <div className="flex items-start justify-between mb-3">
                          <div>
                            <h4 className="font-semibold flex items-center gap-2">
                              <CheckCircle className="w-5 h-5 text-green-600" />
                              {examples[result.example as keyof typeof examples].title}
                            </h4>
                            <p className="text-sm text-gray-600">
                              Completed in {Math.round(result.duration)}ms
                            </p>
                          </div>
                          <div className="flex gap-2">
                            <Button variant="outline" size="sm">
                              <Copy className="w-4 h-4 mr-1" />
                              Copy
                            </Button>
                            <Button variant="outline" size="sm">
                              <Eye className="w-4 h-4 mr-1" />
                              View Details
                            </Button>
                          </div>
                        </div>
                        
                        <div className="bg-gray-50 rounded-lg p-3 overflow-x-auto">
                          <pre className="text-sm">
                            {JSON.stringify(result.response, null, 2)}
                          </pre>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="api-testing" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* API Categories */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">API Categories</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {Object.keys(apiEndpoints).map((category) => (
                    <Button
                      key={category}
                      variant={selectedAPI === category ? "default" : "outline"}
                      className="w-full justify-start"
                      onClick={() => setSelectedAPI(category)}
                    >
                      {category === 'agents' && <MessageSquare className="w-4 h-4 mr-2" />}
                      {category === 'tools' && <Cog className="w-4 h-4 mr-2" />}
                      {category === 'workflows' && <Zap className="w-4 h-4 mr-2" />}
                      {category.charAt(0).toUpperCase() + category.slice(1)}
                    </Button>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* API Endpoints */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Endpoints</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {apiEndpoints[selectedAPI as keyof typeof apiEndpoints].map((endpoint, index) => (
                    <Card key={index} className="cursor-pointer hover:shadow-md transition-all">
                      <CardContent className="p-3">
                        <div className="flex items-start gap-3">
                          <Badge 
                            variant={endpoint.method === 'GET' ? 'secondary' : 'default'}
                            className="text-xs"
                          >
                            {endpoint.method}
                          </Badge>
                          <div className="flex-1">
                            <h4 className="font-semibold text-sm">{endpoint.name}</h4>
                            <p className="text-xs text-gray-600 mb-1">{endpoint.description}</p>
                            <code className="text-xs bg-gray-100 px-2 py-1 rounded">
                              {endpoint.endpoint}
                            </code>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* API Testing Interface */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Test API</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <label className="block text-sm font-medium mb-2">Request Body</label>
                  <textarea 
                    className="w-full p-3 border rounded-lg resize-none font-mono text-sm"
                    rows={8}
                    placeholder="Enter JSON request body..."
                    defaultValue='{\n  "name": "My Test Agent",\n  "description": "A test agent",\n  "config": {\n    "model": "gpt-4",\n    "temperature": 0.7\n  }\n}'
                  />
                </div>

                <Button className="w-full">
                  <Code2 className="w-4 h-4 mr-2" />
                  Send Request
                </Button>

                <div className="bg-gray-50 rounded-lg p-3">
                  <h5 className="font-medium text-sm mb-2">Response Preview</h5>
                  <div className="text-xs text-gray-600">
                    Click "Send Request" to see the API response here
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}