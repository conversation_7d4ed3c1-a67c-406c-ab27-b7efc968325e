"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ToolsModule = void 0;
const common_1 = require("@nestjs/common");
const tools_controller_1 = require("./tools.controller");
const tools_service_1 = require("./tools.service");
const tool_executor_service_1 = require("./tool-executor.service");
const auth_module_1 = require("../auth/auth.module");
const apix_module_1 = require("../apix/apix.module");
const sessions_module_1 = require("../sessions/sessions.module");
let ToolsModule = class ToolsModule {
};
exports.ToolsModule = ToolsModule;
exports.ToolsModule = ToolsModule = __decorate([
    (0, common_1.Module)({
        imports: [auth_module_1.AuthModule, apix_module_1.APIMXModule, sessions_module_1.SessionsModule],
        controllers: [tools_controller_1.ToolsController],
        providers: [tools_service_1.ToolsService, tool_executor_service_1.ToolExecutorService],
        exports: [tools_service_1.ToolsService, tool_executor_service_1.ToolExecutorService],
    })
], ToolsModule);
//# sourceMappingURL=tools.module.js.map