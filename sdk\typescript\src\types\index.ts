// Authentication and Organization types
export interface SynapseAIConfig {
  apiKey: string;
  baseUrl?: string;
  organizationId?: string;
  timeout?: number;
  retries?: number;
  enableWebSocket?: boolean;
  debug?: boolean;
}

export interface AuthResponse {
  token: string;
  user: User;
  organization: Organization;
  expiresAt: string;
}

export interface User {
  id: string;
  name: string;
  email: string;
  role: string;
  organizationId: string;
  createdAt: string;
  updatedAt: string;
}

export interface Organization {
  id: string;
  name: string;
  slug: string;
  settings: Record<string, any>;
  createdAt: string;
  updatedAt: string;
}

// Agent types
export interface Agent {
  id: string;
  name: string;
  description: string;
  instructions: string;
  config: AgentConfig;
  status: 'draft' | 'active' | 'archived';
  organizationId: string;
  metadata: Record<string, any>;
  createdAt: string;
  updatedAt: string;
}

export interface AgentConfig {
  instructions: string;
  temperature?: number;
  maxTokens?: number;
  topP?: number;
  frequencyPenalty?: number;
  presencePenalty?: number;
  stopSequences?: string[];
  modelPreferences?: {
    size?: 'small' | 'medium' | 'large' | 'xlarge';
    capabilities?: string[];
  };
  costLimits?: {
    maxCostPer1K?: number;
    maxTotal?: number;
  };
  preferences?: {
    prioritizeLowLatency?: boolean;
    prioritizeLowCost?: boolean;
  };
  preferredProviders?: string[];
  excludedProviders?: string[];
}

export interface CreateAgentRequest {
  name: string;
  description: string;
  instructions: string;
  config?: Partial<AgentConfig>;
  status?: 'draft' | 'active' | 'archived';
  metadata?: Record<string, any>;
}

export interface ExecuteAgentRequest {
  input: string;
  sessionId?: string;
  context?: Record<string, any>;
  stream?: boolean;
  overrides?: {
    temperature?: number;
    maxTokens?: number;
    timeout?: number;
  };
}

export interface ExecuteAgentResponse {
  success: boolean;
  output: string;
  usage?: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  };
  executionTime: number;
  sessionId: string;
  providerId?: string;
  modelId?: string;
  cost?: number;
  error?: string;
}

// Tool types
export interface Tool {
  id: string;
  name: string;
  description: string;
  type: 'api' | 'function' | 'workflow';
  config: ToolConfig;
  schema: ToolSchema;
  status: 'draft' | 'active' | 'archived';
  organizationId: string;
  metadata: Record<string, any>;
  createdAt: string;
  updatedAt: string;
}

export interface ToolConfig {
  endpoint?: string;
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  headers?: Record<string, string>;
  authentication?: {
    type: 'none' | 'api_key' | 'bearer' | 'basic';
    credentials: Record<string, string>;
  };
  timeout?: number;
  retries?: number;
  functionCode?: string;
  workflowSteps?: Array<{
    id: string;
    type: 'api' | 'function' | 'condition';
    config: any;
    nextSteps?: string[];
  }>;
}

export interface ToolSchema {
  input: Record<string, any>;
  output: Record<string, any>;
  examples?: Array<{
    name: string;
    description: string;
    input: any;
    output: any;
  }>;
}

export interface CreateToolRequest {
  name: string;
  description: string;
  type: 'api' | 'function' | 'workflow';
  config: ToolConfig;
  schema: ToolSchema;
  status?: 'draft' | 'active' | 'archived';
  tags?: string[];
  metadata?: Record<string, any>;
}

export interface ExecuteToolRequest {
  input: Record<string, any>;
  sessionId?: string;
  context?: Record<string, any>;
  validateInput?: boolean;
  overrides?: {
    timeout?: number;
    retries?: number;
    headers?: Record<string, string>;
  };
}

export interface ExecuteToolResponse {
  success: boolean;
  output: any;
  executionTime: number;
  sessionId?: string;
  error?: string;
  metadata?: {
    httpStatus?: number;
    headers?: Record<string, string>;
    retryCount?: number;
  };
}

// Provider types
export interface Provider {
  id: string;
  name: string;
  description: string;
  type: 'openai' | 'claude' | 'gemini' | 'mistral' | 'groq' | 'custom';
  config: ProviderConfig;
  models: ProviderModel[];
  status: 'active' | 'inactive' | 'maintenance' | 'error';
  priority: number;
  organizationId: string;
  metadata: Record<string, any>;
  createdAt: string;
  updatedAt: string;
}

export interface ProviderConfig {
  apiKey?: string;
  baseUrl?: string;
  headers?: Record<string, string>;
  timeout?: number;
  maxRetries?: number;
  maxConcurrentRequests?: number;
  maxRequestsPerMinute?: number;
  rateLimits?: {
    requestsPerMinute: number;
    tokensPerMinute: number;
    requestsPerDay: number;
    tokensPerDay: number;
  };
}

export interface ProviderModel {
  id: string;
  name: string;
  description?: string;
  size: 'small' | 'medium' | 'large' | 'xlarge';
  capabilities: string[];
  inputCostPer1KTokens: number;
  outputCostPer1KTokens: number;
  maxTokens?: number;
  contextWindow?: number;
  parameters?: {
    temperature?: number;
    maxTokens?: number;
    topP?: number;
    frequencyPenalty?: number;
    presencePenalty?: number;
  };
}

// Session types
export interface Session {
  id: string;
  type: 'agent' | 'tool' | 'hybrid';
  targetId: string;
  userId: string;
  organizationId: string;
  status: 'active' | 'completed' | 'failed';
  memory: SessionMemory;
  context: Record<string, any>;
  metadata: Record<string, any>;
  createdAt: string;
  updatedAt: string;
}

export interface SessionMemory {
  messages: SessionMessage[];
  totalTokens: number;
  totalCost: number;
  maxTokens: number;
}

export interface SessionMessage {
  id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  metadata: Record<string, any>;
  timestamp: string;
}

export interface CreateSessionRequest {
  type: 'agent' | 'tool' | 'hybrid';
  targetId: string;
  metadata?: Record<string, any>;
}

// WebSocket types
export interface WebSocketConfig {
  url: string;
  token: string;
  organizationId: string;
  autoReconnect?: boolean;
  maxReconnectAttempts?: number;
  reconnectInterval?: number;
}

export interface WebSocketEvent {
  id: string;
  type: string;
  organizationId: string;
  userId?: string;
  sessionId?: string;
  payload: any;
  timestamp: string;
}

export interface StreamingChunk {
  type: 'start' | 'chunk' | 'end' | 'error';
  content?: string;
  usage?: any;
  sessionId?: string;
  error?: string;
}

// API Response types
export interface APIResponse<T> {
  data: T;
  success: boolean;
  message?: string;
  errors?: string[];
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}

export interface QueryParams {
  page?: number;
  limit?: number;
  search?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

// Error types
export interface SynapseAIError {
  code: string;
  message: string;
  details?: any;
  statusCode?: number;
}

export class SynapseAIClientError extends Error {
  code: string;
  details?: any;
  statusCode?: number;

  constructor(error: SynapseAIError) {
    super(error.message);
    this.name = 'SynapseAIClientError';
    this.code = error.code;
    this.details = error.details;
    this.statusCode = error.statusCode;
  }
}

// Workflow types
export interface Workflow {
  id: string;
  name: string;
  description?: string;
  nodes: WorkflowNode[];
  connections: WorkflowConnection[];
  status: 'draft' | 'active' | 'paused' | 'archived';
  organizationId: string;
  createdBy: string;
  tags: string[];
  metadata: Record<string, any>;
  settings: WorkflowSettings;
  createdAt: string;
  updatedAt: string;
}

export interface WorkflowNode {
  id: string;
  type: 'agent' | 'tool' | 'condition' | 'parallel' | 'sequence' | 'trigger' | 'human_approval';
  name: string;
  description?: string;
  config: Record<string, any>;
  position?: { x: number; y: number };
  metadata?: Record<string, any>;
}

export interface WorkflowConnection {
  id: string;
  sourceNodeId: string;
  targetNodeId: string;
  sourceOutput?: string;
  targetInput?: string;
  condition?: string;
  metadata?: Record<string, any>;
}

export interface WorkflowSettings {
  timeout?: number;
  retryPolicy?: {
    maxRetries: number;
    backoffStrategy: 'linear' | 'exponential';
    backoffDelay: number;
  };
  errorHandling?: 'stop' | 'continue' | 'retry';
  parallelism?: number;
}

export interface WorkflowExecution {
  id: string;
  workflowId: string;
  sessionId?: string;
  organizationId: string;
  userId: string;
  status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled' | 'waiting_approval';
  input?: any;
  output?: any;
  context?: any;
  error?: string;
  startedAt: string;
  completedAt?: string;
  executionTime?: number;
  stepResults: WorkflowStepResult[];
  metadata: Record<string, any>;
}

export interface WorkflowStepResult {
  nodeId: string;
  nodeName: string;
  nodeType: string;
  status: 'pending' | 'running' | 'completed' | 'failed' | 'skipped';
  input?: any;
  output?: any;
  error?: string;
  startTime: number;
  endTime?: number;
  executionTime?: number;
}

export interface CreateWorkflowRequest {
  name: string;
  description?: string;
  nodes: WorkflowNode[];
  connections: WorkflowConnection[];
  status?: 'draft' | 'active' | 'paused' | 'archived';
  tags?: string[];
  metadata?: Record<string, any>;
  settings?: WorkflowSettings;
}

export interface UpdateWorkflowRequest extends Partial<CreateWorkflowRequest> {}

export interface ExecuteWorkflowRequest {
  input?: any;
  sessionId?: string;
  context?: any;
  overrides?: {
    timeout?: number;
    skipValidation?: boolean;
    dryRun?: boolean;
  };
}

export interface CloneWorkflowRequest {
  name: string;
  description?: string;
  includeExecutions?: boolean;
}

export interface WorkflowQueryOptions extends QueryParams {
  status?: 'draft' | 'active' | 'paused' | 'archived';
  tags?: string[];
}

export interface WorkflowExecutionQueryOptions extends QueryParams {
  status?: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled' | 'waiting_approval';
  workflowId?: string;
}

export interface WorkflowStats {
  totalWorkflows: number;
  activeWorkflows: number;
  totalExecutions: number;
  runningExecutions: number;
  recentExecutions: WorkflowExecution[];
}

// Event types for real-time subscriptions
export type EventType = 
  | 'agent.created' | 'agent.updated' | 'agent.executed' | 'agent.deleted'
  | 'tool.created' | 'tool.updated' | 'tool.executed' | 'tool.deleted'
  | 'workflow.created' | 'workflow.updated' | 'workflow.executed' | 'workflow.deleted'
  | 'workflow_execution_started' | 'workflow_execution_completed' | 'workflow_execution_failed'
  | 'workflow_step_completed' | 'workflow_execution_cancelled'
  | 'provider.created' | 'provider.updated' | 'provider.executed' | 'provider.deleted'
  | 'session.created' | 'session.updated' | 'session.message_added' | 'session.completed'
  | 'billing.usage_updated' | 'billing.quota_exceeded' | 'billing.payment_processed'
  | 'system.health_check' | 'system.maintenance' | 'system.alert';

export interface EventSubscription {
  eventTypes: EventType[];
  organizationId?: string;
  userId?: string;
  sessionId?: string;
  callback: (event: WebSocketEvent) => void;
}

// Utility types
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

export type RequireAtLeastOne<T, Keys extends keyof T = keyof T> =
  Pick<T, Exclude<keyof T, Keys>> & 
  { [K in Keys]-?: Required<Pick<T, K>> & Partial<Pick<T, Exclude<Keys, K>>> }[Keys];

export type Timestamps = {
  createdAt: string;
  updatedAt: string;
};

export type WithId<T> = T & { id: string };
export type WithTimestamps<T> = T & Timestamps;
export type Resource<T> = WithId<WithTimestamps<T>>;