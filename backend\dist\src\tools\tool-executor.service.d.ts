import { PrismaService } from '../prisma/prisma.service';
import { APIMXService } from '../apix/apix.service';
import { SessionsService } from '../sessions/sessions.service';
import { ToolsService } from './tools.service';
import { ExecuteToolDto, TestToolDto } from './dto/tool.dto';
import { ConfigService } from '@nestjs/config';
export interface ToolExecutionResult {
    success: boolean;
    output: any;
    executionTime: number;
    sessionId?: string;
    error?: string;
    metadata?: {
        httpStatus?: number;
        headers?: Record<string, string>;
        retryCount?: number;
    };
}
export declare class ToolExecutorService {
    private prisma;
    private apixService;
    private sessionsService;
    private toolsService;
    private configService;
    constructor(prisma: PrismaService, apixService: APIMXService, sessionsService: SessionsService, toolsService: ToolsService, configService: ConfigService);
    executeTool(toolId: string, organizationId: string, userId: string, executeDto: ExecuteToolDto): Promise<ToolExecutionResult>;
    testTool(toolId: string, organizationId: string, testDto: TestToolDto): Promise<ToolExecutionResult>;
    private executeApiTool;
    private executeFunctionTool;
    private executeWorkflowTool;
    private executeWorkflowStep;
    private executeConditionStep;
    private evaluateCondition;
    private interpolateUrl;
    private addAuthentication;
    private validateInput;
    private trackUsage;
    private calculateCost;
    getExecutionHistory(toolId: string, organizationId: string, limit?: number): Promise<any>;
    getExecutionMetrics(toolId: string, organizationId: string): Promise<{
        totalExecutions: any;
        avgExecutionTime: any;
        successRate: number;
        lastExecutedAt: any;
    }>;
}
