generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Organization {
  id        String   @id @default(uuid())
  name      String
  slug      String   @unique
  settings  J<PERSON>     @default("{}")
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  users        User[]
  roles        Role[]
  agents       Agent[]
  tools        Tool[]
  templates    Template[]
  sessions     Session[]
  providers    Provider[]
  hitlRequests HITLRequest[]
  documents    Document[]
  widgets      Widget[]
  quotas       Quota[]
  usage        BillingUsage[]
  notifications Notification[]
  workflows    Workflow[]
  workflowExecutions WorkflowExecution[]

  @@map("organizations")
}

model User {
  id             String   @id @default(uuid())
  email          String   @unique
  name           String
  passwordHash   String?
  avatarUrl      String?
  organizationId String
  settings       Json     @default("{}")
  lastLoginAt    DateTime?
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  organization Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  userRoles    UserRole[]
  sessions     Session[]
  hitlRequests HITLRequest[]
  notifications Notification[]

  @@index([organizationId])
  @@map("users")
}

model Role {
  id             String   @id @default(uuid())
  name           String
  description    String?
  permissions    Json     @default("[]")
  organizationId String
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  organization Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  userRoles    UserRole[]

  @@unique([name, organizationId])
  @@index([organizationId])
  @@map("roles")
}

model UserRole {
  id     String @id @default(uuid())
  userId String
  roleId String

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
  role Role @relation(fields: [roleId], references: [id], onDelete: Cascade)

  @@unique([userId, roleId])
  @@map("user_roles")
}

model Session {
  id             String   @id @default(uuid())
  userId         String
  organizationId String
  type           String
  metadata       Json     @default("{}")
  memory         Json     @default("{}")
  status         String   @default("active")
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  user         User         @relation(fields: [userId], references: [id], onDelete: Cascade)
  organization Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  hitlRequests HITLRequest[]

  @@index([userId])
  @@index([organizationId])
  @@index([status])
  @@map("sessions")
}

model Agent {
  id             String   @id @default(uuid())
  name           String
  description    String
  organizationId String
  templateId     String?
  config         Json
  status         String   @default("draft")
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  organization Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  template     Template?    @relation(fields: [templateId], references: [id])

  @@index([organizationId])
  @@index([status])
  @@map("agents")
}

model Template {
  id             String   @id @default(uuid())
  name           String
  description    String
  category       String
  template       Json
  isPublic       Boolean  @default(false)
  organizationId String?
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  organization Organization? @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  agents       Agent[]

  @@index([organizationId])
  @@index([category])
  @@index([isPublic])
  @@map("templates")
}

model Tool {
  id             String   @id @default(uuid())
  name           String
  description    String
  organizationId String
  type           String
  config         Json
  schema         Json
  status         String   @default("draft")
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  organization Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)

  @@index([organizationId])
  @@index([type])
  @@index([status])
  @@map("tools")
}

model Provider {
  id             String   @id @default(uuid())
  name           String
  type           String
  organizationId String
  config         Json
  status         String   @default("active")
  metrics        Json     @default("{}")
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  organization Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)

  @@index([organizationId])
  @@index([type])
  @@index([status])
  @@map("providers")
}

model HITLRequest {
  id             String    @id @default(uuid())
  organizationId String
  sessionId      String
  userId         String?
  assigneeId     String?
  type           String
  title          String
  description    String
  payload        Json
  status         String    @default("pending")
  priority       String    @default("medium")
  createdAt      DateTime  @default(now())
  resolvedAt     DateTime?

  organization Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  session      Session      @relation(fields: [sessionId], references: [id], onDelete: Cascade)
  user         User?        @relation(fields: [userId], references: [id])

  @@index([organizationId])
  @@index([sessionId])
  @@index([status])
  @@index([priority])
  @@map("hitl_requests")
}

model Document {
  id             String   @id @default(uuid())
  organizationId String
  name           String
  type           String
  size           Int
  url            String
  content        String?
  embedding      Float[]
  metadata       Json     @default("{}")
  status         String   @default("processing")
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  organization Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)

  @@index([organizationId])
  @@index([type])
  @@index([status])
  @@map("documents")
}

model Widget {
  id             String   @id @default(uuid())
  organizationId String
  name           String
  type           String
  targetId       String
  config         Json
  embedCode      String
  status         String   @default("draft")
  analytics      Json     @default("{}")
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  organization Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)

  @@index([organizationId])
  @@index([type])
  @@index([status])
  @@map("widgets")
}

model Quota {
  id             String   @id @default(uuid())
  organizationId String
  type           String
  limit          Int
  used           Int      @default(0)
  resetAt        DateTime
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  organization Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)

  @@unique([organizationId, type])
  @@index([organizationId])
  @@index([type])
  @@map("quotas")
}

model BillingUsage {
  id             String   @id @default(uuid())
  organizationId String
  type           String
  quantity       Float
  cost           Float
  metadata       Json     @default("{}")
  timestamp      DateTime @default(now())

  organization Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)

  @@index([organizationId])
  @@index([type])
  @@index([timestamp])
  @@map("billing_usage")
}

model Notification {
  id             String    @id @default(uuid())
  organizationId String
  userId         String?
  type           String
  channel        String
  title          String
  content        String
  status         String    @default("pending")
  metadata       Json      @default("{}")
  createdAt      DateTime  @default(now())
  sentAt         DateTime?

  organization Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  user         User?        @relation(fields: [userId], references: [id])

  @@index([organizationId])
  @@index([userId])
  @@index([status])
  @@index([channel])
  @@map("notifications")
}

model Workflow {
  id             String   @id @default(uuid())
  name           String
  description    String?
  organizationId String
  createdBy      String
  nodes          Json     @default("[]")
  connections    Json     @default("[]")
  status         String   @default("draft")
  tags           String[] @default([])
  metadata       Json     @default("{}")
  settings       Json     @default("{}")
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  organization Organization       @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  executions   WorkflowExecution[]

  @@index([organizationId])
  @@index([status])
  @@index([createdBy])
  @@map("workflows")
}

model WorkflowExecution {
  id           String    @id @default(uuid())
  workflowId   String
  sessionId    String?
  organizationId String
  userId       String
  status       String    @default("pending")
  input        Json?
  output       Json?
  context      Json?
  error        String?
  startedAt    DateTime  @default(now())
  completedAt  DateTime?
  executionTime Int?
  stepResults  Json      @default("[]")
  metadata     Json      @default("{}")

  workflow     Workflow     @relation(fields: [workflowId], references: [id], onDelete: Cascade)
  organization Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)

  @@index([workflowId])
  @@index([organizationId])
  @@index([userId])
  @@index([status])
  @@index([startedAt])
  @@map("workflow_executions")
}

model APIXEvent {
  id             String   @id @default(uuid())
  type           String
  organizationId String
  userId         String?
  sessionId      String?
  payload        Json
  timestamp      DateTime @default(now())

  @@index([organizationId])
  @@index([type])
  @@index([timestamp])
  @@map("apix_events")
}