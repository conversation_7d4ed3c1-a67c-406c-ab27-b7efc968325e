import { IsString, Is<PERSON>ptional, <PERSON><PERSON><PERSON>, IsObject, <PERSON><PERSON><PERSON>ber, Min, Max, IsUUID, IsArray, IsBoolean } from 'class-validator';
import { Type } from 'class-transformer';

export enum AgentStatus {
  DRAFT = 'draft',
  ACTIVE = 'active',
  ARCHIVED = 'archived',
}

export enum ExecutionStrategy {
  ROLLING = 'rolling',
  SUMMARIZE = 'summarize',
  VECTOR = 'vector',
}

export class AgentConfigDto {
  @IsString()
  prompt: string;

  @IsString()
  model: string;

  @IsNumber()
  @Min(0)
  @Max(2)
  temperature: number = 0.7;

  @IsNumber()
  @Min(1)
  @Max(32000)
  maxTokens: number = 4000;

  @IsOptional()
  @IsString()
  systemPrompt?: string;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tools?: string[];

  @IsOptional()
  @IsObject()
  memory?: {
    enabled: boolean;
    maxMessages: number;
    strategy: ExecutionStrategy;
  };

  @IsOptional()
  @IsObject()
  parameters?: Record<string, any>;
}

export class CreateAgentDto {
  @IsString()
  name: string;

  @IsString()
  description: string;

  @IsOptional()
  @IsString()
  @IsUUID()
  templateId?: string;

  @IsObject()
  @Type(() => AgentConfigDto)
  config: AgentConfigDto;

  @IsOptional()
  @IsEnum(AgentStatus)
  status?: AgentStatus = AgentStatus.DRAFT;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];
}

export class UpdateAgentDto {
  @IsOptional()
  @IsString()
  name?: string;

  @IsOptional()
  @IsString()
  description?: string;

  @IsOptional()
  @IsObject()
  @Type(() => AgentConfigDto)
  config?: Partial<AgentConfigDto>;

  @IsOptional()
  @IsEnum(AgentStatus)
  status?: AgentStatus;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];
}

export class AgentQueryDto {
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  page?: number = 1;

  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  @Max(100)
  limit?: number = 20;

  @IsOptional()
  @IsEnum(AgentStatus)
  status?: AgentStatus;

  @IsOptional()
  @IsString()
  search?: string;

  @IsOptional()
  @IsString()
  model?: string;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];

  @IsOptional()
  @IsString()
  sortBy?: string = 'createdAt';

  @IsOptional()
  @IsEnum(['asc', 'desc'])
  sortOrder?: 'asc' | 'desc' = 'desc';
}

export class ExecuteAgentDto {
  @IsString()
  input: string;

  @IsOptional()
  @IsString()
  @IsUUID()
  sessionId?: string;

  @IsOptional()
  @IsObject()
  context?: Record<string, any>;

  @IsOptional()
  @IsBoolean()
  streaming?: boolean = false;

  @IsOptional()
  @IsObject()
  overrides?: {
    temperature?: number;
    maxTokens?: number;
    model?: string;
  };
}

export class AgentVersionDto {
  @IsString()
  version: string;

  @IsString()
  description: string;

  @IsObject()
  config: AgentConfigDto;

  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;
}

export class CloneAgentDto {
  @IsString()
  name: string;

  @IsOptional()
  @IsString()
  description?: string;

  @IsOptional()
  @IsBoolean()
  includeVersionHistory?: boolean = false;
}