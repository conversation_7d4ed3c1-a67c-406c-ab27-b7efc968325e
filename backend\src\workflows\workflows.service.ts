import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { APIMXService } from '../apix/apix.service';
import { 
  CreateWorkflowDto, 
  UpdateWorkflowDto, 
  WorkflowQueryDto, 
  CloneWorkflowDto,
  WorkflowStatus,
  WorkflowNodeType 
} from './dto/workflow.dto';

@Injectable()
export class WorkflowsService {
  constructor(
    private prisma: PrismaService,
    private apixService: APIMXService,
  ) {}

  async create(
    organizationId: string,
    userId: string,
    createDto: CreateWorkflowDto,
  ) {
    // Validate workflow structure
    await this.validateWorkflow(createDto);

    const workflow = await this.prisma.workflow.create({
      data: {
        name: createDto.name,
        description: createDto.description,
        organizationId,
        createdBy: userId,
        nodes: createDto.nodes,
        connections: createDto.connections,
        status: createDto.status || WorkflowStatus.DRAFT,
        tags: createDto.tags || [],
        metadata: {
          ...createDto.metadata,
          version: '1.0.0',
          createdAt: new Date(),
          lastModified: new Date(),
        },
        settings: createDto.settings || {
          timeout: 300000, // 5 minutes default
          retryPolicy: {
            maxRetries: 3,
            backoffStrategy: 'exponential',
            backoffDelay: 1000,
          },
          errorHandling: 'stop',
          parallelism: 5,
        },
      },
    });

    // Emit creation event
    await this.apixService.broadcastToOrganization(organizationId, {
      type: 'workflow_created',
      payload: { workflowId: workflow.id, name: workflow.name },
      timestamp: new Date(),
      userId,
      organizationId,
    });

    // Track billing
    await this.trackUsage(organizationId, 'workflow_created', { workflowId: workflow.id });

    return workflow;
  }

  async findAll(organizationId: string, query: WorkflowQueryDto) {
    const { search, status, tags, page, limit, sortBy, sortOrder } = query;
    const skip = (page - 1) * limit;

    const where: any = { organizationId };

    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } },
      ];
    }

    if (status) {
      where.status = status;
    }

    if (tags && tags.length > 0) {
      where.tags = {
        hasSome: tags,
      };
    }

    const [workflows, total] = await Promise.all([
      this.prisma.workflow.findMany({
        where,
        skip,
        take: limit,
        orderBy: { [sortBy]: sortOrder },
        include: {
          executions: {
            take: 1,
            orderBy: { createdAt: 'desc' },
            select: {
              id: true,
              status: true,
              startedAt: true,
              completedAt: true,
            },
          },
          _count: {
            select: {
              executions: true,
            },
          },
        },
      }),
      this.prisma.workflow.count({ where }),
    ]);

    return {
      workflows: workflows.map(workflow => ({
        ...workflow,
        lastExecution: workflow.executions[0] || null,
        executionCount: workflow._count.executions,
      })),
      pagination: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  async findOne(id: string, organizationId: string) {
    const workflow = await this.prisma.workflow.findFirst({
      where: { id, organizationId },
      include: {
        executions: {
          take: 10,
          orderBy: { createdAt: 'desc' },
        },
        _count: {
          select: {
            executions: true,
          },
        },
      },
    });

    if (!workflow) {
      throw new NotFoundException('Workflow not found');
    }

    return {
      ...workflow,
      executionCount: workflow._count.executions,
    };
  }

  async update(
    id: string,
    organizationId: string,
    userId: string,
    updateDto: UpdateWorkflowDto,
  ) {
    const existingWorkflow = await this.findOne(id, organizationId);

    // Validate updated workflow structure if nodes/connections changed
    if (updateDto.nodes || updateDto.connections) {
      const workflowToValidate = {
        ...existingWorkflow,
        ...updateDto,
        nodes: updateDto.nodes || existingWorkflow.nodes,
        connections: updateDto.connections || existingWorkflow.connections,
      };
      await this.validateWorkflow(workflowToValidate);
    }

    const workflow = await this.prisma.workflow.update({
      where: { id },
      data: {
        ...updateDto,
        metadata: {
          ...existingWorkflow.metadata,
          ...updateDto.metadata,
          lastModified: new Date(),
          modifiedBy: userId,
        },
      },
    });

    // Emit update event
    await this.apixService.broadcastToOrganization(organizationId, {
      type: 'workflow_updated',
      payload: { workflowId: id, name: workflow.name },
      timestamp: new Date(),
      userId,
      organizationId,
    });

    return workflow;
  }

  async remove(id: string, organizationId: string, userId: string) {
    const workflow = await this.findOne(id, organizationId);

    // Check if workflow has running executions
    const runningExecutions = await this.prisma.workflowExecution.count({
      where: {
        workflowId: id,
        status: { in: ['pending', 'running', 'waiting_approval'] },
      },
    });

    if (runningExecutions > 0) {
      throw new BadRequestException('Cannot delete workflow with running executions');
    }

    await this.prisma.workflow.delete({
      where: { id },
    });

    // Emit deletion event
    await this.apixService.broadcastToOrganization(organizationId, {
      type: 'workflow_deleted',
      payload: { workflowId: id, name: workflow.name },
      timestamp: new Date(),
      userId,
      organizationId,
    });

    return { success: true };
  }

  async clone(
    id: string,
    organizationId: string,
    userId: string,
    cloneDto: CloneWorkflowDto,
  ) {
    const originalWorkflow = await this.findOne(id, organizationId);

    const clonedWorkflow = await this.prisma.workflow.create({
      data: {
        name: cloneDto.name,
        description: cloneDto.description || `Cloned from ${originalWorkflow.name}`,
        organizationId,
        createdBy: userId,
        nodes: originalWorkflow.nodes,
        connections: originalWorkflow.connections,
        status: WorkflowStatus.DRAFT,
        tags: originalWorkflow.tags,
        metadata: {
          ...originalWorkflow.metadata,
          version: '1.0.0',
          clonedFrom: originalWorkflow.id,
          createdAt: new Date(),
          lastModified: new Date(),
        },
        settings: originalWorkflow.settings,
      },
    });

    // Emit clone event
    await this.apixService.broadcastToOrganization(organizationId, {
      type: 'workflow_cloned',
      payload: { 
        originalId: id, 
        clonedId: clonedWorkflow.id, 
        name: clonedWorkflow.name 
      },
      timestamp: new Date(),
      userId,
      organizationId,
    });

    return clonedWorkflow;
  }

  async getStats(organizationId: string) {
    const [
      totalWorkflows,
      activeWorkflows,
      totalExecutions,
      runningExecutions,
      recentExecutions,
    ] = await Promise.all([
      this.prisma.workflow.count({ where: { organizationId } }),
      this.prisma.workflow.count({ 
        where: { organizationId, status: WorkflowStatus.ACTIVE } 
      }),
      this.prisma.workflowExecution.count({
        where: { workflow: { organizationId } },
      }),
      this.prisma.workflowExecution.count({
        where: {
          workflow: { organizationId },
          status: { in: ['pending', 'running', 'waiting_approval'] },
        },
      }),
      this.prisma.workflowExecution.findMany({
        where: { workflow: { organizationId } },
        take: 10,
        orderBy: { createdAt: 'desc' },
        include: {
          workflow: {
            select: { id: true, name: true },
          },
        },
      }),
    ]);

    return {
      totalWorkflows,
      activeWorkflows,
      totalExecutions,
      runningExecutions,
      recentExecutions,
    };
  }

  private async validateWorkflow(workflow: any) {
    const { nodes, connections } = workflow;

    // Validate nodes
    if (!nodes || nodes.length === 0) {
      throw new BadRequestException('Workflow must have at least one node');
    }

    // Check for duplicate node IDs
    const nodeIds = nodes.map((node: any) => node.id);
    if (new Set(nodeIds).size !== nodeIds.length) {
      throw new BadRequestException('Duplicate node IDs found');
    }

    // Validate node types and configurations
    for (const node of nodes) {
      await this.validateNode(node);
    }

    // Validate connections
    for (const connection of connections) {
      const sourceNode = nodes.find((n: any) => n.id === connection.sourceNodeId);
      const targetNode = nodes.find((n: any) => n.id === connection.targetNodeId);

      if (!sourceNode) {
        throw new BadRequestException(`Source node ${connection.sourceNodeId} not found`);
      }

      if (!targetNode) {
        throw new BadRequestException(`Target node ${connection.targetNodeId} not found`);
      }
    }

    // Check for cycles (simplified check)
    this.detectCycles(nodes, connections);
  }

  private async validateNode(node: any) {
    switch (node.type) {
      case WorkflowNodeType.AGENT:
        if (!node.config.agentId) {
          throw new BadRequestException(`Agent node ${node.id} must specify agentId`);
        }
        break;

      case WorkflowNodeType.TOOL:
        if (!node.config.toolId) {
          throw new BadRequestException(`Tool node ${node.id} must specify toolId`);
        }
        break;

      case WorkflowNodeType.CONDITION:
        if (!node.config.condition) {
          throw new BadRequestException(`Condition node ${node.id} must specify condition`);
        }
        break;

      case WorkflowNodeType.HUMAN_APPROVAL:
        if (!node.config.approvers || node.config.approvers.length === 0) {
          throw new BadRequestException(`Human approval node ${node.id} must specify approvers`);
        }
        break;
    }
  }

  private detectCycles(nodes: any[], connections: any[]) {
    const adjacencyList: Record<string, string[]> = {};
    
    // Build adjacency list
    nodes.forEach(node => {
      adjacencyList[node.id] = [];
    });

    connections.forEach(connection => {
      adjacencyList[connection.sourceNodeId].push(connection.targetNodeId);
    });

    // DFS to detect cycles
    const visited = new Set<string>();
    const recursionStack = new Set<string>();

    const hasCycle = (nodeId: string): boolean => {
      visited.add(nodeId);
      recursionStack.add(nodeId);

      for (const neighbor of adjacencyList[nodeId] || []) {
        if (!visited.has(neighbor)) {
          if (hasCycle(neighbor)) {
            return true;
          }
        } else if (recursionStack.has(neighbor)) {
          return true;
        }
      }

      recursionStack.delete(nodeId);
      return false;
    };

    for (const node of nodes) {
      if (!visited.has(node.id)) {
        if (hasCycle(node.id)) {
          throw new BadRequestException('Workflow contains cycles');
        }
      }
    }
  }

  private async trackUsage(organizationId: string, type: string, metadata: any) {
    try {
      await this.prisma.billingUsage.create({
        data: {
          organizationId,
          type,
          quantity: 1,
          cost: 0.01, // Minimal cost for workflow operations
          metadata,
        },
      });
    } catch (error) {
      // Log error but don't fail the operation
      console.error('Failed to track usage:', error);
    }
  }
}