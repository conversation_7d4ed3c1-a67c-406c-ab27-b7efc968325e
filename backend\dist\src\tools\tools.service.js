"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ToolsService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
const apix_service_1 = require("../apix/apix.service");
let ToolsService = class ToolsService {
    constructor(prisma, apixService) {
        this.prisma = prisma;
        this.apixService = apixService;
    }
    async create(userId, organizationId, createToolDto) {
        const { tags = [], metadata = {}, ...toolData } = createToolDto;
        this.validateToolConfig(createToolDto.type, createToolDto.config);
        const tool = await this.prisma.tool.create({
            data: {
                ...toolData,
                organizationId,
                metadata: {
                    ...metadata,
                    tags,
                    createdBy: userId,
                    version: metadata.version || '1.0.0',
                    executions: 0,
                    lastExecuted: null,
                },
            },
        });
        await this.apixService.publishToolEvent('created', tool.id, organizationId, {
            name: tool.name,
            type: tool.type,
            status: tool.status,
            createdBy: userId,
        });
        return tool;
    }
    async findAll(organizationId, query) {
        const { page = 1, limit = 20, type, status, search, category, tags, sortBy = 'createdAt', sortOrder = 'desc', } = query;
        const skip = (page - 1) * limit;
        const where = {
            organizationId,
            ...(type && { type }),
            ...(status && { status }),
        };
        if (search) {
            where.OR = [
                { name: { contains: search, mode: 'insensitive' } },
                { description: { contains: search, mode: 'insensitive' } },
            ];
        }
        if (category) {
            where.metadata = {
                path: ['category'],
                equals: category,
            };
        }
        if (tags && tags.length > 0) {
            where.metadata = {
                ...where.metadata,
                path: ['tags'],
                array_contains: tags,
            };
        }
        const [tools, total] = await Promise.all([
            this.prisma.tool.findMany({
                where,
                orderBy: { [sortBy]: sortOrder },
                skip,
                take: limit,
            }),
            this.prisma.tool.count({ where }),
        ]);
        return {
            tools,
            pagination: {
                page,
                limit,
                total,
                pages: Math.ceil(total / limit),
            },
        };
    }
    async findOne(id, organizationId) {
        const tool = await this.prisma.tool.findUnique({
            where: { id },
        });
        if (!tool) {
            throw new common_1.NotFoundException('Tool not found');
        }
        if (tool.organizationId !== organizationId) {
            throw new common_1.ForbiddenException('Access denied');
        }
        return tool;
    }
    async update(id, organizationId, updateToolDto) {
        const tool = await this.findOne(id, organizationId);
        if (updateToolDto.config) {
            this.validateToolConfig(tool.type, updateToolDto.config);
        }
        const updatedTool = await this.prisma.tool.update({
            where: { id },
            data: {
                ...updateToolDto,
                config: updateToolDto.config ? {
                    ...tool.config,
                    ...updateToolDto.config,
                } : tool.config,
                schema: updateToolDto.schema ? {
                    ...tool.schema,
                    ...updateToolDto.schema,
                } : tool.schema,
                metadata: updateToolDto.metadata ? {
                    ...tool.metadata,
                    ...updateToolDto.metadata,
                    updatedAt: new Date(),
                } : tool.metadata,
                updatedAt: new Date(),
            },
        });
        await this.apixService.publishToolEvent('updated', tool.id, organizationId, {
            changes: updateToolDto,
        });
        return updatedTool;
    }
    async remove(id, organizationId) {
        const tool = await this.findOne(id, organizationId);
        const activeSessions = await this.prisma.session.count({
            where: {
                organizationId,
                type: 'tool',
                metadata: {
                    path: ['targetId'],
                    equals: id,
                },
                status: 'active',
            },
        });
        if (activeSessions > 0) {
            throw new common_1.BadRequestException('Cannot delete tool with active sessions');
        }
        await this.prisma.tool.delete({
            where: { id },
        });
        await this.apixService.publishToolEvent('deleted', tool.id, organizationId, {});
        return { message: 'Tool deleted successfully' };
    }
    async createVersion(id, organizationId, versionDto) {
        const tool = await this.findOne(id, organizationId);
        const currentMetadata = tool.metadata;
        const versions = currentMetadata.versions || [];
        versions.push({
            version: versionDto.version,
            description: versionDto.description,
            config: versionDto.config,
            schema: versionDto.schema,
            createdAt: new Date(),
            metadata: versionDto.metadata,
        });
        const updatedTool = await this.prisma.tool.update({
            where: { id },
            data: {
                config: versionDto.config,
                schema: versionDto.schema,
                metadata: {
                    ...currentMetadata,
                    versions,
                    currentVersion: versionDto.version,
                },
                updatedAt: new Date(),
            },
        });
        await this.apixService.publishToolEvent('version_created', tool.id, organizationId, {
            version: versionDto.version,
        });
        return updatedTool;
    }
    async rollbackToVersion(id, organizationId, version) {
        const tool = await this.findOne(id, organizationId);
        const metadata = tool.metadata;
        const versions = metadata.versions || [];
        const targetVersion = versions.find((v) => v.version === version);
        if (!targetVersion) {
            throw new common_1.NotFoundException('Version not found');
        }
        const updatedTool = await this.prisma.tool.update({
            where: { id },
            data: {
                config: targetVersion.config,
                schema: targetVersion.schema,
                metadata: {
                    ...metadata,
                    currentVersion: version,
                    rolledBackAt: new Date(),
                },
                updatedAt: new Date(),
            },
        });
        await this.apixService.publishToolEvent('version_rollback', tool.id, organizationId, {
            version,
            previousVersion: metadata.currentVersion,
        });
        return updatedTool;
    }
    async clone(id, organizationId, cloneData) {
        const originalTool = await this.findOne(id, organizationId);
        const clonedTool = await this.prisma.tool.create({
            data: {
                name: cloneData.name,
                description: cloneData.description || `Clone of ${originalTool.name}`,
                organizationId,
                type: originalTool.type,
                config: originalTool.config,
                schema: originalTool.schema,
                status: 'draft',
                metadata: {
                    ...originalTool.metadata,
                    clonedFrom: originalTool.id,
                    clonedAt: new Date(),
                    executions: 0,
                    lastExecuted: null,
                },
            },
        });
        await this.apixService.publishToolEvent('cloned', clonedTool.id, organizationId, {
            originalId: originalTool.id,
            originalName: originalTool.name,
        });
        return clonedTool;
    }
    async getToolStats(organizationId) {
        const [total, byType, byStatus, executions] = await Promise.all([
            this.prisma.tool.count({ where: { organizationId } }),
            this.prisma.tool.groupBy({
                by: ['type'],
                where: { organizationId },
                _count: true,
            }),
            this.prisma.tool.groupBy({
                by: ['status'],
                where: { organizationId },
                _count: true,
            }),
            this.prisma.session.count({
                where: {
                    organizationId,
                    type: 'tool',
                },
            }),
        ]);
        return {
            total,
            executions,
            byType: byType.reduce((acc, item) => {
                acc[item.type] = item._count;
                return acc;
            }, {}),
            byStatus: byStatus.reduce((acc, item) => {
                acc[item.status] = item._count;
                return acc;
            }, {}),
        };
    }
    async getPopularTools(organizationId, limit = 10) {
        const tools = await this.prisma.tool.findMany({
            where: { organizationId },
            take: limit,
        });
        return tools
            .map(tool => ({
            ...tool,
            executions: tool.metadata?.executions || 0,
        }))
            .sort((a, b) => b.executions - a.executions);
    }
    async searchToolsByTags(organizationId, tags) {
        return this.prisma.tool.findMany({
            where: {
                organizationId,
                metadata: {
                    path: ['tags'],
                    array_contains: tags,
                },
            },
            select: {
                id: true,
                name: true,
                description: true,
                type: true,
                status: true,
                metadata: true,
            },
        });
    }
    async getToolsByCategory(organizationId, category) {
        return this.prisma.tool.findMany({
            where: {
                organizationId,
                metadata: {
                    path: ['category'],
                    equals: category,
                },
            },
            orderBy: { metadata: { path: ['executions'], sort: 'desc' } },
        });
    }
    validateToolConfig(type, config) {
        switch (type) {
            case 'api':
                if (!config.endpoint) {
                    throw new common_1.BadRequestException('API tools require an endpoint');
                }
                if (!config.method) {
                    throw new common_1.BadRequestException('API tools require an HTTP method');
                }
                break;
            case 'function':
                if (!config.functionCode) {
                    throw new common_1.BadRequestException('Function tools require function code');
                }
                break;
            case 'workflow':
                if (!config.workflowSteps || !Array.isArray(config.workflowSteps)) {
                    throw new common_1.BadRequestException('Workflow tools require workflow steps');
                }
                break;
            default:
                throw new common_1.BadRequestException('Invalid tool type');
        }
    }
    async incrementExecutionCount(toolId) {
        const tool = await this.prisma.tool.findUnique({
            where: { id: toolId },
        });
        if (tool) {
            const metadata = tool.metadata;
            await this.prisma.tool.update({
                where: { id: toolId },
                data: {
                    metadata: {
                        ...metadata,
                        executions: (metadata.executions || 0) + 1,
                        lastExecuted: new Date(),
                    },
                },
            });
        }
    }
};
exports.ToolsService = ToolsService;
exports.ToolsService = ToolsService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService,
        apix_service_1.APIMXService])
], ToolsService);
//# sourceMappingURL=tools.service.js.map