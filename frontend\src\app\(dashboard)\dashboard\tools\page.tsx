'use client';

import { useState, useEffect } from 'react';
import { useOrganization } from '@/lib/organization-context';
import { Plus, Search, Filter, Play, Edit, Trash, Copy, History, BarChart3 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { toolsApi } from '@/lib/api/tools';
import { ToolBuilder } from '@/components/tools/tool-builder';
import { ToolExecutor } from '@/components/tools/tool-executor';
import { ToolMetrics } from '@/components/tools/tool-metrics';
import { Tool, ToolType, ToolStatus } from '@/types/tool';

const ToolTypeColors = {
  api: 'bg-blue-100 text-blue-800',
  function: 'bg-green-100 text-green-800',
  workflow: 'bg-purple-100 text-purple-800',
};

const ToolStatusColors = {
  draft: 'bg-gray-100 text-gray-800',
  active: 'bg-green-100 text-green-800',
  archived: 'bg-red-100 text-red-800',
};

export default function ToolsPage() {
  const { currentOrganization } = useOrganization();
  const [tools, setTools] = useState<Tool[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedTool, setSelectedTool] = useState<Tool | null>(null);
  const [isBuilderOpen, setIsBuilderOpen] = useState(false);
  const [isExecutorOpen, setIsExecutorOpen] = useState(false);
  const [isMetricsOpen, setIsMetricsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [typeFilter, setTypeFilter] = useState<ToolType | 'all'>('all');
  const [statusFilter, setStatusFilter] = useState<ToolStatus | 'all'>('all');
  const [stats, setStats] = useState<any>(null);

  useEffect(() => {
    if (currentOrganization) {
      loadTools();
      loadStats();
    }
  }, [currentOrganization, typeFilter, statusFilter, searchTerm]);

  const loadTools = async () => {
    try {
      setLoading(true);
      const params: any = {};
      if (typeFilter !== 'all') params.type = typeFilter;
      if (statusFilter !== 'all') params.status = statusFilter;
      if (searchTerm) params.search = searchTerm;

      const result = await toolsApi.getAll(params);
      setTools(result.tools);
    } catch (error) {
      console.error('Failed to load tools:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadStats = async () => {
    try {
      const result = await toolsApi.getStats();
      setStats(result);
    } catch (error) {
      console.error('Failed to load stats:', error);
    }
  };

  const handleExecuteTool = (tool: Tool) => {
    setSelectedTool(tool);
    setIsExecutorOpen(true);
  };

  const handleEditTool = (tool: Tool) => {
    setSelectedTool(tool);
    setIsBuilderOpen(true);
  };

  const handleCloneTool = async (tool: Tool) => {
    try {
      await toolsApi.clone(tool.id, {
        name: `${tool.name} (Copy)`,
        description: `Clone of ${tool.name}`,
      });
      loadTools();
    } catch (error) {
      console.error('Failed to clone tool:', error);
    }
  };

  const handleDeleteTool = async (tool: Tool) => {
    if (confirm('Are you sure you want to delete this tool?')) {
      try {
        await toolsApi.delete(tool.id);
        loadTools();
      } catch (error) {
        console.error('Failed to delete tool:', error);
      }
    }
  };

  const handleViewMetrics = (tool: Tool) => {
    setSelectedTool(tool);
    setIsMetricsOpen(true);
  };

  const filteredTools = tools.filter(tool => {
    const matchesSearch = !searchTerm || 
      tool.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      tool.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesType = typeFilter === 'all' || tool.type === typeFilter;
    const matchesStatus = statusFilter === 'all' || tool.status === statusFilter;
    return matchesSearch && matchesType && matchesStatus;
  });

  if (!currentOrganization) {
    return <div>Please select an organization</div>;
  }

  return (
    <div className="container mx-auto py-6">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Tools</h1>
          <p className="text-muted-foreground">
            Build and manage API tools, functions, and workflows
          </p>
        </div>
        <Button
          onClick={() => {
            setSelectedTool(null);
            setIsBuilderOpen(true);
          }}
          className="gap-2"
        >
          <Plus className="h-4 w-4" />
          Create Tool
        </Button>
      </div>

      {/* Stats Cards */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Tools</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.total}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Executions</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.executions}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">API Tools</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.byType?.api || 0}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Active</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.byStatus?.active || 0}</div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Filters */}
      <div className="flex flex-col sm:flex-row gap-4 mb-6">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <Input
            placeholder="Search tools..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
        <Select value={typeFilter} onValueChange={(value: any) => setTypeFilter(value)}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Filter by type" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Types</SelectItem>
            <SelectItem value="api">API Tools</SelectItem>
            <SelectItem value="function">Functions</SelectItem>
            <SelectItem value="workflow">Workflows</SelectItem>
          </SelectContent>
        </Select>
        <Select value={statusFilter} onValueChange={(value: any) => setStatusFilter(value)}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Filter by status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Status</SelectItem>
            <SelectItem value="draft">Draft</SelectItem>
            <SelectItem value="active">Active</SelectItem>
            <SelectItem value="archived">Archived</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Tools Grid */}
      {loading ? (
        <div className="text-center py-12">Loading tools...</div>
      ) : filteredTools.length === 0 ? (
        <Card>
          <CardContent className="text-center py-12">
            <h3 className="text-lg font-semibold mb-2">No tools found</h3>
            <p className="text-muted-foreground mb-4">
              Get started by creating your first tool
            </p>
            <Button
              onClick={() => {
                setSelectedTool(null);
                setIsBuilderOpen(true);
              }}
              className="gap-2"
            >
              <Plus className="h-4 w-4" />
              Create Tool
            </Button>
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {filteredTools.map((tool) => (
            <Card key={tool.id} className="relative group">
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <CardTitle className="text-lg mb-2">{tool.name}</CardTitle>
                    <CardDescription className="line-clamp-2">
                      {tool.description}
                    </CardDescription>
                  </div>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="opacity-0 group-hover:opacity-100"
                      >
                        ⋮
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={() => handleExecuteTool(tool)}>
                        <Play className="h-4 w-4 mr-2" />
                        Execute
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => handleEditTool(tool)}>
                        <Edit className="h-4 w-4 mr-2" />
                        Edit
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => handleCloneTool(tool)}>
                        <Copy className="h-4 w-4 mr-2" />
                        Clone
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => handleViewMetrics(tool)}>
                        <BarChart3 className="h-4 w-4 mr-2" />
                        Metrics
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        onClick={() => handleDeleteTool(tool)}
                        className="text-red-600"
                      >
                        <Trash className="h-4 w-4 mr-2" />
                        Delete
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
                <div className="flex gap-2 mt-2">
                  <Badge className={ToolTypeColors[tool.type]}>
                    {tool.type}
                  </Badge>
                  <Badge className={ToolStatusColors[tool.status]}>
                    {tool.status}
                  </Badge>
                </div>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-between text-sm text-muted-foreground">
                  <span>
                    {(tool.metadata as any)?.executions || 0} executions
                  </span>
                  <span>
                    {tool.updatedAt && new Date(tool.updatedAt).toLocaleDateString()}
                  </span>
                </div>
                <div className="mt-3 flex gap-2">
                  <Button
                    size="sm"
                    onClick={() => handleExecuteTool(tool)}
                    disabled={tool.status !== 'active'}
                    className="flex-1"
                  >
                    <Play className="h-3 w-3 mr-1" />
                    Execute
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => handleEditTool(tool)}
                    className="flex-1"
                  >
                    <Edit className="h-3 w-3 mr-1" />
                    Edit
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Tool Builder Dialog */}
      <Dialog open={isBuilderOpen} onOpenChange={setIsBuilderOpen}>
        <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>
              {selectedTool ? 'Edit Tool' : 'Create New Tool'}
            </DialogTitle>
          </DialogHeader>
          <ToolBuilder
            tool={selectedTool}
            onSave={() => {
              setIsBuilderOpen(false);
              loadTools();
            }}
            onCancel={() => setIsBuilderOpen(false)}
          />
        </DialogContent>
      </Dialog>

      {/* Tool Executor Dialog */}
      <Dialog open={isExecutorOpen} onOpenChange={setIsExecutorOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Execute Tool: {selectedTool?.name}</DialogTitle>
          </DialogHeader>
          {selectedTool && (
            <ToolExecutor
              tool={selectedTool}
              onClose={() => setIsExecutorOpen(false)}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* Tool Metrics Dialog */}
      <Dialog open={isMetricsOpen} onOpenChange={setIsMetricsOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Tool Metrics: {selectedTool?.name}</DialogTitle>
          </DialogHeader>
          {selectedTool && (
            <ToolMetrics
              tool={selectedTool}
              onClose={() => setIsMetricsOpen(false)}
            />
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}