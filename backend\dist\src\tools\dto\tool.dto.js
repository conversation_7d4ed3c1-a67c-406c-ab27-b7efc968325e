"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ToolVersionDto = exports.TestToolDto = exports.ExecuteToolDto = exports.ToolQueryDto = exports.UpdateToolDto = exports.CreateToolDto = exports.ToolSchemaDto = exports.ToolConfigDto = exports.AuthType = exports.HttpMethod = exports.ToolStatus = exports.ToolType = void 0;
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
var ToolType;
(function (ToolType) {
    ToolType["API"] = "api";
    ToolType["FUNCTION"] = "function";
    ToolType["WORKFLOW"] = "workflow";
})(ToolType || (exports.ToolType = ToolType = {}));
var ToolStatus;
(function (ToolStatus) {
    ToolStatus["DRAFT"] = "draft";
    ToolStatus["ACTIVE"] = "active";
    ToolStatus["ARCHIVED"] = "archived";
})(ToolStatus || (exports.ToolStatus = ToolStatus = {}));
var HttpMethod;
(function (HttpMethod) {
    HttpMethod["GET"] = "GET";
    HttpMethod["POST"] = "POST";
    HttpMethod["PUT"] = "PUT";
    HttpMethod["DELETE"] = "DELETE";
    HttpMethod["PATCH"] = "PATCH";
})(HttpMethod || (exports.HttpMethod = HttpMethod = {}));
var AuthType;
(function (AuthType) {
    AuthType["NONE"] = "none";
    AuthType["API_KEY"] = "api_key";
    AuthType["BEARER"] = "bearer";
    AuthType["BASIC"] = "basic";
    AuthType["OAUTH2"] = "oauth2";
})(AuthType || (exports.AuthType = AuthType = {}));
class ToolConfigDto {
    constructor() {
        this.timeout = 30000;
        this.retries = 3;
    }
}
exports.ToolConfigDto = ToolConfigDto;
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUrl)(),
    __metadata("design:type", String)
], ToolConfigDto.prototype, "endpoint", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(HttpMethod),
    __metadata("design:type", String)
], ToolConfigDto.prototype, "method", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], ToolConfigDto.prototype, "headers", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], ToolConfigDto.prototype, "authentication", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1000),
    (0, class_validator_1.Max)(300000),
    __metadata("design:type", Number)
], ToolConfigDto.prototype, "timeout", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    (0, class_validator_1.Max)(10),
    __metadata("design:type", Number)
], ToolConfigDto.prototype, "retries", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], ToolConfigDto.prototype, "parameters", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ToolConfigDto.prototype, "functionCode", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsObject)({ each: true }),
    __metadata("design:type", Array)
], ToolConfigDto.prototype, "workflowSteps", void 0);
class ToolSchemaDto {
}
exports.ToolSchemaDto = ToolSchemaDto;
__decorate([
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], ToolSchemaDto.prototype, "input", void 0);
__decorate([
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], ToolSchemaDto.prototype, "output", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsObject)({ each: true }),
    __metadata("design:type", Array)
], ToolSchemaDto.prototype, "examples", void 0);
class CreateToolDto {
    constructor() {
        this.status = ToolStatus.DRAFT;
    }
}
exports.CreateToolDto = CreateToolDto;
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateToolDto.prototype, "name", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateToolDto.prototype, "description", void 0);
__decorate([
    (0, class_validator_1.IsEnum)(ToolType),
    __metadata("design:type", String)
], CreateToolDto.prototype, "type", void 0);
__decorate([
    (0, class_validator_1.IsObject)(),
    (0, class_transformer_1.Type)(() => ToolConfigDto),
    __metadata("design:type", ToolConfigDto)
], CreateToolDto.prototype, "config", void 0);
__decorate([
    (0, class_validator_1.IsObject)(),
    (0, class_transformer_1.Type)(() => ToolSchemaDto),
    __metadata("design:type", ToolSchemaDto)
], CreateToolDto.prototype, "schema", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(ToolStatus),
    __metadata("design:type", String)
], CreateToolDto.prototype, "status", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], CreateToolDto.prototype, "tags", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], CreateToolDto.prototype, "metadata", void 0);
class UpdateToolDto {
}
exports.UpdateToolDto = UpdateToolDto;
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UpdateToolDto.prototype, "name", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UpdateToolDto.prototype, "description", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)(),
    (0, class_transformer_1.Type)(() => ToolConfigDto),
    __metadata("design:type", Object)
], UpdateToolDto.prototype, "config", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)(),
    (0, class_transformer_1.Type)(() => ToolSchemaDto),
    __metadata("design:type", Object)
], UpdateToolDto.prototype, "schema", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(ToolStatus),
    __metadata("design:type", String)
], UpdateToolDto.prototype, "status", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], UpdateToolDto.prototype, "tags", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], UpdateToolDto.prototype, "metadata", void 0);
class ToolQueryDto {
    constructor() {
        this.page = 1;
        this.limit = 20;
        this.sortBy = 'createdAt';
        this.sortOrder = 'desc';
    }
}
exports.ToolQueryDto = ToolQueryDto;
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    __metadata("design:type", Number)
], ToolQueryDto.prototype, "page", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    (0, class_validator_1.Max)(100),
    __metadata("design:type", Number)
], ToolQueryDto.prototype, "limit", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(ToolType),
    __metadata("design:type", String)
], ToolQueryDto.prototype, "type", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(ToolStatus),
    __metadata("design:type", String)
], ToolQueryDto.prototype, "status", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ToolQueryDto.prototype, "search", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ToolQueryDto.prototype, "category", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], ToolQueryDto.prototype, "tags", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ToolQueryDto.prototype, "sortBy", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(['asc', 'desc']),
    __metadata("design:type", String)
], ToolQueryDto.prototype, "sortOrder", void 0);
class ExecuteToolDto {
    constructor() {
        this.validateInput = true;
    }
}
exports.ExecuteToolDto = ExecuteToolDto;
__decorate([
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], ExecuteToolDto.prototype, "input", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ExecuteToolDto.prototype, "sessionId", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], ExecuteToolDto.prototype, "context", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], ExecuteToolDto.prototype, "validateInput", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], ExecuteToolDto.prototype, "overrides", void 0);
class TestToolDto {
    constructor() {
        this.dryRun = false;
    }
}
exports.TestToolDto = TestToolDto;
__decorate([
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], TestToolDto.prototype, "input", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], TestToolDto.prototype, "dryRun", void 0);
class ToolVersionDto {
}
exports.ToolVersionDto = ToolVersionDto;
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ToolVersionDto.prototype, "version", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ToolVersionDto.prototype, "description", void 0);
__decorate([
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", ToolConfigDto)
], ToolVersionDto.prototype, "config", void 0);
__decorate([
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", ToolSchemaDto)
], ToolVersionDto.prototype, "schema", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], ToolVersionDto.prototype, "metadata", void 0);
//# sourceMappingURL=tool.dto.js.map