import { IsString, IsOptional, IsBoolean, IsObject, IsEnum, <PERSON><PERSON><PERSON>ber, Min, Max, IsArray } from 'class-validator';
import { Type } from 'class-transformer';

export enum TemplateCategory {
  CUSTOMER_SERVICE = 'customer-service',
  CONTENT_CREATION = 'content-creation',
  DATA_ANALYSIS = 'data-analysis',
  AUTOMATION = 'automation',
  EDUCATION = 'education',
  HEALTHCARE = 'healthcare',
  FINANCE = 'finance',
  MARKETING = 'marketing',
  SALES = 'sales',
  RESEARCH = 'research',
  GENERAL = 'general',
}

export class TemplateConfigDto {
  @IsString()
  prompt: string;

  @IsOptional()
  @IsString()
  systemPrompt?: string;

  @IsOptional()
  @IsObject()
  config?: {
    temperature?: number;
    maxTokens?: number;
    model?: string;
    memory?: {
      enabled: boolean;
      maxMessages: number;
      strategy: 'rolling' | 'summarize' | 'vector';
    };
  };

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tools?: string[];

  @IsOptional()
  @IsArray()
  @IsObject({ each: true })
  parameters?: Array<{
    name: string;
    type: 'string' | 'number' | 'boolean' | 'array' | 'object';
    description: string;
    required: boolean;
    defaultValue?: any;
  }>;
}

export class CreateTemplateDto {
  @IsString()
  name: string;

  @IsString()
  description: string;

  @IsEnum(TemplateCategory)
  category: TemplateCategory;

  @IsObject()
  @Type(() => TemplateConfigDto)
  template: TemplateConfigDto;

  @IsOptional()
  @IsBoolean()
  isPublic?: boolean = false;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];

  @IsOptional()
  @IsObject()
  metadata?: {
    version?: string;
    author?: string;
    license?: string;
    website?: string;
    examples?: Array<{
      input: string;
      output: string;
      description: string;
    }>;
  };
}

export class UpdateTemplateDto {
  @IsOptional()
  @IsString()
  name?: string;

  @IsOptional()
  @IsString()
  description?: string;

  @IsOptional()
  @IsEnum(TemplateCategory)
  category?: TemplateCategory;

  @IsOptional()
  @IsObject()
  @Type(() => TemplateConfigDto)
  template?: Partial<TemplateConfigDto>;

  @IsOptional()
  @IsBoolean()
  isPublic?: boolean;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];

  @IsOptional()
  @IsObject()
  metadata?: any;
}

export class TemplateQueryDto {
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  page?: number = 1;

  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  @Max(100)
  limit?: number = 20;

  @IsOptional()
  @IsEnum(TemplateCategory)
  category?: TemplateCategory;

  @IsOptional()
  @IsString()
  search?: string;

  @IsOptional()
  @IsBoolean()
  isPublic?: boolean;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];

  @IsOptional()
  @IsString()
  sortBy?: string = 'createdAt';

  @IsOptional()
  @IsEnum(['asc', 'desc'])
  sortOrder?: 'asc' | 'desc' = 'desc';
}

export class RateTemplateDto {
  @IsNumber()
  @Min(1)
  @Max(5)
  rating: number;

  @IsOptional()
  @IsString()
  review?: string;
}

export class ForkTemplateDto {
  @IsString()
  name: string;

  @IsOptional()
  @IsString()
  description?: string;

  @IsOptional()
  @IsBoolean()
  isPublic?: boolean = false;
}